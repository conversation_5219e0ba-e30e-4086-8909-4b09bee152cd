import * as pdfjsLib from 'pdfjs-dist'

// 配置PDF.js worker
let workerInitialized = false

export const initializePdfWorker = () => {
  if (!workerInitialized) {
    // 尝试多种worker配置方式
    try {
      // 方法1: 使用本地worker文件
      const workerUrl = new URL('pdfjs-dist/build/pdf.worker.min.mjs', import.meta.url)
      pdfjsLib.GlobalWorkerOptions.workerSrc = workerUrl.href
    } catch (error) {
      console.warn('Failed to load local PDF worker, falling back to CDN:', error)
      // 方法2: 使用CDN worker - 使用与安装版本匹配的worker
      pdfjsLib.GlobalWorkerOptions.workerSrc = `https://unpkg.com/pdfjs-dist@5.4.54/build/pdf.worker.min.mjs`
    }
    workerInitialized = true
  }
}

export const loadPdfDocument = async (url) => {
  initializePdfWorker()
  
  try {
    const loadingTask = pdfjsLib.getDocument({
      url: url,
      cMapUrl: 'https://unpkg.com/pdfjs-dist@5.4.54/cmaps/',
      cMapPacked: true,
    })
    
    return await loadingTask.promise
  } catch (error) {
    console.error('PDF loading failed:', error)
    throw new Error('PDF文档加载失败: ' + error.message)
  }
}

export const renderPdfPage = async (pdfDoc, pageNumber, canvas, scale = 1.5) => {
  try {
    const page = await pdfDoc.getPage(pageNumber)
    const viewport = page.getViewport({ scale })
    
    const context = canvas.getContext('2d')
    canvas.height = viewport.height
    canvas.width = viewport.width
    
    const renderContext = {
      canvasContext: context,
      viewport: viewport
    }
    
    const renderTask = page.render(renderContext)
    await renderTask.promise
    
    return renderTask
  } catch (error) {
    console.error('PDF page rendering failed:', error)
    throw new Error('PDF页面渲染失败: ' + error.message)
  }
}

export { pdfjsLib }
