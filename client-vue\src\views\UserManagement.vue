<template>
  <div class="user-management">
    <div class="page-header">
      <div class="header-left">
        <el-button type="info" plain @click="goBack" class="back-button">
          <el-icon><ArrowLeft /></el-icon>
          返回首页
        </el-button>
        <h2>用户管理</h2>
      </div>
      <el-button type="primary" @click="showCreateDialog = true" v-if="authStore.isAdmin">
        <el-icon><Plus /></el-icon>
        创建用户
      </el-button>
    </div>

    <!-- 用户列表 -->
    <el-table :data="users" v-loading="loading" stripe>
      <el-table-column prop="username" label="用户名" width="120" />
      <el-table-column prop="email" label="邮箱" width="200" />
      <el-table-column prop="displayName" label="显示名称" width="120" />
      <el-table-column prop="role" label="角色" width="100">
        <template #default="{ row }">
          <el-tag :type="row.role === 'Admin' ? 'danger' : 'primary'" size="small">
            {{ row.role === 'Admin' ? '管理员' : '普通用户' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="权限" width="200">
        <template #default="{ row }">
          <div class="permissions">
            <el-tag v-if="row.canDeleteFiles" type="warning" size="small">可删除</el-tag>
            <el-tag v-if="row.canDownloadOriginalFiles" type="info" size="small">可下载</el-tag>
            <el-tag v-if="!row.canDeleteFiles && !row.canDownloadOriginalFiles" type="info" size="small">基础权限</el-tag>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="createdAt" label="创建时间" width="180">
        <template #default="{ row }">
          {{ formatDate(row.createdAt) }}
        </template>
      </el-table-column>
      <el-table-column prop="lastLoginAt" label="最后登录" width="180">
        <template #default="{ row }">
          {{ row.lastLoginAt ? formatDate(row.lastLoginAt) : '从未登录' }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200" v-if="authStore.isAdmin">
        <template #default="{ row }">
          <el-button 
            type="primary" 
            size="small" 
            @click="editUser(row)"
            :disabled="row.role === 'Admin'"
          >
            编辑权限
          </el-button>
          <el-button 
            type="danger" 
            size="small" 
            @click="deleteUser(row)"
            :disabled="row.role === 'Admin'"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 创建用户对话框 -->
    <el-dialog v-model="showCreateDialog" title="创建用户" width="500px">
      <el-form ref="createFormRef" :model="createForm" :rules="createRules" label-width="100px">
        <el-form-item label="用户名" prop="username">
          <el-input v-model="createForm.username" placeholder="请输入用户名" />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="createForm.email" placeholder="请输入邮箱" />
        </el-form-item>
        <el-form-item label="显示名称" prop="displayName">
          <el-input v-model="createForm.displayName" placeholder="请输入显示名称" />
        </el-form-item>
        <el-form-item label="密码" prop="password">
          <el-input v-model="createForm.password" type="password" placeholder="请输入密码" />
        </el-form-item>
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input v-model="createForm.confirmPassword" type="password" placeholder="请确认密码" />
        </el-form-item>
        <el-form-item label="角色" prop="role">
          <el-select v-model="createForm.role" placeholder="请选择角色">
            <el-option label="普通用户" value="User" />
            <el-option label="管理员" value="Admin" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="handleCreateUser" :loading="createLoading">创建</el-button>
      </template>
    </el-dialog>

    <!-- 编辑权限对话框 -->
    <el-dialog v-model="showEditDialog" title="编辑用户权限" width="400px">
      <el-form :model="editForm" label-width="120px">
        <el-form-item label="用户名">
          <el-input v-model="editForm.username" disabled />
        </el-form-item>
        <el-form-item label="可删除文件">
          <el-switch v-model="editForm.canDeleteFiles" />
        </el-form-item>
        <el-form-item label="可下载源文件">
          <el-switch v-model="editForm.canDownloadOriginalFiles" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showEditDialog = false">取消</el-button>
        <el-button type="primary" @click="handleUpdatePermissions" :loading="editLoading">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, ArrowLeft } from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/authStore'
import { authApi } from '@/services/api'

const authStore = useAuthStore()
const router = useRouter()

const users = ref([])
const loading = ref(false)
const showCreateDialog = ref(false)
const showEditDialog = ref(false)
const createLoading = ref(false)
const editLoading = ref(false)
const createFormRef = ref()

const createForm = reactive({
  username: '',
  email: '',
  displayName: '',
  password: '',
  confirmPassword: '',
  role: 'User'
})

const editForm = reactive({
  id: '',
  username: '',
  canDeleteFiles: false,
  canDownloadOriginalFiles: false
})

const createRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 50, message: '用户名长度在 3 到 50 个字符', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于 6 个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== createForm.password) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 格式化日期
const formatDate = (dateString) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

// 获取用户列表
const fetchUsers = async () => {
  try {
    loading.value = true
    const response = await authApi.getUsers()
    if (response.success) {
      users.value = response.data
    } else {
      ElMessage.error('获取用户列表失败')
    }
  } catch (error) {
    console.error('获取用户列表失败:', error)
    ElMessage.error('获取用户列表失败')
  } finally {
    loading.value = false
  }
}

// 创建用户
const handleCreateUser = async () => {
  if (!createFormRef.value) return

  try {
    const valid = await createFormRef.value.validate()
    if (!valid) return

    createLoading.value = true
    const response = await authStore.register(createForm)
    
    if (response.success) {
      ElMessage.success('用户创建成功')
      showCreateDialog.value = false
      resetCreateForm()
      await fetchUsers()
    } else {
      ElMessage.error(response.message || '创建用户失败')
    }
  } catch (error) {
    console.error('创建用户失败:', error)
    ElMessage.error('创建用户失败')
  } finally {
    createLoading.value = false
  }
}

// 编辑用户
const editUser = (user) => {
  editForm.id = user.id
  editForm.username = user.username
  editForm.canDeleteFiles = user.canDeleteFiles
  editForm.canDownloadOriginalFiles = user.canDownloadOriginalFiles
  showEditDialog.value = true
}

// 更新用户权限
const handleUpdatePermissions = async () => {
  try {
    editLoading.value = true
    const response = await authApi.updateUserPermissions(editForm.id, {
      canDeleteFiles: editForm.canDeleteFiles,
      canDownloadOriginalFiles: editForm.canDownloadOriginalFiles
    })
    
    if (response.success) {
      ElMessage.success('权限更新成功')
      showEditDialog.value = false
      await fetchUsers()
    } else {
      ElMessage.error(response.message || '权限更新失败')
    }
  } catch (error) {
    console.error('权限更新失败:', error)
    ElMessage.error('权限更新失败')
  } finally {
    editLoading.value = false
  }
}

// 删除用户
const deleteUser = async (user) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除用户 "${user.username}" 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    const response = await authApi.deleteUser(user.id)
    if (response.success) {
      ElMessage.success('用户删除成功')
      await fetchUsers()
    } else {
      ElMessage.error(response.message || '删除用户失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除用户失败:', error)
      ElMessage.error('删除用户失败')
    }
  }
}

// 重置创建表单
const resetCreateForm = () => {
  Object.assign(createForm, {
    username: '',
    email: '',
    displayName: '',
    password: '',
    confirmPassword: '',
    role: 'User'
  })
}

// 返回首页
const goBack = () => {
  router.push('/')
}

onMounted(() => {
  if (authStore.isAdmin) {
    fetchUsers()
  }
})
</script>

<style scoped>
.user-management {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.back-button {
  font-size: 14px;
}

.page-header h2 {
  margin: 0;
  color: #333;
}

.permissions {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.el-table {
  margin-top: 20px;
}
</style>
