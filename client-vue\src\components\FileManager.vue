<template>
  <div class="file-manager" ref="fileManagerRef" :data-compact="isCompact">
    <div class="toolbar">
      <el-button type="primary" @click="showUploadDialog = true" class="toolbar-btn">
        <el-icon><Upload /></el-icon>
        <span class="btn-text">上传文件</span>
      </el-button>
      <el-button @click="showCreateFolderDialog = true" class="toolbar-btn">
        <el-icon><FolderAdd /></el-icon>
        <span class="btn-text">新建文件夹</span>
      </el-button>
    </div>

    <div class="file-tree">
      <el-tree
        ref="treeRef"
        :data="fileTree"
        :props="treeProps"
        node-key="id"
        @node-click="handleNodeClick"
        @node-contextmenu="handleNodeRightClick"
        :expand-on-click-node="false"
        :default-expanded-keys="expandedKeys"
      >
        <template #default="{ node, data }">
          <span class="tree-node">
            <el-icon v-if="data.type === 'folder'"><Folder /></el-icon>
            <el-icon v-else-if="data.type === 'image'"><Picture /></el-icon>
            <el-icon v-else-if="data.type === 'pdf'"><Document /></el-icon>
            <el-icon v-else-if="data.type === 'word'"><Document /></el-icon>
            <span class="node-label" :title="node.label">{{ node.label }}</span>
          </span>
        </template>
      </el-tree>
    </div>

    <!-- 上传文件对话框 -->
    <el-dialog v-model="showUploadDialog" title="上传文件" width="500px">
      <el-upload
        ref="uploadRef"
        :auto-upload="false"
        :on-change="handleFileSelect"
        :accept="acceptedFileTypes"
        drag
        multiple
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <template #tip>
          <div class="el-upload__tip">支持 jpg/png/gif/pdf/docx 格式文件</div>
        </template>
      </el-upload>
      <template #footer>
        <el-button @click="showUploadDialog = false">取消</el-button>
        <el-button type="primary" @click="handleUpload">确定</el-button>
      </template>
    </el-dialog>

    <!-- 文件夹上传文件对话框 -->
    <el-dialog v-model="showFolderUploadDialog" :title="`上传文件到 ${targetFolderName}`" width="500px">
      <el-upload
        ref="folderUploadRef"
        :auto-upload="false"
        :on-change="handleFolderFileSelect"
        :accept="acceptedFileTypes"
        drag
        multiple
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <template #tip>
          <div class="el-upload__tip">支持 jpg/png/gif/pdf/docx 格式文件</div>
        </template>
      </el-upload>
      <template #footer>
        <el-button @click="cancelFolderUpload">取消</el-button>
        <el-button type="primary" @click="handleFolderUpload">确定</el-button>
      </template>
    </el-dialog>

    <!-- 新建文件夹对话框 -->
    <el-dialog v-model="showCreateFolderDialog" title="新建文件夹" width="400px">
      <el-form :model="folderForm" label-width="80px">
        <el-form-item label="文件夹名">
          <el-input v-model="folderForm.name" placeholder="请输入文件夹名称"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showCreateFolderDialog = false">取消</el-button>
        <el-button type="primary" @click="createFolder">确定</el-button>
      </template>
    </el-dialog>

    <!-- 重命名文件夹对话框 -->
    <el-dialog v-model="showRenameFolderDialog" title="重命名文件夹" width="400px">
      <el-form :model="renameForm" label-width="80px">
        <el-form-item label="新名称">
          <el-input v-model="renameForm.newName" placeholder="请输入新的文件夹名称" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showRenameFolderDialog = false">取消</el-button>
        <el-button type="primary" @click="confirmRename">确定</el-button>
      </template>
    </el-dialog>

    <!-- 重命名文件对话框 -->
    <el-dialog v-model="showRenameFileDialog" title="重命名文件" width="400px">
      <el-form>
        <el-form-item label="新名称">
          <el-input v-model="renameFileForm.newName" placeholder="请输入新的文件名称" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showRenameFileDialog = false">取消</el-button>
        <el-button type="primary" @click="confirmFileRename">确定</el-button>
      </template>
    </el-dialog>

    <!-- 右键菜单 -->
    <div
      v-show="showContextMenu"
      ref="contextMenuRef"
      class="context-menu"
      :style="contextMenuStyle"
      @click.stop
      @contextmenu.prevent
    >
      <!-- 文件夹菜单项 -->
      <div class="context-menu-item" @click="handleContextMenuCommand('uploadToFolder')" v-if="contextMenuData?.type === 'folder'">
        <el-icon><Upload /></el-icon>
        <span>上传文件</span>
      </div>
      <div class="context-menu-item" @click="handleContextMenuCommand('rename')" v-if="contextMenuData?.type === 'folder' && contextMenuData?.id !== 'root'">
        <el-icon><Edit /></el-icon>
        <span>重命名</span>
      </div>
      <div class="context-menu-item" @click="handleContextMenuCommand('delete')" v-if="contextMenuData?.type === 'folder' && contextMenuData?.id !== 'root'">
        <el-icon><Delete /></el-icon>
        <span>删除文件夹</span>
      </div>

      <!-- 文件菜单项 -->
      <div class="context-menu-item" @click="handleContextMenuCommand('previewOriginal')" v-if="contextMenuData?.type !== 'folder'">
        <el-icon><Picture /></el-icon>
        <span>原件预览</span>
      </div>
      <div class="context-menu-item" @click="handleContextMenuCommand('previewWatermark')" v-if="contextMenuData?.type !== 'folder'">
        <el-icon><Picture /></el-icon>
        <span>水印预览</span>
      </div>
      <div class="context-menu-item" @click="handleContextMenuCommand('renameFile')" v-if="contextMenuData?.type !== 'folder'">
        <el-icon><Edit /></el-icon>
        <span>重命名</span>
      </div>
      <div class="context-menu-item" @click="handleContextMenuCommand('deleteFile')" v-if="contextMenuData?.type !== 'folder'">
        <el-icon><Delete /></el-icon>
        <span>删除文件</span>
      </div>
    </div>

    <!-- 文件预览对话框 -->
    <FilePreview
      v-model="showPreviewDialog"
      :file="previewFile"
      :preview-mode="previewMode"
    />
  </div>
</template>

<script setup>
import { ref, reactive, computed, nextTick, onMounted, onUnmounted } from 'vue'
import { Upload, FolderAdd, Folder, Picture, Document, UploadFilled, Edit, Delete } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useFileStore } from '../stores/fileStore'
import { FileTypeDetector } from '../utils/watermarkUtils'
import FilePreview from './FilePreview.vue'

const emit = defineEmits(['file-selected', 'folder-selected'])
const fileStore = useFileStore()

// 响应式数据
const showUploadDialog = ref(false)
const showFolderUploadDialog = ref(false)
const showCreateFolderDialog = ref(false)
const showRenameFolderDialog = ref(false)
const showRenameFileDialog = ref(false)
const uploadRef = ref()
const folderUploadRef = ref()
const treeRef = ref()
const selectedFiles = ref([])
const selectedFolderFiles = ref([])
const contextMenuRef = ref()
const contextMenuData = ref(null)
const showContextMenu = ref(false)
const contextMenuStyle = ref({})
const targetFolderId = ref('')
const targetFolderName = ref('')
const expandedKeys = ref([])
const fileManagerRef = ref()
const isCompact = ref(false)
const showPreviewDialog = ref(false)
const previewFile = ref(null)
const previewMode = ref('original')

const folderForm = reactive({
  name: '',
})

const renameForm = reactive({
  newName: '',
  folderId: '',
})

const renameFileForm = reactive({
  newName: '',
  fileId: '',
})

// 使用store中的文件树
const fileTree = computed(() => fileStore.fileTree)

const treeProps = {
  children: 'children',
  label: 'label',
}

const acceptedFileTypes = '.jpg,.jpeg,.png,.gif,.pdf,.docx,.doc'

// 方法
const handleFileSelect = (file) => {
  selectedFiles.value.push(file)
}

const handleUpload = async () => {
  if (selectedFiles.value.length === 0) {
    return
  }

  let successCount = 0
  let errorCount = 0

  for (const file of selectedFiles.value) {
    try {
      // 验证文件
      const validation = await FileTypeDetector.validateFile(file.raw)

      if (!validation.isValid) {
        const errors = validation.errors.filter((e) => !e.startsWith('警告'))
        if (errors.length > 0) {
          ElMessage.error(`${file.name}: ${errors.join(', ')}`)
          errorCount++
          continue
        }
      }

      // 显示警告（如果有）
      if (validation.warnings.length > 0) {
        ElMessage.warning(`${file.name}: ${validation.warnings.join(', ')}`)
      }

      const fileType = FileTypeDetector.getFileType(file.name)
      await fileStore.addFile({
        name: file.name,
        type: fileType,
        file: file.raw,
        size: file.size,
        folderId: fileStore.currentFolder
      })

      successCount++
    } catch (error) {
      console.error(`文件上传失败: ${file.name}`, error)
      errorCount++
    }
  }

  // 显示结果摘要
  if (successCount > 0) {
    ElMessage.success(`成功上传 ${successCount} 个文件`)
  }
  if (errorCount > 0) {
    ElMessage.error(`${errorCount} 个文件上传失败`)
  }

  selectedFiles.value = []
  showUploadDialog.value = false
}

const createFolder = async () => {
  if (!folderForm.name.trim()) {
    return
  }

  try {
    await fileStore.addFolder(folderForm.name)
    folderForm.name = ''
    showCreateFolderDialog.value = false
  } catch (error) {
    // 错误已在store中处理
  }
}

const handleNodeClick = (data) => {
  if (data.type === 'folder') {
    // 点击文件夹时发送文件夹选择事件
    emit('folder-selected', data)
  } else if (data.file) {
    // 点击文件时发送文件选择事件
    emit('file-selected', data.file)
  }
}

// 右键菜单处理
const handleNodeRightClick = (event, data) => {
  event.preventDefault()
  event.stopPropagation()

  // 对文件夹（非根文件夹）和文件显示右键菜单
  if ((data.type === 'folder' && data.id !== 'root') || data.type !== 'folder') {
    contextMenuData.value = data

    // 获取鼠标位置
    let mouseX = event.clientX
    let mouseY = event.clientY

    // 获取视窗尺寸
    const viewportWidth = window.innerWidth
    const viewportHeight = window.innerHeight

    // 菜单预估尺寸
    const menuWidth = 120
    const menuHeight = 80

    // 调整位置避免超出视窗
    if (mouseX + menuWidth > viewportWidth) {
      mouseX = viewportWidth - menuWidth - 10
    }
    if (mouseY + menuHeight > viewportHeight) {
      mouseY = viewportHeight - menuHeight - 10
    }

    // 设置菜单位置
    contextMenuStyle.value = {
      position: 'fixed',
      left: `${mouseX}px`,
      top: `${mouseY}px`,
      zIndex: 9999
    }

    // 显示菜单
    showContextMenu.value = true

    // 添加全局事件监听器来隐藏菜单
    nextTick(() => {
      document.addEventListener('click', hideContextMenu, true)
      document.addEventListener('contextmenu', hideContextMenu, true)
      document.addEventListener('keydown', handleKeyDown, true)
    })
  }
}

// 处理键盘事件
const handleKeyDown = (event) => {
  if (event.key === 'Escape') {
    hideContextMenu()
  }
}

// 隐藏右键菜单
const hideContextMenu = (event) => {
  // 如果点击的是菜单内部，不隐藏菜单
  if (event && event.target && contextMenuRef.value && contextMenuRef.value.contains(event.target)) {
    return
  }

  showContextMenu.value = false
  contextMenuData.value = null
  document.removeEventListener('click', hideContextMenu, true)
  document.removeEventListener('contextmenu', hideContextMenu, true)
  document.removeEventListener('keydown', handleKeyDown, true)
}

// 右键菜单命令处理
const handleContextMenuCommand = async (command) => {
  if (!contextMenuData.value) return

  const data = contextMenuData.value

  // 隐藏菜单
  hideContextMenu()

  switch (command) {
    case 'uploadToFolder':
      openFolderUploadDialog(data)
      break
    case 'rename':
      showRenameDialog(data)
      break
    case 'delete':
      await deleteFolder(data)
      break
    case 'previewOriginal':
      showFilePreview(data, 'original')
      break
    case 'previewWatermark':
      showFilePreview(data, 'watermark')
      break
    case 'renameFile':
      showFileRenameDialog(data)
      break
    case 'deleteFile':
      await deleteFile(data)
      break
  }
}

// 显示重命名对话框
const showRenameDialog = (folder) => {
  renameForm.folderId = folder.id
  renameForm.newName = folder.label
  showRenameFolderDialog.value = true
}

// 确认重命名
const confirmRename = async () => {
  if (!renameForm.newName.trim()) {
    ElMessage.warning('请输入新的文件夹名称')
    return
  }

  try {
    await fileStore.renameFolder(renameForm.folderId, renameForm.newName.trim())
    showRenameFolderDialog.value = false
    renameForm.newName = ''
    renameForm.folderId = ''
  } catch (error) {
    // 错误已在store中处理
  }
}

// 显示文件重命名对话框
const showFileRenameDialog = (file) => {
  renameFileForm.fileId = file.file.id
  renameFileForm.newName = file.file.name
  showRenameFileDialog.value = true
}

// 确认文件重命名
const confirmFileRename = async () => {
  if (!renameFileForm.newName.trim()) {
    ElMessage.warning('请输入新的文件名称')
    return
  }

  try {
    await fileStore.renameFile(renameFileForm.fileId, renameFileForm.newName.trim())
    showRenameFileDialog.value = false
    renameFileForm.newName = ''
    renameFileForm.fileId = ''
  } catch (error) {
    // 错误已在store中处理
  }
}

// 删除文件
const deleteFile = async (file) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除文件"${file.file.name}"吗？此操作不可恢复！`,
      '确认删除',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: false
      }
    )

    await fileStore.deleteFile(file.file.id)
  } catch (error) {
    if (error !== 'cancel') {
      // 删除失败的错误已在store中处理
    }
  }
}

// 删除文件夹
const deleteFolder = async (folder) => {
  try {
    // 首先尝试普通删除
    await fileStore.deleteFolder(folder.id, false)
  } catch (error) {
    // 如果删除失败，检查是否是因为文件夹不为空
    if (error.message && error.message.includes('文件夹不为空')) {
      try {
        // 提示用户确认递归删除
        await ElMessageBox.confirm(
          `文件夹"${folder.label}"不为空，是否删除该文件夹及其所有内容？此操作不可恢复！`,
          '确认删除',
          {
            confirmButtonText: '确定删除',
            cancelButtonText: '取消',
            type: 'warning',
            dangerouslyUseHTMLString: false
          }
        )

        // 用户确认后进行递归删除
        await fileStore.deleteFolder(folder.id, true)
      } catch (confirmError) {
        if (confirmError !== 'cancel') {
          // 递归删除失败的错误已在store中处理
        }
      }
    } else {
      // 其他错误已在store中处理
    }
  }
}

// 显示文件夹上传对话框
const openFolderUploadDialog = (folder) => {
  targetFolderId.value = folder.id
  targetFolderName.value = folder.label
  selectedFolderFiles.value = []
  showFolderUploadDialog.value = true
}

// 处理文件夹文件选择
const handleFolderFileSelect = (file) => {
  selectedFolderFiles.value.push(file)
}

// 处理文件夹上传
const handleFolderUpload = async () => {
  if (selectedFolderFiles.value.length === 0) {
    return
  }

  let successCount = 0
  let errorCount = 0

  for (const file of selectedFolderFiles.value) {
    try {
      // 验证文件
      const validation = await FileTypeDetector.validateFile(file.raw)

      if (!validation.isValid) {
        const errors = validation.errors.filter((e) => !e.startsWith('警告'))
        if (errors.length > 0) {
          ElMessage.error(`${file.name}: ${errors.join(', ')}`)
          errorCount++
          continue
        }
      }

      // 显示警告（如果有）
      if (validation.warnings.length > 0) {
        ElMessage.warning(`${file.name}: ${validation.warnings.join(', ')}`)
      }

      const fileType = FileTypeDetector.getFileType(file.name)
      await fileStore.addFile({
        name: file.name,
        type: fileType,
        file: file.raw,
        size: file.size,
        folderId: targetFolderId.value
      })

      successCount++
    } catch (error) {
      console.error(`文件上传失败: ${file.name}`, error)
      errorCount++
    }
  }

  // 显示结果摘要
  if (successCount > 0) {
    ElMessage.success(`成功上传 ${successCount} 个文件到 ${targetFolderName.value}`)

    // 展开目标文件夹
    if (treeRef.value && targetFolderId.value) {
      // 确保文件夹在展开列表中
      if (!expandedKeys.value.includes(targetFolderId.value)) {
        expandedKeys.value.push(targetFolderId.value)
      }
      // 设置展开状态
      treeRef.value.setExpandedKeys([...expandedKeys.value])
    }
  }
  if (errorCount > 0) {
    ElMessage.error(`${errorCount} 个文件上传失败`)
  }

  selectedFolderFiles.value = []
  if (folderUploadRef.value) {
    folderUploadRef.value.clearFiles()
  }
  showFolderUploadDialog.value = false
}

// 取消文件夹上传
const cancelFolderUpload = () => {
  selectedFolderFiles.value = []
  if (folderUploadRef.value) {
    folderUploadRef.value.clearFiles()
  }
  showFolderUploadDialog.value = false
}

// 显示文件预览
const showFilePreview = (fileNode, mode = 'original') => {
  if (fileNode.file) {
    previewFile.value = fileNode.file
    previewMode.value = mode
    showPreviewDialog.value = true
  }
}

// 监听容器宽度变化
let resizeObserver = null
onMounted(() => {
  if (fileManagerRef.value) {
    resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        const width = entry.contentRect.width
        isCompact.value = width < 270
      }
    })
    resizeObserver.observe(fileManagerRef.value)
  }
})

// 组件卸载时清理事件监听器
onUnmounted(() => {
  document.removeEventListener('click', hideContextMenu, true)
  document.removeEventListener('contextmenu', hideContextMenu, true)
  document.removeEventListener('keydown', handleKeyDown, true)

  if (resizeObserver) {
    resizeObserver.disconnect()
  }
})
</script>

<style scoped>
.file-manager {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.toolbar {
  padding: 16px;
  border-bottom: 1px solid #ebeef5;
  display: flex;
  gap: 8px;
  flex-wrap: nowrap;
}

.toolbar .el-button {
  flex-shrink: 0;
  min-width: 0;
}

.toolbar-btn {
  transition: all 0.3s ease;
}

.toolbar-btn .btn-text {
  margin-left: 4px;
  transition: all 0.3s ease;
}

/* 当文件管理器容器较小时的响应式处理 */
.file-manager[data-compact="true"] .toolbar-btn .btn-text {
  display: none;
}

.file-manager[data-compact="true"] .toolbar-btn {
  min-width: 32px;
  padding: 8px;
}

.file-tree {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

/* 树节点样式 - 修复文件名省略号显示 */
:deep(.el-tree-node__content) {
  overflow: hidden;
  width: 100%;
}

:deep(.el-tree-node__label) {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.tree-node {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
  min-width: 0;
  overflow: hidden;
  flex: 1;
}

.node-label {
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  white-space: nowrap !important;
  flex: 1;
  min-width: 0;
  max-width: calc(100% - 32px); /* 减去图标的宽度 */
  display: inline-block;
}

.context-menu {
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 4px 0;
  min-width: 120px;
}

.context-menu-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  cursor: pointer;
  font-size: 14px;
  color: #606266;
  transition: background-color 0.3s;
}

.context-menu-item:hover {
  background-color: #f5f7fa;
  color: #409eff;
}

.el-icon--upload {
  font-size: 67px;
  color: #c0c4cc;
  margin: 40px 0 16px;
  line-height: 50px;
}
</style>
