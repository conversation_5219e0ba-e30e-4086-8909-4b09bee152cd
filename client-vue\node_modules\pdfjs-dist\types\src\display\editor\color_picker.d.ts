/**
 * BasicColorPicker class provides a simple color picker.
 * It displays an input element (with type="color") that allows the user
 * to select a color for the annotation.
 */
export class BasicColorPicker {
    static "__#6@#l10nColor": null;
    constructor(editor: any);
    renderButton(): HTMLInputElement;
    update(value: any): void;
    destroy(): void;
    hideDropdown(): void;
    #private;
}
/**
 * ColorPicker class provides a color picker for the annotation editor.
 * It displays a dropdown with some predefined colors and allows the user
 * to select a color for the annotation.
 */
export class ColorPicker {
    static "__#5@#l10nColor": null;
    static get _keyboardManager(): any;
    constructor({ editor, uiManager }: {
        editor?: null | undefined;
        uiManager?: null | undefined;
    });
    renderButton(): HTMLButtonElement;
    renderMainDropdown(): HTMLDivElement;
    _colorSelectFromKeyboard(event: any): void;
    _moveToNext(event: any): void;
    _moveToPrevious(event: any): void;
    _moveToBeginning(event: any): void;
    _moveToEnd(event: any): void;
    hideDropdown(): void;
    _hideDropdownFromKeyboard(): void;
    updateColor(color: any): void;
    destroy(): void;
    #private;
}
