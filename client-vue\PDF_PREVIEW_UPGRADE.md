# PDF预览功能升级说明

## 概述
将前端PDF预览功能从"在新窗口中打开"改为使用PDF.js+Canvas在当前对话框中直接预览。

## 主要更改

### 1. 依赖安装
- 安装了 `pdfjs-dist` 包用于PDF渲染

### 2. 组件更新 (FilePreview.vue)

#### 模板更改
- 替换了原来的PDF预览部分，从简单的"在新窗口打开"按钮改为完整的PDF查看器
- 添加了PDF工具栏，包含：
  - 文件名和页码信息显示
  - 上一页/下一页导航按钮
  - 缩放控制按钮（放大、缩小、重置）
- 添加了PDF画布容器用于渲染PDF页面

#### 脚本更改
- 导入PDF.js库和worker配置
- 添加PDF相关的响应式数据：
  - `pdfCanvas`: PDF画布引用
  - `pdfDoc`: PDF文档对象
  - `currentPage`: 当前页码
  - `pdfPageCount`: 总页数
  - `pdfScale`: PDF缩放比例
  - `renderTask`: 渲染任务引用

#### 新增方法
- `loadPdf()`: 加载PDF文档
- `renderPage()`: 渲染指定页面
- `previousPage()`: 上一页
- `nextPage()`: 下一页
- `zoomInPdf()`: 放大PDF
- `zoomOutPdf()`: 缩小PDF
- `resetPdfZoom()`: 重置PDF缩放

#### 监听器更新
- 更新文件变化监听器，当文件类型为PDF时自动加载PDF
- 更新对话框显示状态监听器，处理PDF资源的清理
- 更新预览类型变化监听器，支持PDF类型

#### 样式更改
- 添加了完整的PDF预览样式
- 包含工具栏、查看器容器、画布样式等

### 3. 配置更新 (vite.config.js)
- 添加了PDF.js的优化依赖配置
- 配置了Web Worker支持

## 功能特性

### PDF查看器功能
1. **页面导航**: 支持上一页/下一页切换
2. **缩放控制**: 支持放大、缩小、重置缩放（50%-300%）
3. **页面信息**: 显示当前页码和总页数
4. **响应式设计**: 适配不同屏幕尺寸
5. **错误处理**: 完善的错误提示和重试机制

### 用户体验改进
1. **无需新窗口**: 在当前对话框中直接预览PDF
2. **快速加载**: 使用Canvas渲染，加载速度快
3. **流畅操作**: 支持键盘和鼠标操作
4. **资源管理**: 自动清理PDF资源，避免内存泄漏

## 技术实现

### PDF.js集成
- 使用最新版本的PDF.js库
- 配置本地Worker文件，避免CDN依赖
- 支持异步加载和渲染

### Canvas渲染
- 使用HTML5 Canvas进行PDF页面渲染
- 支持高质量渲染（1.5倍缩放）
- 优化渲染性能，支持渲染任务取消

### 状态管理
- 完善的组件状态管理
- 支持多个PDF文件切换
- 自动清理资源，防止内存泄漏

## 兼容性
- 支持现代浏览器（Chrome 80+, Firefox 75+, Safari 13+, Edge 80+）
- 支持移动端浏览器
- 向后兼容原有的图片和Word文档预览功能

## 使用说明
1. 用户点击PDF文件的预览按钮
2. 系统自动加载PDF文档并显示第一页
3. 用户可以使用工具栏进行页面导航和缩放操作
4. 关闭对话框时自动清理资源

## 注意事项
1. 大型PDF文件可能需要较长加载时间
2. 建议PDF文件大小控制在合理范围内
3. 如遇到加载问题，可以使用重试功能
