import { defineStore } from 'pinia'
import { authApi } from '@/services/api'

export const useAuthStore = defineStore('auth', {
  state: () => ({
    // 用户信息
    user: null,
    // 访问令牌
    accessToken: localStorage.getItem('accessToken') || null,
    // 令牌过期时间
    tokenExpiresAt: localStorage.getItem('tokenExpiresAt') || null,
    // 登录状态
    isAuthenticated: false,
    // 加载状态
    loading: false
  }),

  getters: {
    // 是否为管理员
    isAdmin: (state) => state.user?.role === 'Admin',
    
    // 是否为普通用户
    isUser: (state) => state.user?.role === 'User',
    
    // 是否可以删除文件
    canDeleteFiles: (state) => {
      if (!state.user) return false
      return state.user.role === 'Admin' || state.user.canDeleteFiles
    },
    
    // 是否可以下载源文件
    canDownloadOriginalFiles: (state) => {
      if (!state.user) return false
      return state.user.role === 'Admin' || state.user.canDownloadOriginalFiles
    },
    
    // 用户显示名称
    displayName: (state) => {
      if (!state.user) return ''
      return state.user.displayName || state.user.username
    },
    
    // 令牌是否过期
    isTokenExpired: (state) => {
      if (!state.tokenExpiresAt) return true
      return Date.now() / 1000 > parseInt(state.tokenExpiresAt)
    }
  },

  actions: {
    // 初始化认证状态
    async initAuth() {
      if (this.accessToken && !this.isTokenExpired) {
        try {
          await this.getCurrentUser()
        } catch (error) {
          console.error('初始化认证状态失败:', error)
          this.logout()
        }
      } else {
        this.logout()
      }
    },

    // 用户登录
    async login(credentials) {
      try {
        this.loading = true
        const response = await authApi.login(credentials)
        
        if (response.success && response.data) {
          const { accessToken, expiresAt, user } = response.data
          
          // 保存令牌和用户信息
          this.setAuthData(accessToken, expiresAt, user)
          
          return true
        } else {
          throw new Error(response.message || '登录失败')
        }
      } catch (error) {
        console.error('登录失败:', error)
        return false
      } finally {
        this.loading = false
      }
    },

    // 用户注册
    async register(userData) {
      try {
        this.loading = true
        const response = await authApi.register(userData)
        
        if (response.success) {
          return { success: true, data: response.data }
        } else {
          return { success: false, message: response.message || '注册失败' }
        }
      } catch (error) {
        console.error('注册失败:', error)
        return { success: false, message: '注册失败，请稍后重试' }
      } finally {
        this.loading = false
      }
    },

    // 获取当前用户信息
    async getCurrentUser() {
      try {
        const response = await authApi.getCurrentUser()
        
        if (response.success && response.data) {
          this.user = response.data
          this.isAuthenticated = true
          return response.data
        } else {
          throw new Error('获取用户信息失败')
        }
      } catch (error) {
        console.error('获取用户信息失败:', error)
        this.logout()
        throw error
      }
    },

    // 修改密码
    async changePassword(passwordData) {
      try {
        this.loading = true
        const response = await authApi.changePassword(passwordData)
        
        if (response.success) {
          return { success: true, message: '密码修改成功' }
        } else {
          return { success: false, message: response.message || '密码修改失败' }
        }
      } catch (error) {
        console.error('修改密码失败:', error)
        return { success: false, message: '修改密码失败，请稍后重试' }
      } finally {
        this.loading = false
      }
    },

    // 用户登出
    async logout() {
      try {
        // 调用后端登出接口（记录日志）
        if (this.isAuthenticated) {
          await authApi.logout()
        }
      } catch (error) {
        console.error('登出失败:', error)
      } finally {
        // 清除本地数据
        this.clearAuthData()

        // 清除文件数据
        const { useFileStore } = await import('@/stores/fileStore')
        const fileStore = useFileStore()
        fileStore.clearData()
      }
    },

    // 设置认证数据
    setAuthData(accessToken, expiresAt, user) {
      this.accessToken = accessToken
      this.tokenExpiresAt = expiresAt
      this.user = user
      this.isAuthenticated = true

      // 保存到本地存储
      localStorage.setItem('accessToken', accessToken)
      localStorage.setItem('tokenExpiresAt', expiresAt)
    },

    // 清除认证数据
    clearAuthData() {
      this.accessToken = null
      this.tokenExpiresAt = null
      this.user = null
      this.isAuthenticated = false

      // 清除本地存储
      localStorage.removeItem('accessToken')
      localStorage.removeItem('tokenExpiresAt')
    },

    // 刷新令牌（如果需要的话）
    async refreshToken() {
      // 这里可以实现令牌刷新逻辑
      // 目前使用的是长期令牌，暂不实现
    },

    // 检查权限
    hasPermission(permission) {
      if (!this.user) return false
      
      // 管理员拥有所有权限
      if (this.user.role === 'Admin') return true
      
      // 检查特定权限
      switch (permission) {
        case 'delete_files':
          return this.user.canDeleteFiles
        case 'download_original_files':
          return this.user.canDownloadOriginalFiles
        default:
          return false
      }
    }
  }
})
