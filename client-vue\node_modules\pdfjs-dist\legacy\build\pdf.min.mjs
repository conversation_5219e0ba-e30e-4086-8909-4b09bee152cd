/**
 * @licstart The following is the entire license notice for the
 * JavaScript code in this page
 *
 * Copyright 2024 Mozilla Foundation
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * @licend The above is the entire license notice for the
 * JavaScript code in this page
 */
/**
 * pdfjsVersion = 5.4.54
 * pdfjsBuild = 295fb3ec4
 */var t={34:(t,e,i)=>{var n=i(4901);t.exports=function(t){return"object"==typeof t?null!==t:n(t)}},81:(t,e,i)=>{var n=i(9565),s=i(9306),r=i(8551),a=i(6823),o=i(851),l=TypeError;t.exports=function(t,e){var i=arguments.length<2?o(t):e;if(s(i))return r(n(i,t));throw new l(a(t)+" is not iterable")}},283:(t,e,i)=>{var n=i(9504),s=i(9039),r=i(4901),a=i(9297),o=i(3724),l=i(350).CONFIGURABLE,h=i(3706),c=i(1181),d=c.enforce,u=c.get,p=String,g=Object.defineProperty,f=n("".slice),m=n("".replace),b=n([].join),v=o&&!s((function(){return 8!==g((function(){}),"length",{value:8}).length})),w=String(String).split("String"),y=t.exports=function(t,e,i){"Symbol("===f(p(e),0,7)&&(e="["+m(p(e),/^Symbol\(([^)]*)\).*$/,"$1")+"]");i&&i.getter&&(e="get "+e);i&&i.setter&&(e="set "+e);(!a(t,"name")||l&&t.name!==e)&&(o?g(t,"name",{value:e,configurable:!0}):t.name=e);v&&i&&a(i,"arity")&&t.length!==i.arity&&g(t,"length",{value:i.arity});try{i&&a(i,"constructor")&&i.constructor?o&&g(t,"prototype",{writable:!1}):t.prototype&&(t.prototype=void 0)}catch(t){}var n=d(t);a(n,"source")||(n.source=b(w,"string"==typeof e?e:""));return t};Function.prototype.toString=y((function toString(){return r(this)&&u(this).source||h(this)}),"toString")},350:(t,e,i)=>{var n=i(3724),s=i(9297),r=Function.prototype,a=n&&Object.getOwnPropertyDescriptor,o=s(r,"name"),l=o&&"something"===function something(){}.name,h=o&&(!n||n&&a(r,"name").configurable);t.exports={EXISTS:o,PROPER:l,CONFIGURABLE:h}},397:(t,e,i)=>{var n=i(7751);t.exports=n("document","documentElement")},421:t=>{t.exports={}},507:(t,e,i)=>{var n=i(9565);t.exports=function(t,e,i){for(var s,r,a=i?t:t.iterator,o=t.next;!(s=n(o,a)).done;)if(void 0!==(r=e(s.value)))return r}},531:(t,e,i)=>{var n=i(6518),s=i(9565),r=i(9306),a=i(8551),o=i(1767),l=i(8646),h=i(9462),c=i(9539),d=i(6395),u=i(684),p=i(4549),g=!d&&!u("flatMap",(function(){})),f=!d&&!g&&p("flatMap",TypeError),m=d||g||f,b=h((function(){for(var t,e,i=this.iterator,n=this.mapper;;){if(e=this.inner)try{if(!(t=a(s(e.next,e.iterator))).done)return t.value;this.inner=null}catch(t){c(i,"throw",t)}t=a(s(this.next,i));if(this.done=!!t.done)return;try{this.inner=l(n(t.value,this.counter++),!1)}catch(t){c(i,"throw",t)}}}));n({target:"Iterator",proto:!0,real:!0,forced:m},{flatMap:function flatMap(t){a(this);try{r(t)}catch(t){c(this,"throw",t)}return f?s(f,this,t):new b(o(this),{mapper:t,inner:null})}})},616:(t,e,i)=>{var n=i(9039);t.exports=!n((function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")}))},655:(t,e,i)=>{var n=i(6955),s=String;t.exports=function(t){if("Symbol"===n(t))throw new TypeError("Cannot convert a Symbol value to a string");return s(t)}},679:(t,e,i)=>{var n=i(1625),s=TypeError;t.exports=function(t,e){if(n(e,t))return t;throw new s("Incorrect invocation")}},684:t=>{t.exports=function(t,e){var i="function"==typeof Iterator&&Iterator.prototype[t];if(i)try{i.call({next:null},e).next()}catch(t){return!0}}},741:t=>{var e=Math.ceil,i=Math.floor;t.exports=Math.trunc||function trunc(t){var n=+t;return(n>0?i:e)(n)}},757:(t,e,i)=>{var n=i(7751),s=i(4901),r=i(1625),a=i(7040),o=Object;t.exports=a?function(t){return"symbol"==typeof t}:function(t){var e=n("Symbol");return s(e)&&r(e.prototype,o(t))}},851:(t,e,i)=>{var n=i(6955),s=i(5966),r=i(4117),a=i(6269),o=i(8227)("iterator");t.exports=function(t){if(!r(t))return s(t,o)||s(t,"@@iterator")||a[n(t)]}},944:t=>{var e=TypeError;t.exports=function(t){var i=t&&t.alphabet;if(void 0===i||"base64"===i||"base64url"===i)return i||"base64";throw new e("Incorrect `alphabet` option")}},1072:(t,e,i)=>{var n=i(1828),s=i(8727);t.exports=Object.keys||function keys(t){return n(t,s)}},1103:t=>{t.exports=function(t){try{return{error:!1,value:t()}}catch(t){return{error:!0,value:t}}}},1108:(t,e,i)=>{var n=i(6955);t.exports=function(t){var e=n(t);return"BigInt64Array"===e||"BigUint64Array"===e}},1148:(t,e,i)=>{var n=i(6518),s=i(9565),r=i(2652),a=i(9306),o=i(8551),l=i(1767),h=i(9539),c=i(4549)("every",TypeError);n({target:"Iterator",proto:!0,real:!0,forced:c},{every:function every(t){o(this);try{a(t)}catch(t){h(this,"throw",t)}if(c)return s(c,this,t);var e=l(this),i=0;return!r(e,(function(e,n){if(!t(e,i++))return n()}),{IS_RECORD:!0,INTERRUPTED:!0}).stopped}})},1181:(t,e,i)=>{var n,s,r,a=i(8622),o=i(4576),l=i(34),h=i(6699),c=i(9297),d=i(7629),u=i(6119),p=i(421),g="Object already initialized",f=o.TypeError,m=o.WeakMap;if(a||d.state){var b=d.state||(d.state=new m);b.get=b.get;b.has=b.has;b.set=b.set;n=function(t,e){if(b.has(t))throw new f(g);e.facade=t;b.set(t,e);return e};s=function(t){return b.get(t)||{}};r=function(t){return b.has(t)}}else{var v=u("state");p[v]=!0;n=function(t,e){if(c(t,v))throw new f(g);e.facade=t;h(t,v,e);return e};s=function(t){return c(t,v)?t[v]:{}};r=function(t){return c(t,v)}}t.exports={set:n,get:s,has:r,enforce:function(t){return r(t)?s(t):n(t,{})},getterFor:function(t){return function(e){var i;if(!l(e)||(i=s(e)).type!==t)throw new f("Incompatible receiver, "+t+" required");return i}}}},1291:(t,e,i)=>{var n=i(741);t.exports=function(t){var e=+t;return e!=e||0===e?0:n(e)}},1385:(t,e,i)=>{var n=i(9539);t.exports=function(t,e,i){for(var s=t.length-1;s>=0;s--)if(void 0!==t[s])try{i=n(t[s].iterator,e,i)}catch(t){e="throw";i=t}if("throw"===e)throw i;return i}},1548:(t,e,i)=>{var n=i(4576),s=i(9039),r=i(9519),a=i(4215),o=n.structuredClone;t.exports=!!o&&!s((function(){if("DENO"===a&&r>92||"NODE"===a&&r>94||"BROWSER"===a&&r>97)return!1;var t=new ArrayBuffer(8),e=o(t,{transfer:[t]});return 0!==t.byteLength||8!==e.byteLength}))},1549:(t,e,i)=>{var n=i(6518),s=i(4576),r=i(9143),a=i(4154),o=s.Uint8Array,l=!o||!o.prototype.setFromBase64||!function(){var t=new o([255,255,255,255,255]);try{t.setFromBase64("",null);return}catch(t){}try{t.setFromBase64("MjYyZg===")}catch(e){return 50===t[0]&&54===t[1]&&50===t[2]&&255===t[3]&&255===t[4]}}();o&&n({target:"Uint8Array",proto:!0,forced:l},{setFromBase64:function setFromBase64(t){a(this);var e=r(t,arguments.length>1?arguments[1]:void 0,this,this.length);return{read:e.read,written:e.written}}})},1625:(t,e,i)=>{var n=i(9504);t.exports=n({}.isPrototypeOf)},1689:(t,e,i)=>{var n=i(6518),s=i(4576),r=i(8745),a=i(7680),o=i(6043),l=i(9306),h=i(1103),c=s.Promise,d=!1;n({target:"Promise",stat:!0,forced:!c||!c.try||h((function(){c.try((function(t){d=8===t}),8)})).error||!d},{try:function(t){var e=arguments.length>1?a(arguments,1):[],i=o.f(this),n=h((function(){return r(l(t),void 0,e)}));(n.error?i.reject:i.resolve)(n.value);return i.promise}})},1698:(t,e,i)=>{var n=i(6518),s=i(4204),r=i(9835);n({target:"Set",proto:!0,real:!0,forced:!i(4916)("union")||!r("union")},{union:s})},1701:(t,e,i)=>{var n=i(6518),s=i(9565),r=i(9306),a=i(8551),o=i(1767),l=i(9462),h=i(6319),c=i(9539),d=i(684),u=i(4549),p=i(6395),g=!p&&!d("map",(function(){})),f=!p&&!g&&u("map",TypeError),m=p||g||f,b=l((function(){var t=this.iterator,e=a(s(this.next,t));if(!(this.done=!!e.done))return h(t,this.mapper,[e.value,this.counter++],!0)}));n({target:"Iterator",proto:!0,real:!0,forced:m},{map:function map(t){a(this);try{r(t)}catch(t){c(this,"throw",t)}return f?s(f,this,t):new b(o(this),{mapper:t})}})},1767:t=>{t.exports=function(t){return{iterator:t,next:t.next,done:!1}}},1828:(t,e,i)=>{var n=i(9504),s=i(9297),r=i(5397),a=i(9617).indexOf,o=i(421),l=n([].push);t.exports=function(t,e){var i,n=r(t),h=0,c=[];for(i in n)!s(o,i)&&s(n,i)&&l(c,i);for(;e.length>h;)s(n,i=e[h++])&&(~a(c,i)||l(c,i));return c}},2106:(t,e,i)=>{var n=i(283),s=i(4913);t.exports=function(t,e,i){i.get&&n(i.get,e,{getter:!0});i.set&&n(i.set,e,{setter:!0});return s.f(t,e,i)}},2140:(t,e,i)=>{var n={};n[i(8227)("toStringTag")]="z";t.exports="[object z]"===String(n)},2195:(t,e,i)=>{var n=i(9504),s=n({}.toString),r=n("".slice);t.exports=function(t){return r(s(t),8,-1)}},2211:(t,e,i)=>{var n=i(9039);t.exports=!n((function(){function F(){}F.prototype.constructor=null;return Object.getPrototypeOf(new F)!==F.prototype}))},2303:(t,e,i)=>{var n=i(4576),s=i(9504),r=n.Uint8Array,a=n.SyntaxError,o=n.parseInt,l=Math.min,h=/[^\da-f]/i,c=s(h.exec),d=s("".slice);t.exports=function(t,e){var i=t.length;if(i%2!=0)throw new a("String should be an even number of characters");for(var n=e?l(e.length,i/2):i/2,s=e||new r(n),u=0,p=0;p<n;){var g=d(t,u,u+=2);if(c(h,g))throw new a("String should only contain hex characters");s[p++]=o(g,16)}return{bytes:s,read:u}}},2360:(t,e,i)=>{var n,s=i(8551),r=i(6801),a=i(8727),o=i(421),l=i(397),h=i(4055),c=i(6119),d="prototype",u="script",p=c("IE_PROTO"),EmptyConstructor=function(){},scriptTag=function(t){return"<"+u+">"+t+"</"+u+">"},NullProtoObjectViaActiveX=function(t){t.write(scriptTag(""));t.close();var e=t.parentWindow.Object;t=null;return e},NullProtoObject=function(){try{n=new ActiveXObject("htmlfile")}catch(t){}NullProtoObject="undefined"!=typeof document?document.domain&&n?NullProtoObjectViaActiveX(n):function(){var t,e=h("iframe"),i="java"+u+":";e.style.display="none";l.appendChild(e);e.src=String(i);(t=e.contentWindow.document).open();t.write(scriptTag("document.F=Object"));t.close();return t.F}():NullProtoObjectViaActiveX(n);for(var t=a.length;t--;)delete NullProtoObject[d][a[t]];return NullProtoObject()};o[p]=!0;t.exports=Object.create||function create(t,e){var i;if(null!==t){EmptyConstructor[d]=s(t);i=new EmptyConstructor;EmptyConstructor[d]=null;i[p]=t}else i=NullProtoObject();return void 0===e?i:r.f(i,e)}},2475:(t,e,i)=>{var n=i(6518),s=i(8527);n({target:"Set",proto:!0,real:!0,forced:!i(4916)("isSupersetOf",(function(t){return!t}))},{isSupersetOf:s})},2489:(t,e,i)=>{var n=i(6518),s=i(9565),r=i(9306),a=i(8551),o=i(1767),l=i(9462),h=i(6319),c=i(6395),d=i(9539),u=i(684),p=i(4549),g=!c&&!u("filter",(function(){})),f=!c&&!g&&p("filter",TypeError),m=c||g||f,b=l((function(){for(var t,e,i=this.iterator,n=this.predicate,r=this.next;;){t=a(s(r,i));if(this.done=!!t.done)return;e=t.value;if(h(i,n,[e,this.counter++],!0))return e}}));n({target:"Iterator",proto:!0,real:!0,forced:m},{filter:function filter(t){a(this);try{r(t)}catch(t){d(this,"throw",t)}return f?s(f,this,t):new b(o(this),{predicate:t})}})},2529:t=>{t.exports=function(t,e){return{value:t,done:e}}},2603:(t,e,i)=>{var n=i(655);t.exports=function(t,e){return void 0===t?arguments.length<2?"":e:n(t)}},2652:(t,e,i)=>{var n=i(6080),s=i(9565),r=i(8551),a=i(6823),o=i(4209),l=i(6198),h=i(1625),c=i(81),d=i(851),u=i(9539),p=TypeError,Result=function(t,e){this.stopped=t;this.result=e},g=Result.prototype;t.exports=function(t,e,i){var f,m,b,v,w,y,A,x=i&&i.that,_=!(!i||!i.AS_ENTRIES),E=!(!i||!i.IS_RECORD),S=!(!i||!i.IS_ITERATOR),C=!(!i||!i.INTERRUPTED),T=n(e,x),stop=function(t){f&&u(f,"normal");return new Result(!0,t)},callFn=function(t){if(_){r(t);return C?T(t[0],t[1],stop):T(t[0],t[1])}return C?T(t,stop):T(t)};if(E)f=t.iterator;else if(S)f=t;else{if(!(m=d(t)))throw new p(a(t)+" is not iterable");if(o(m)){for(b=0,v=l(t);v>b;b++)if((w=callFn(t[b]))&&h(g,w))return w;return new Result(!1)}f=c(t,m)}y=E?t.next:f.next;for(;!(A=s(y,f)).done;){try{w=callFn(A.value)}catch(t){u(f,"throw",t)}if("object"==typeof w&&w&&h(g,w))return w}return new Result(!1)}},2777:(t,e,i)=>{var n=i(9565),s=i(34),r=i(757),a=i(5966),o=i(4270),l=i(8227),h=TypeError,c=l("toPrimitive");t.exports=function(t,e){if(!s(t)||r(t))return t;var i,l=a(t,c);if(l){void 0===e&&(e="default");i=n(l,t,e);if(!s(i)||r(i))return i;throw new h("Can't convert object to primitive value")}void 0===e&&(e="number");return o(t,e)}},2787:(t,e,i)=>{var n=i(9297),s=i(4901),r=i(8981),a=i(6119),o=i(2211),l=a("IE_PROTO"),h=Object,c=h.prototype;t.exports=o?h.getPrototypeOf:function(t){var e=r(t);if(n(e,l))return e[l];var i=e.constructor;return s(i)&&e instanceof i?i.prototype:e instanceof h?c:null}},2796:(t,e,i)=>{var n=i(9039),s=i(4901),r=/#|\.prototype\./,isForced=function(t,e){var i=o[a(t)];return i===h||i!==l&&(s(e)?n(e):!!e)},a=isForced.normalize=function(t){return String(t).replace(r,".").toLowerCase()},o=isForced.data={},l=isForced.NATIVE="N",h=isForced.POLYFILL="P";t.exports=isForced},2804:t=>{var e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",i=e+"+/",n=e+"-_",inverse=function(t){for(var e={},i=0;i<64;i++)e[t.charAt(i)]=i;return e};t.exports={i2c:i,c2i:inverse(i),i2cUrl:n,c2iUrl:inverse(n)}},2812:t=>{var e=TypeError;t.exports=function(t,i){if(t<i)throw new e("Not enough arguments");return t}},2839:(t,e,i)=>{var n=i(4576).navigator,s=n&&n.userAgent;t.exports=s?String(s):""},2967:(t,e,i)=>{var n=i(6706),s=i(34),r=i(7750),a=i(3506);t.exports=Object.setPrototypeOf||("__proto__"in{}?function(){var t,e=!1,i={};try{(t=n(Object.prototype,"__proto__","set"))(i,[]);e=i instanceof Array}catch(t){}return function setPrototypeOf(i,n){r(i);a(n);if(!s(i))return i;e?t(i,n):i.__proto__=n;return i}}():void 0)},3167:(t,e,i)=>{var n=i(4901),s=i(34),r=i(2967);t.exports=function(t,e,i){var a,o;r&&n(a=e.constructor)&&a!==i&&s(o=a.prototype)&&o!==i.prototype&&r(t,o);return t}},3238:(t,e,i)=>{var n=i(4576),s=i(7811),r=i(7394),a=n.DataView;t.exports=function(t){if(!s||0!==r(t))return!1;try{new a(t);return!1}catch(t){return!0}}},3392:(t,e,i)=>{var n=i(9504),s=0,r=Math.random(),a=n(1.1.toString);t.exports=function(t){return"Symbol("+(void 0===t?"":t)+")_"+a(++s+r,36)}},3440:(t,e,i)=>{var n=i(7080),s=i(4402),r=i(9286),a=i(5170),o=i(3789),l=i(8469),h=i(507),c=s.has,d=s.remove;t.exports=function difference(t){var e=n(this),i=o(t),s=r(e);a(e)<=i.size?l(e,(function(t){i.includes(t)&&d(s,t)})):h(i.getIterator(),(function(t){c(s,t)&&d(s,t)}));return s}},3463:t=>{var e=TypeError;t.exports=function(t){if("string"==typeof t)return t;throw new e("Argument is not a string")}},3506:(t,e,i)=>{var n=i(3925),s=String,r=TypeError;t.exports=function(t){if(n(t))return t;throw new r("Can't set "+s(t)+" as a prototype")}},3579:(t,e,i)=>{var n=i(6518),s=i(9565),r=i(2652),a=i(9306),o=i(8551),l=i(1767),h=i(9539),c=i(4549)("some",TypeError);n({target:"Iterator",proto:!0,real:!0,forced:c},{some:function some(t){o(this);try{a(t)}catch(t){h(this,"throw",t)}if(c)return s(c,this,t);var e=l(this),i=0;return r(e,(function(e,n){if(t(e,i++))return n()}),{IS_RECORD:!0,INTERRUPTED:!0}).stopped}})},3650:(t,e,i)=>{var n=i(7080),s=i(4402),r=i(9286),a=i(3789),o=i(507),l=s.add,h=s.has,c=s.remove;t.exports=function symmetricDifference(t){var e=n(this),i=a(t).getIterator(),s=r(e);o(i,(function(t){h(e,t)?c(s,t):l(s,t)}));return s}},3706:(t,e,i)=>{var n=i(9504),s=i(4901),r=i(7629),a=n(Function.toString);s(r.inspectSource)||(r.inspectSource=function(t){return a(t)});t.exports=r.inspectSource},3717:(t,e)=>{e.f=Object.getOwnPropertySymbols},3724:(t,e,i)=>{var n=i(9039);t.exports=!n((function(){return 7!==Object.defineProperty({},1,{get:function(){return 7}})[1]}))},3789:(t,e,i)=>{var n=i(9306),s=i(8551),r=i(9565),a=i(1291),o=i(1767),l="Invalid size",h=RangeError,c=TypeError,d=Math.max,SetRecord=function(t,e){this.set=t;this.size=d(e,0);this.has=n(t.has);this.keys=n(t.keys)};SetRecord.prototype={getIterator:function(){return o(s(r(this.keys,this.set)))},includes:function(t){return r(this.has,this.set,t)}};t.exports=function(t){s(t);var e=+t.size;if(e!=e)throw new c(l);var i=a(e);if(i<0)throw new h(l);return new SetRecord(t,i)}},3838:(t,e,i)=>{var n=i(7080),s=i(5170),r=i(8469),a=i(3789);t.exports=function isSubsetOf(t){var e=n(this),i=a(t);return!(s(e)>i.size)&&!1!==r(e,(function(t){if(!i.includes(t))return!1}),!0)}},3853:(t,e,i)=>{var n=i(6518),s=i(4449);n({target:"Set",proto:!0,real:!0,forced:!i(4916)("isDisjointFrom",(function(t){return!t}))},{isDisjointFrom:s})},3925:(t,e,i)=>{var n=i(34);t.exports=function(t){return n(t)||null===t}},3972:(t,e,i)=>{var n=i(34),s=String,r=TypeError;t.exports=function(t){if(void 0===t||n(t))return t;throw new r(s(t)+" is not an object or undefined")}},4055:(t,e,i)=>{var n=i(4576),s=i(34),r=n.document,a=s(r)&&s(r.createElement);t.exports=function(t){return a?r.createElement(t):{}}},4114:(t,e,i)=>{var n=i(6518),s=i(8981),r=i(6198),a=i(4527),o=i(6837);n({target:"Array",proto:!0,arity:1,forced:i(9039)((function(){return 4294967297!==[].push.call({length:4294967296},1)}))||!function(){try{Object.defineProperty([],"length",{writable:!1}).push()}catch(t){return t instanceof TypeError}}()},{push:function push(t){var e=s(this),i=r(e),n=arguments.length;o(i+n);for(var l=0;l<n;l++){e[i]=arguments[l];i++}a(e,i);return i}})},4117:t=>{t.exports=function(t){return null==t}},4149:t=>{var e=RangeError;t.exports=function(t){if(t==t)return t;throw new e("NaN is not allowed")}},4154:(t,e,i)=>{var n=i(6955),s=TypeError;t.exports=function(t){if("Uint8Array"===n(t))return t;throw new s("Argument is not an Uint8Array")}},4204:(t,e,i)=>{var n=i(7080),s=i(4402).add,r=i(9286),a=i(3789),o=i(507);t.exports=function union(t){var e=n(this),i=a(t).getIterator(),l=r(e);o(i,(function(t){s(l,t)}));return l}},4209:(t,e,i)=>{var n=i(8227),s=i(6269),r=n("iterator"),a=Array.prototype;t.exports=function(t){return void 0!==t&&(s.Array===t||a[r]===t)}},4215:(t,e,i)=>{var n=i(4576),s=i(2839),r=i(2195),userAgentStartsWith=function(t){return s.slice(0,t.length)===t};t.exports=userAgentStartsWith("Bun/")?"BUN":userAgentStartsWith("Cloudflare-Workers")?"CLOUDFLARE":userAgentStartsWith("Deno/")?"DENO":userAgentStartsWith("Node.js/")?"NODE":n.Bun&&"string"==typeof Bun.version?"BUN":n.Deno&&"object"==typeof Deno.version?"DENO":"process"===r(n.process)?"NODE":n.window&&n.document?"BROWSER":"REST"},4235:(t,e,i)=>{var n=i(6518),s=i(9504),r=i(2652),a=RangeError,o=TypeError,l=1/0,h=Math.abs,c=Math.pow,d=s([].push),u=c(2,1023),p=c(2,53)-1,g=Number.MAX_VALUE,f=c(2,971),m={},b={},v={},w={},y={},twosum=function(t,e){var i=t+e;return{hi:i,lo:e-(i-t)}};n({target:"Math",stat:!0},{sumPrecise:function sumPrecise(t){var e=[],i=0,n=w;r(t,(function(t){if(++i>=p)throw new a("Maximum allowed index exceeded");if("number"!=typeof t)throw new o("Value is not a number");if(n!==m)if(t!=t)n=m;else if(t===l)n=n===b?m:v;else if(t===-1/0)n=n===v?m:b;else if(!(0===t&&1/t!==l||n!==w&&n!==y)){n=y;d(e,t)}}));switch(n){case m:return NaN;case b:return-1/0;case v:return l;case w:return-0}for(var s,c,A,x,_,E,S=[],C=0,T=0;T<e.length;T++){s=e[T];for(var M=0,D=0;D<S.length;D++){c=S[D];if(h(s)<h(c)){E=s;s=c;c=E}x=(A=twosum(s,c)).hi;_=A.lo;if(h(x)===l){var P=x===l?1:-1;C+=P;if(h(s=s-P*u-P*u)<h(c)){E=s;s=c;c=E}x=(A=twosum(s,c)).hi;_=A.lo}0!==_&&(S[M++]=_);s=x}S.length=M;0!==s&&d(S,s)}var k=S.length-1;x=0;_=0;if(0!==C){var I=k>=0?S[k]:0;k--;if(h(C)>1||C>0&&I>0||C<0&&I<0)return C>0?l:-1/0;x=(A=twosum(C*u,I/2)).hi;_=A.lo;_*=2;if(h(2*x)===l)return x>0?x===u&&_===-f/2&&k>=0&&S[k]<0?g:l:x===-u&&_===f/2&&k>=0&&S[k]>0?-g:-1/0;if(0!==_){S[++k]=_;_=0}x*=2}for(;k>=0;){x=(A=twosum(x,S[k--])).hi;if(0!==(_=A.lo))break}k>=0&&(_<0&&S[k]<0||_>0&&S[k]>0)&&(c=2*_)===(s=x+c)-x&&(x=s);return x}})},4270:(t,e,i)=>{var n=i(9565),s=i(4901),r=i(34),a=TypeError;t.exports=function(t,e){var i,o;if("string"===e&&s(i=t.toString)&&!r(o=n(i,t)))return o;if(s(i=t.valueOf)&&!r(o=n(i,t)))return o;if("string"!==e&&s(i=t.toString)&&!r(o=n(i,t)))return o;throw new a("Can't convert object to primitive value")}},4376:(t,e,i)=>{var n=i(2195);t.exports=Array.isArray||function isArray(t){return"Array"===n(t)}},4402:(t,e,i)=>{var n=i(9504),s=Set.prototype;t.exports={Set,add:n(s.add),has:n(s.has),remove:n(s.delete),proto:s}},4449:(t,e,i)=>{var n=i(7080),s=i(4402).has,r=i(5170),a=i(3789),o=i(8469),l=i(507),h=i(9539);t.exports=function isDisjointFrom(t){var e=n(this),i=a(t);if(r(e)<=i.size)return!1!==o(e,(function(t){if(i.includes(t))return!1}),!0);var c=i.getIterator();return!1!==l(c,(function(t){if(s(e,t))return h(c,"normal",!1)}))}},4483:(t,e,i)=>{var n,s,r,a,o=i(4576),l=i(9429),h=i(1548),c=o.structuredClone,d=o.ArrayBuffer,u=o.MessageChannel,p=!1;if(h)p=function(t){c(t,{transfer:[t]})};else if(d)try{u||(n=l("worker_threads"))&&(u=n.MessageChannel);if(u){s=new u;r=new d(2);a=function(t){s.port1.postMessage(null,[t])};if(2===r.byteLength){a(r);0===r.byteLength&&(p=a)}}}catch(t){}t.exports=p},4495:(t,e,i)=>{var n=i(9519),s=i(9039),r=i(4576).String;t.exports=!!Object.getOwnPropertySymbols&&!s((function(){var t=Symbol("symbol detection");return!r(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&n&&n<41}))},4527:(t,e,i)=>{var n=i(3724),s=i(4376),r=TypeError,a=Object.getOwnPropertyDescriptor,o=n&&!function(){if(void 0!==this)return!0;try{Object.defineProperty([],"length",{writable:!1}).length=1}catch(t){return t instanceof TypeError}}();t.exports=o?function(t,e){if(s(t)&&!a(t,"length").writable)throw new r("Cannot set read only .length");return t.length=e}:function(t,e){return t.length=e}},4549:(t,e,i)=>{var n=i(4576);t.exports=function(t,e){var i=n.Iterator,s=i&&i.prototype,r=s&&s[t],a=!1;if(r)try{r.call({next:function(){return{done:!0}},return:function(){a=!0}},-1)}catch(t){t instanceof e||(a=!1)}if(!a)return r}},4576:function(t){var check=function(t){return t&&t.Math===Math&&t};t.exports=check("object"==typeof globalThis&&globalThis)||check("object"==typeof window&&window)||check("object"==typeof self&&self)||check("object"==typeof global&&global)||check("object"==typeof this&&this)||function(){return this}()||Function("return this")()},4603:(t,e,i)=>{var n=i(6840),s=i(9504),r=i(655),a=i(2812),o=URLSearchParams,l=o.prototype,h=s(l.append),c=s(l.delete),d=s(l.forEach),u=s([].push),p=new o("a=1&a=2&b=3");p.delete("a",1);p.delete("b",void 0);p+""!="a=2"&&n(l,"delete",(function(t){var e=arguments.length,i=e<2?void 0:arguments[1];if(e&&void 0===i)return c(this,t);var n=[];d(this,(function(t,e){u(n,{key:e,value:t})}));a(e,1);for(var s,o=r(t),l=r(i),p=0,g=0,f=!1,m=n.length;p<m;){s=n[p++];if(f||s.key===o){f=!0;c(this,s.key)}else g++}for(;g<m;)(s=n[g++]).key===o&&s.value===l||h(this,s.key,s.value)}),{enumerable:!0,unsafe:!0})},4628:(t,e,i)=>{var n=i(6518),s=i(6043);n({target:"Promise",stat:!0},{withResolvers:function withResolvers(){var t=s.f(this);return{promise:t.promise,resolve:t.resolve,reject:t.reject}}})},4644:(t,e,i)=>{var n,s,r,a=i(7811),o=i(3724),l=i(4576),h=i(4901),c=i(34),d=i(9297),u=i(6955),p=i(6823),g=i(6699),f=i(6840),m=i(2106),b=i(1625),v=i(2787),w=i(2967),y=i(8227),A=i(3392),x=i(1181),_=x.enforce,E=x.get,S=l.Int8Array,C=S&&S.prototype,T=l.Uint8ClampedArray,M=T&&T.prototype,D=S&&v(S),P=C&&v(C),k=Object.prototype,I=l.TypeError,R=y("toStringTag"),L=A("TYPED_ARRAY_TAG"),O="TypedArrayConstructor",N=a&&!!w&&"Opera"!==u(l.opera),B=!1,U={Int8Array:1,Uint8Array:1,Uint8ClampedArray:1,Int16Array:2,Uint16Array:2,Int32Array:4,Uint32Array:4,Float32Array:4,Float64Array:8},H={BigInt64Array:8,BigUint64Array:8},getTypedArrayConstructor=function(t){var e=v(t);if(c(e)){var i=E(e);return i&&d(i,O)?i[O]:getTypedArrayConstructor(e)}},isTypedArray=function(t){if(!c(t))return!1;var e=u(t);return d(U,e)||d(H,e)};for(n in U)(r=(s=l[n])&&s.prototype)?_(r)[O]=s:N=!1;for(n in H)(r=(s=l[n])&&s.prototype)&&(_(r)[O]=s);if(!N||!h(D)||D===Function.prototype){D=function TypedArray(){throw new I("Incorrect invocation")};if(N)for(n in U)l[n]&&w(l[n],D)}if(!N||!P||P===k){P=D.prototype;if(N)for(n in U)l[n]&&w(l[n].prototype,P)}N&&v(M)!==P&&w(M,P);if(o&&!d(P,R)){B=!0;m(P,R,{configurable:!0,get:function(){return c(this)?this[L]:void 0}});for(n in U)l[n]&&g(l[n],L,n)}t.exports={NATIVE_ARRAY_BUFFER_VIEWS:N,TYPED_ARRAY_TAG:B&&L,aTypedArray:function(t){if(isTypedArray(t))return t;throw new I("Target is not a typed array")},aTypedArrayConstructor:function(t){if(h(t)&&(!w||b(D,t)))return t;throw new I(p(t)+" is not a typed array constructor")},exportTypedArrayMethod:function(t,e,i,n){if(o){if(i)for(var s in U){var r=l[s];if(r&&d(r.prototype,t))try{delete r.prototype[t]}catch(i){try{r.prototype[t]=e}catch(t){}}}P[t]&&!i||f(P,t,i?e:N&&C[t]||e,n)}},exportTypedArrayStaticMethod:function(t,e,i){var n,s;if(o){if(w){if(i)for(n in U)if((s=l[n])&&d(s,t))try{delete s[t]}catch(t){}if(D[t]&&!i)return;try{return f(D,t,i?e:N&&D[t]||e)}catch(t){}}for(n in U)!(s=l[n])||s[t]&&!i||f(s,t,e)}},getTypedArrayConstructor,isView:function isView(t){if(!c(t))return!1;var e=u(t);return"DataView"===e||d(U,e)||d(H,e)},isTypedArray,TypedArray:D,TypedArrayPrototype:P}},4659:(t,e,i)=>{var n=i(3724),s=i(4913),r=i(6980);t.exports=function(t,e,i){n?s.f(t,e,r(0,i)):t[e]=i}},4901:t=>{var e="object"==typeof document&&document.all;t.exports=void 0===e&&void 0!==e?function(t){return"function"==typeof t||t===e}:function(t){return"function"==typeof t}},4913:(t,e,i)=>{var n=i(3724),s=i(5917),r=i(8686),a=i(8551),o=i(6969),l=TypeError,h=Object.defineProperty,c=Object.getOwnPropertyDescriptor,d="enumerable",u="configurable",p="writable";e.f=n?r?function defineProperty(t,e,i){a(t);e=o(e);a(i);if("function"==typeof t&&"prototype"===e&&"value"in i&&p in i&&!i[p]){var n=c(t,e);if(n&&n[p]){t[e]=i.value;i={configurable:u in i?i[u]:n[u],enumerable:d in i?i[d]:n[d],writable:!1}}}return h(t,e,i)}:h:function defineProperty(t,e,i){a(t);e=o(e);a(i);if(s)try{return h(t,e,i)}catch(t){}if("get"in i||"set"in i)throw new l("Accessors not supported");"value"in i&&(t[e]=i.value);return t}},4916:(t,e,i)=>{var n=i(7751),createSetLike=function(t){return{size:t,has:function(){return!1},keys:function(){return{next:function(){return{done:!0}}}}}},createSetLikeWithInfinitySize=function(t){return{size:t,has:function(){return!0},keys:function(){throw new Error("e")}}};t.exports=function(t,e){var i=n("Set");try{(new i)[t](createSetLike(0));try{(new i)[t](createSetLike(-1));return!1}catch(n){if(!e)return!0;try{(new i)[t](createSetLikeWithInfinitySize(-1/0));return!1}catch(n){var s=new i;s.add(1);s.add(2);return e(s[t](createSetLikeWithInfinitySize(1/0)))}}}catch(t){return!1}}},4979:(t,e,i)=>{var n=i(6518),s=i(4576),r=i(7751),a=i(6980),o=i(4913).f,l=i(9297),h=i(679),c=i(3167),d=i(2603),u=i(5002),p=i(8574),g=i(3724),f=i(6395),m="DOMException",b=r("Error"),v=r(m),w=function DOMException(){h(this,y);var t=arguments.length,e=d(t<1?void 0:arguments[0]),i=d(t<2?void 0:arguments[1],"Error"),n=new v(e,i),s=new b(e);s.name=m;o(n,"stack",a(1,p(s.stack,1)));c(n,this,w);return n},y=w.prototype=v.prototype,A="stack"in new b(m),x="stack"in new v(1,2),_=v&&g&&Object.getOwnPropertyDescriptor(s,m),E=!(!_||_.writable&&_.configurable),S=A&&!E&&!x;n({global:!0,constructor:!0,forced:f||S},{DOMException:S?w:v});var C=r(m),T=C.prototype;if(T.constructor!==C){f||o(T,"constructor",a(1,C));for(var M in u)if(l(u,M)){var D=u[M],P=D.s;l(C,P)||o(C,P,a(6,D.c))}}},5002:t=>{t.exports={IndexSizeError:{s:"INDEX_SIZE_ERR",c:1,m:1},DOMStringSizeError:{s:"DOMSTRING_SIZE_ERR",c:2,m:0},HierarchyRequestError:{s:"HIERARCHY_REQUEST_ERR",c:3,m:1},WrongDocumentError:{s:"WRONG_DOCUMENT_ERR",c:4,m:1},InvalidCharacterError:{s:"INVALID_CHARACTER_ERR",c:5,m:1},NoDataAllowedError:{s:"NO_DATA_ALLOWED_ERR",c:6,m:0},NoModificationAllowedError:{s:"NO_MODIFICATION_ALLOWED_ERR",c:7,m:1},NotFoundError:{s:"NOT_FOUND_ERR",c:8,m:1},NotSupportedError:{s:"NOT_SUPPORTED_ERR",c:9,m:1},InUseAttributeError:{s:"INUSE_ATTRIBUTE_ERR",c:10,m:1},InvalidStateError:{s:"INVALID_STATE_ERR",c:11,m:1},SyntaxError:{s:"SYNTAX_ERR",c:12,m:1},InvalidModificationError:{s:"INVALID_MODIFICATION_ERR",c:13,m:1},NamespaceError:{s:"NAMESPACE_ERR",c:14,m:1},InvalidAccessError:{s:"INVALID_ACCESS_ERR",c:15,m:1},ValidationError:{s:"VALIDATION_ERR",c:16,m:0},TypeMismatchError:{s:"TYPE_MISMATCH_ERR",c:17,m:1},SecurityError:{s:"SECURITY_ERR",c:18,m:1},NetworkError:{s:"NETWORK_ERR",c:19,m:1},AbortError:{s:"ABORT_ERR",c:20,m:1},URLMismatchError:{s:"URL_MISMATCH_ERR",c:21,m:1},QuotaExceededError:{s:"QUOTA_EXCEEDED_ERR",c:22,m:1},TimeoutError:{s:"TIMEOUT_ERR",c:23,m:1},InvalidNodeTypeError:{s:"INVALID_NODE_TYPE_ERR",c:24,m:1},DataCloneError:{s:"DATA_CLONE_ERR",c:25,m:1}}},5024:(t,e,i)=>{var n=i(6518),s=i(3650),r=i(9835);n({target:"Set",proto:!0,real:!0,forced:!i(4916)("symmetricDifference")||!r("symmetricDifference")},{symmetricDifference:s})},5031:(t,e,i)=>{var n=i(7751),s=i(9504),r=i(8480),a=i(3717),o=i(8551),l=s([].concat);t.exports=n("Reflect","ownKeys")||function ownKeys(t){var e=r.f(o(t)),i=a.f;return i?l(e,i(t)):e}},5169:(t,e,i)=>{var n=i(3238),s=TypeError;t.exports=function(t){if(n(t))throw new s("ArrayBuffer is detached");return t}},5170:(t,e,i)=>{var n=i(6706),s=i(4402);t.exports=n(s.proto,"size","get")||function(t){return t.size}},5370:(t,e,i)=>{var n=i(6198);t.exports=function(t,e,i){for(var s=0,r=arguments.length>2?i:n(e),a=new t(r);r>s;)a[s]=e[s++];return a}},5397:(t,e,i)=>{var n=i(7055),s=i(7750);t.exports=function(t){return n(s(t))}},5610:(t,e,i)=>{var n=i(1291),s=Math.max,r=Math.min;t.exports=function(t,e){var i=n(t);return i<0?s(i+e,0):r(i,e)}},5623:(t,e,i)=>{var n=i(6518),s=i(4576),r=i(9504),a=i(4154),o=i(5169),l=r(1.1.toString),h=s.Uint8Array,c=!h||!h.prototype.toHex||!function(){try{return"ffffffffffffffff"===new h([255,255,255,255,255,255,255,255]).toHex()}catch(t){return!1}}();h&&n({target:"Uint8Array",proto:!0,forced:c},{toHex:function toHex(){a(this);o(this.buffer);for(var t="",e=0,i=this.length;e<i;e++){var n=l(this[e],16);t+=1===n.length?"0"+n:n}return t}})},5636:(t,e,i)=>{var n=i(4576),s=i(9504),r=i(6706),a=i(7696),o=i(5169),l=i(7394),h=i(4483),c=i(1548),d=n.structuredClone,u=n.ArrayBuffer,p=n.DataView,g=Math.min,f=u.prototype,m=p.prototype,b=s(f.slice),v=r(f,"resizable","get"),w=r(f,"maxByteLength","get"),y=s(m.getInt8),A=s(m.setInt8);t.exports=(c||h)&&function(t,e,i){var n,s=l(t),r=void 0===e?s:a(e),f=!v||!v(t);o(t);if(c){t=d(t,{transfer:[t]});if(s===r&&(i||f))return t}if(s>=r&&(!i||f))n=b(t,0,r);else{var m=i&&!f&&w?{maxByteLength:w(t)}:void 0;n=new u(r,m);for(var x=new p(t),_=new p(n),E=g(r,s),S=0;S<E;S++)A(_,S,y(x,S))}c||h(t);return n}},5745:(t,e,i)=>{var n=i(7629);t.exports=function(t,e){return n[t]||(n[t]=e||{})}},5781:(t,e,i)=>{var n=i(6518),s=i(7751),r=i(2812),a=i(655),o=i(7416),l=s("URL");n({target:"URL",stat:!0,forced:!o},{parse:function parse(t){var e=r(arguments.length,1),i=a(t),n=e<2||void 0===arguments[1]?void 0:a(arguments[1]);try{return new l(i,n)}catch(t){return null}}})},5854:(t,e,i)=>{var n=i(2777),s=TypeError;t.exports=function(t){var e=n(t,"number");if("number"==typeof e)throw new s("Can't convert number to bigint");return BigInt(e)}},5876:(t,e,i)=>{var n=i(6518),s=i(3838);n({target:"Set",proto:!0,real:!0,forced:!i(4916)("isSubsetOf",(function(t){return t}))},{isSubsetOf:s})},5917:(t,e,i)=>{var n=i(3724),s=i(9039),r=i(4055);t.exports=!n&&!s((function(){return 7!==Object.defineProperty(r("div"),"a",{get:function(){return 7}}).a}))},5966:(t,e,i)=>{var n=i(9306),s=i(4117);t.exports=function(t,e){var i=t[e];return s(i)?void 0:n(i)}},6043:(t,e,i)=>{var n=i(9306),s=TypeError,PromiseCapability=function(t){var e,i;this.promise=new t((function(t,n){if(void 0!==e||void 0!==i)throw new s("Bad Promise constructor");e=t;i=n}));this.resolve=n(e);this.reject=n(i)};t.exports.f=function(t){return new PromiseCapability(t)}},6080:(t,e,i)=>{var n=i(7476),s=i(9306),r=i(616),a=n(n.bind);t.exports=function(t,e){s(t);return void 0===e?t:r?a(t,e):function(){return t.apply(e,arguments)}}},6119:(t,e,i)=>{var n=i(5745),s=i(3392),r=n("keys");t.exports=function(t){return r[t]||(r[t]=s(t))}},6193:(t,e,i)=>{var n=i(4215);t.exports="NODE"===n},6198:(t,e,i)=>{var n=i(8014);t.exports=function(t){return n(t.length)}},6269:t=>{t.exports={}},6279:(t,e,i)=>{var n=i(6840);t.exports=function(t,e,i){for(var s in e)n(t,s,e[s],i);return t}},6319:(t,e,i)=>{var n=i(8551),s=i(9539);t.exports=function(t,e,i,r){try{return r?e(n(i)[0],i[1]):e(i)}catch(e){s(t,"throw",e)}}},6395:t=>{t.exports=!1},6518:(t,e,i)=>{var n=i(4576),s=i(7347).f,r=i(6699),a=i(6840),o=i(9433),l=i(7740),h=i(2796);t.exports=function(t,e){var i,c,d,u,p,g=t.target,f=t.global,m=t.stat;if(i=f?n:m?n[g]||o(g,{}):n[g]&&n[g].prototype)for(c in e){u=e[c];d=t.dontCallGetSet?(p=s(i,c))&&p.value:i[c];if(!h(f?c:g+(m?".":"#")+c,t.forced)&&void 0!==d){if(typeof u==typeof d)continue;l(u,d)}(t.sham||d&&d.sham)&&r(u,"sham",!0);a(i,c,u,t)}}},6573:(t,e,i)=>{var n=i(3724),s=i(2106),r=i(3238),a=ArrayBuffer.prototype;n&&!("detached"in a)&&s(a,"detached",{configurable:!0,get:function detached(){return r(this)}})},6699:(t,e,i)=>{var n=i(3724),s=i(4913),r=i(6980);t.exports=n?function(t,e,i){return s.f(t,e,r(1,i))}:function(t,e,i){t[e]=i;return t}},6706:(t,e,i)=>{var n=i(9504),s=i(9306);t.exports=function(t,e,i){try{return n(s(Object.getOwnPropertyDescriptor(t,e)[i]))}catch(t){}}},6801:(t,e,i)=>{var n=i(3724),s=i(8686),r=i(4913),a=i(8551),o=i(5397),l=i(1072);e.f=n&&!s?Object.defineProperties:function defineProperties(t,e){a(t);for(var i,n=o(e),s=l(e),h=s.length,c=0;h>c;)r.f(t,i=s[c++],n[i]);return t}},6823:t=>{var e=String;t.exports=function(t){try{return e(t)}catch(t){return"Object"}}},6837:t=>{var e=TypeError;t.exports=function(t){if(t>9007199254740991)throw e("Maximum allowed index exceeded");return t}},6840:(t,e,i)=>{var n=i(4901),s=i(4913),r=i(283),a=i(9433);t.exports=function(t,e,i,o){o||(o={});var l=o.enumerable,h=void 0!==o.name?o.name:e;n(i)&&r(i,h,o);if(o.global)l?t[e]=i:a(e,i);else{try{o.unsafe?t[e]&&(l=!0):delete t[e]}catch(t){}l?t[e]=i:s.f(t,e,{value:i,enumerable:!1,configurable:!o.nonConfigurable,writable:!o.nonWritable})}return t}},6955:(t,e,i)=>{var n=i(2140),s=i(4901),r=i(2195),a=i(8227)("toStringTag"),o=Object,l="Arguments"===r(function(){return arguments}());t.exports=n?r:function(t){var e,i,n;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(i=function(t,e){try{return t[e]}catch(t){}}(e=o(t),a))?i:l?r(e):"Object"===(n=r(e))&&s(e.callee)?"Arguments":n}},6969:(t,e,i)=>{var n=i(2777),s=i(757);t.exports=function(t){var e=n(t,"string");return s(e)?e:e+""}},6980:t=>{t.exports=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}}},7040:(t,e,i)=>{var n=i(4495);t.exports=n&&!Symbol.sham&&"symbol"==typeof Symbol.iterator},7055:(t,e,i)=>{var n=i(9504),s=i(9039),r=i(2195),a=Object,o=n("".split);t.exports=s((function(){return!a("z").propertyIsEnumerable(0)}))?function(t){return"String"===r(t)?o(t,""):a(t)}:a},7080:(t,e,i)=>{var n=i(4402).has;t.exports=function(t){n(t);return t}},7347:(t,e,i)=>{var n=i(3724),s=i(9565),r=i(8773),a=i(6980),o=i(5397),l=i(6969),h=i(9297),c=i(5917),d=Object.getOwnPropertyDescriptor;e.f=n?d:function getOwnPropertyDescriptor(t,e){t=o(t);e=l(e);if(c)try{return d(t,e)}catch(t){}if(h(t,e))return a(!s(r.f,t,e),t[e])}},7394:(t,e,i)=>{var n=i(4576),s=i(6706),r=i(2195),a=n.ArrayBuffer,o=n.TypeError;t.exports=a&&s(a.prototype,"byteLength","get")||function(t){if("ArrayBuffer"!==r(t))throw new o("ArrayBuffer expected");return t.byteLength}},7416:(t,e,i)=>{var n=i(9039),s=i(8227),r=i(3724),a=i(6395),o=s("iterator");t.exports=!n((function(){var t=new URL("b?a=1&b=2&c=3","https://a"),e=t.searchParams,i=new URLSearchParams("a=1&a=2&b=3"),n="";t.pathname="c%20d";e.forEach((function(t,i){e.delete("b");n+=i+t}));i.delete("a",2);i.delete("b",void 0);return a&&(!t.toJSON||!i.has("a",1)||i.has("a",2)||!i.has("a",void 0)||i.has("b"))||!e.size&&(a||!r)||!e.sort||"https://a/c%20d?a=1&c=3"!==t.href||"3"!==e.get("c")||"a=1"!==String(new URLSearchParams("?a=1"))||!e[o]||"a"!==new URL("https://a@b").username||"b"!==new URLSearchParams(new URLSearchParams("a=b")).get("a")||"xn--e1aybc"!==new URL("https://тест").host||"#%D0%B1"!==new URL("https://a#б").hash||"a1c3"!==n||"x"!==new URL("https://x",void 0).host}))},7476:(t,e,i)=>{var n=i(2195),s=i(9504);t.exports=function(t){if("Function"===n(t))return s(t)}},7566:(t,e,i)=>{var n=i(6840),s=i(9504),r=i(655),a=i(2812),o=URLSearchParams,l=o.prototype,h=s(l.getAll),c=s(l.has),d=new o("a=1");!d.has("a",2)&&d.has("a",void 0)||n(l,"has",(function has(t){var e=arguments.length,i=e<2?void 0:arguments[1];if(e&&void 0===i)return c(this,t);var n=h(this,t);a(e,1);for(var s=r(i),o=0;o<n.length;)if(n[o++]===s)return!0;return!1}),{enumerable:!0,unsafe:!0})},7629:(t,e,i)=>{var n=i(6395),s=i(4576),r=i(9433),a="__core-js_shared__",o=t.exports=s[a]||r(a,{});(o.versions||(o.versions=[])).push({version:"3.44.0",mode:n?"pure":"global",copyright:"© 2014-2025 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.44.0/LICENSE",source:"https://github.com/zloirock/core-js"})},7642:(t,e,i)=>{var n=i(6518),s=i(3440),r=i(9039);n({target:"Set",proto:!0,real:!0,forced:!i(4916)("difference",(function(t){return 0===t.size}))||r((function(){var t={size:1,has:function(){return!0},keys:function(){var t=0;return{next:function(){var i=t++>1;e.has(1)&&e.clear();return{done:i,value:2}}}}},e=new Set([1,2,3,4]);return 3!==e.difference(t).size}))},{difference:s})},7657:(t,e,i)=>{var n,s,r,a=i(9039),o=i(4901),l=i(34),h=i(2360),c=i(2787),d=i(6840),u=i(8227),p=i(6395),g=u("iterator"),f=!1;[].keys&&("next"in(r=[].keys())?(s=c(c(r)))!==Object.prototype&&(n=s):f=!0);!l(n)||a((function(){var t={};return n[g].call(t)!==t}))?n={}:p&&(n=h(n));o(n[g])||d(n,g,(function(){return this}));t.exports={IteratorPrototype:n,BUGGY_SAFARI_ITERATORS:f}},7680:(t,e,i)=>{var n=i(9504);t.exports=n([].slice)},7696:(t,e,i)=>{var n=i(1291),s=i(8014),r=RangeError;t.exports=function(t){if(void 0===t)return 0;var e=n(t),i=s(e);if(e!==i)throw new r("Wrong length or index");return i}},7740:(t,e,i)=>{var n=i(9297),s=i(5031),r=i(7347),a=i(4913);t.exports=function(t,e,i){for(var o=s(e),l=a.f,h=r.f,c=0;c<o.length;c++){var d=o[c];n(t,d)||i&&n(i,d)||l(t,d,h(e,d))}}},7750:(t,e,i)=>{var n=i(4117),s=TypeError;t.exports=function(t){if(n(t))throw new s("Can't call method on "+t);return t}},7751:(t,e,i)=>{var n=i(4576),s=i(4901);t.exports=function(t,e){return arguments.length<2?(i=n[t],s(i)?i:void 0):n[t]&&n[t][e];var i}},7811:t=>{t.exports="undefined"!=typeof ArrayBuffer&&"undefined"!=typeof DataView},7936:(t,e,i)=>{var n=i(6518),s=i(5636);s&&n({target:"ArrayBuffer",proto:!0},{transferToFixedLength:function transferToFixedLength(){return s(this,arguments.length?arguments[0]:void 0,!1)}})},8004:(t,e,i)=>{var n=i(6518),s=i(9039),r=i(8750);n({target:"Set",proto:!0,real:!0,forced:!i(4916)("intersection",(function(t){return 2===t.size&&t.has(1)&&t.has(2)}))||s((function(){return"3,2"!==String(Array.from(new Set([1,2,3]).intersection(new Set([3,2]))))}))},{intersection:r})},8014:(t,e,i)=>{var n=i(1291),s=Math.min;t.exports=function(t){var e=n(t);return e>0?s(e,9007199254740991):0}},8100:(t,e,i)=>{var n=i(6518),s=i(5636);s&&n({target:"ArrayBuffer",proto:!0},{transfer:function transfer(){return s(this,arguments.length?arguments[0]:void 0,!0)}})},8111:(t,e,i)=>{var n=i(6518),s=i(4576),r=i(679),a=i(8551),o=i(4901),l=i(2787),h=i(2106),c=i(4659),d=i(9039),u=i(9297),p=i(8227),g=i(7657).IteratorPrototype,f=i(3724),m=i(6395),b="constructor",v="Iterator",w=p("toStringTag"),y=TypeError,A=s[v],x=m||!o(A)||A.prototype!==g||!d((function(){A({})})),_=function Iterator(){r(this,g);if(l(this)===g)throw new y("Abstract class Iterator not directly constructable")},defineIteratorPrototypeAccessor=function(t,e){f?h(g,t,{configurable:!0,get:function(){return e},set:function(e){a(this);if(this===g)throw new y("You can't redefine this property");u(this,t)?this[t]=e:c(this,t,e)}}):g[t]=e};u(g,w)||defineIteratorPrototypeAccessor(w,v);!x&&u(g,b)&&g[b]!==Object||defineIteratorPrototypeAccessor(b,_);_.prototype=g;n({global:!0,constructor:!0,forced:x},{Iterator:_})},8227:(t,e,i)=>{var n=i(4576),s=i(5745),r=i(9297),a=i(3392),o=i(4495),l=i(7040),h=n.Symbol,c=s("wks"),d=l?h.for||h:h&&h.withoutSetter||a;t.exports=function(t){r(c,t)||(c[t]=o&&r(h,t)?h[t]:d("Symbol."+t));return c[t]}},8235:(t,e,i)=>{var n=i(9504),s=i(9297),r=SyntaxError,a=parseInt,o=String.fromCharCode,l=n("".charAt),h=n("".slice),c=n(/./.exec),d={'\\"':'"',"\\\\":"\\","\\/":"/","\\b":"\b","\\f":"\f","\\n":"\n","\\r":"\r","\\t":"\t"},u=/^[\da-f]{4}$/i,p=/^[\u0000-\u001F]$/;t.exports=function(t,e){for(var i=!0,n="";e<t.length;){var g=l(t,e);if("\\"===g){var f=h(t,e,e+2);if(s(d,f)){n+=d[f];e+=2}else{if("\\u"!==f)throw new r('Unknown escape sequence: "'+f+'"');var m=h(t,e+=2,e+4);if(!c(u,m))throw new r("Bad Unicode escape at: "+e);n+=o(a(m,16));e+=4}}else{if('"'===g){i=!1;e++;break}if(c(p,g))throw new r("Bad control character in string literal at: "+e);n+=g;e++}}if(i)throw new r("Unterminated string at: "+e);return{value:n,end:e}}},8237:(t,e,i)=>{var n=i(6518),s=i(2652),r=i(9306),a=i(8551),o=i(1767),l=i(9539),h=i(4549),c=i(8745),d=i(9039),u=TypeError,p=d((function(){[].keys().reduce((function(){}),void 0)})),g=!p&&h("reduce",u);n({target:"Iterator",proto:!0,real:!0,forced:p||g},{reduce:function reduce(t){a(this);try{r(t)}catch(t){l(this,"throw",t)}var e=arguments.length<2,i=e?void 0:arguments[1];if(g)return c(g,this,e?[t]:[t,i]);var n=o(this),h=0;s(n,(function(n){if(e){e=!1;i=n}else i=t(i,n,h);h++}),{IS_RECORD:!0});if(e)throw new u("Reduce of empty iterator with no initial value");return i}})},8335:(t,e,i)=>{var n=i(6518),s=i(3724),r=i(4576),a=i(7751),o=i(9504),l=i(9565),h=i(4901),c=i(34),d=i(4376),u=i(9297),p=i(655),g=i(6198),f=i(4659),m=i(9039),b=i(8235),v=i(4495),w=r.JSON,y=r.Number,A=r.SyntaxError,x=w&&w.parse,_=a("Object","keys"),E=Object.getOwnPropertyDescriptor,S=o("".charAt),C=o("".slice),T=o(/./.exec),M=o([].push),D=/^\d$/,P=/^[1-9]$/,k=/^[\d-]$/,I=/^[\t\n\r ]$/,internalize=function(t,e,i,n){var s,r,a,o,h,p=t[e],f=n&&p===n.value,m=f&&"string"==typeof n.source?{source:n.source}:{};if(c(p)){var b=d(p),v=f?n.nodes:b?[]:{};if(b){s=v.length;a=g(p);for(o=0;o<a;o++)internalizeProperty(p,o,internalize(p,""+o,i,o<s?v[o]:void 0))}else{r=_(p);a=g(r);for(o=0;o<a;o++){h=r[o];internalizeProperty(p,h,internalize(p,h,i,u(v,h)?v[h]:void 0))}}}return l(i,t,e,p,m)},internalizeProperty=function(t,e,i){if(s){var n=E(t,e);if(n&&!n.configurable)return}void 0===i?delete t[e]:f(t,e,i)},Node=function(t,e,i,n){this.value=t;this.end=e;this.source=i;this.nodes=n},Context=function(t,e){this.source=t;this.index=e};Context.prototype={fork:function(t){return new Context(this.source,t)},parse:function(){var t=this.source,e=this.skip(I,this.index),i=this.fork(e),n=S(t,e);if(T(k,n))return i.number();switch(n){case"{":return i.object();case"[":return i.array();case'"':return i.string();case"t":return i.keyword(!0);case"f":return i.keyword(!1);case"n":return i.keyword(null)}throw new A('Unexpected character: "'+n+'" at: '+e)},node:function(t,e,i,n,s){return new Node(e,n,t?null:C(this.source,i,n),s)},object:function(){for(var t=this.source,e=this.index+1,i=!1,n={},s={};e<t.length;){e=this.until(['"',"}"],e);if("}"===S(t,e)&&!i){e++;break}var r=this.fork(e).string(),a=r.value;e=r.end;e=this.until([":"],e)+1;e=this.skip(I,e);r=this.fork(e).parse();f(s,a,r);f(n,a,r.value);e=this.until([",","}"],r.end);var o=S(t,e);if(","===o){i=!0;e++}else if("}"===o){e++;break}}return this.node(1,n,this.index,e,s)},array:function(){for(var t=this.source,e=this.index+1,i=!1,n=[],s=[];e<t.length;){e=this.skip(I,e);if("]"===S(t,e)&&!i){e++;break}var r=this.fork(e).parse();M(s,r);M(n,r.value);e=this.until([",","]"],r.end);if(","===S(t,e)){i=!0;e++}else if("]"===S(t,e)){e++;break}}return this.node(1,n,this.index,e,s)},string:function(){var t=this.index,e=b(this.source,this.index+1);return this.node(0,e.value,t,e.end)},number:function(){var t=this.source,e=this.index,i=e;"-"===S(t,i)&&i++;if("0"===S(t,i))i++;else{if(!T(P,S(t,i)))throw new A("Failed to parse number at: "+i);i=this.skip(D,i+1)}"."===S(t,i)&&(i=this.skip(D,i+1));if("e"===S(t,i)||"E"===S(t,i)){i++;"+"!==S(t,i)&&"-"!==S(t,i)||i++;if(i===(i=this.skip(D,i)))throw new A("Failed to parse number's exponent value at: "+i)}return this.node(0,y(C(t,e,i)),e,i)},keyword:function(t){var e=""+t,i=this.index,n=i+e.length;if(C(this.source,i,n)!==e)throw new A("Failed to parse value at: "+i);return this.node(0,t,i,n)},skip:function(t,e){for(var i=this.source;e<i.length&&T(t,S(i,e));e++);return e},until:function(t,e){e=this.skip(I,e);for(var i=S(this.source,e),n=0;n<t.length;n++)if(t[n]===i)return e;throw new A('Unexpected character: "'+i+'" at: '+e)}};var R=m((function(){var t,e="9007199254740993";x(e,(function(e,i,n){t=n.source}));return t!==e})),L=v&&!m((function(){return 1/x("-0 \t")!=-1/0}));n({target:"JSON",stat:!0,forced:R},{parse:function parse(t,e){return L&&!h(e)?x(t):function(t,e){t=p(t);var i=new Context(t,0,""),n=i.parse(),s=n.value,r=i.skip(I,n.end);if(r<t.length)throw new A('Unexpected extra character: "'+S(t,r)+'" after the parsed data at: '+r);return h(e)?internalize({"":s},"",e,n):s}(t,e)}})},8469:(t,e,i)=>{var n=i(9504),s=i(507),r=i(4402),a=r.Set,o=r.proto,l=n(o.forEach),h=n(o.keys),c=h(new a).next;t.exports=function(t,e,i){return i?s({iterator:h(t),next:c},e):l(t,e)}},8480:(t,e,i)=>{var n=i(1828),s=i(8727).concat("length","prototype");e.f=Object.getOwnPropertyNames||function getOwnPropertyNames(t){return n(t,s)}},8527:(t,e,i)=>{var n=i(7080),s=i(4402).has,r=i(5170),a=i(3789),o=i(507),l=i(9539);t.exports=function isSupersetOf(t){var e=n(this),i=a(t);if(r(e)<i.size)return!1;var h=i.getIterator();return!1!==o(h,(function(t){if(!s(e,t))return l(h,"normal",!1)}))}},8551:(t,e,i)=>{var n=i(34),s=String,r=TypeError;t.exports=function(t){if(n(t))return t;throw new r(s(t)+" is not an object")}},8574:(t,e,i)=>{var n=i(9504),s=Error,r=n("".replace),a=String(new s("zxcasd").stack),o=/\n\s*at [^:]*:[^\n]*/,l=o.test(a);t.exports=function(t,e){if(l&&"string"==typeof t&&!s.prepareStackTrace)for(;e--;)t=r(t,o,"");return t}},8622:(t,e,i)=>{var n=i(4576),s=i(4901),r=n.WeakMap;t.exports=s(r)&&/native code/.test(String(r))},8646:(t,e,i)=>{var n=i(9565),s=i(8551),r=i(1767),a=i(851);t.exports=function(t,e){e&&"string"==typeof t||s(t);var i=a(t);return r(s(void 0!==i?n(i,t):t))}},8686:(t,e,i)=>{var n=i(3724),s=i(9039);t.exports=n&&s((function(){return 42!==Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype}))},8721:(t,e,i)=>{var n=i(3724),s=i(9504),r=i(2106),a=URLSearchParams.prototype,o=s(a.forEach);n&&!("size"in a)&&r(a,"size",{get:function size(){var t=0;o(this,(function(){t++}));return t},configurable:!0,enumerable:!0})},8727:t=>{t.exports=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"]},8745:(t,e,i)=>{var n=i(616),s=Function.prototype,r=s.apply,a=s.call;t.exports="object"==typeof Reflect&&Reflect.apply||(n?a.bind(r):function(){return a.apply(r,arguments)})},8750:(t,e,i)=>{var n=i(7080),s=i(4402),r=i(5170),a=i(3789),o=i(8469),l=i(507),h=s.Set,c=s.add,d=s.has;t.exports=function intersection(t){var e=n(this),i=a(t),s=new h;r(e)>i.size?l(i.getIterator(),(function(t){d(e,t)&&c(s,t)})):o(e,(function(t){i.includes(t)&&c(s,t)}));return s}},8773:(t,e)=>{var i={}.propertyIsEnumerable,n=Object.getOwnPropertyDescriptor,s=n&&!i.call({1:2},1);e.f=s?function propertyIsEnumerable(t){var e=n(this,t);return!!e&&e.enumerable}:i},8981:(t,e,i)=>{var n=i(7750),s=Object;t.exports=function(t){return s(n(t))}},9039:t=>{t.exports=function(t){try{return!!t()}catch(t){return!0}}},9143:(t,e,i)=>{var n=i(4576),s=i(9504),r=i(3972),a=i(3463),o=i(9297),l=i(2804),h=i(944),c=i(5169),d=l.c2i,u=l.c2iUrl,p=n.SyntaxError,g=n.TypeError,f=s("".charAt),skipAsciiWhitespace=function(t,e){for(var i=t.length;e<i;e++){var n=f(t,e);if(" "!==n&&"\t"!==n&&"\n"!==n&&"\f"!==n&&"\r"!==n)break}return e},decodeBase64Chunk=function(t,e,i){var n=t.length;n<4&&(t+=2===n?"AA":"A");var s=(e[f(t,0)]<<18)+(e[f(t,1)]<<12)+(e[f(t,2)]<<6)+e[f(t,3)],r=[s>>16&255,s>>8&255,255&s];if(2===n){if(i&&0!==r[1])throw new p("Extra bits");return[r[0]]}if(3===n){if(i&&0!==r[2])throw new p("Extra bits");return[r[0],r[1]]}return r},writeBytes=function(t,e,i){for(var n=e.length,s=0;s<n;s++)t[i+s]=e[s];return i+n};t.exports=function(t,e,i,n){a(t);r(e);var s="base64"===h(e)?d:u,l=e?e.lastChunkHandling:void 0;void 0===l&&(l="loose");if("loose"!==l&&"strict"!==l&&"stop-before-partial"!==l)throw new g("Incorrect `lastChunkHandling` option");i&&c(i.buffer);var m=i||[],b=0,v=0,w="",y=0;if(n)for(;;){if((y=skipAsciiWhitespace(t,y))===t.length){if(w.length>0){if("stop-before-partial"===l)break;if("loose"!==l)throw new p("Missing padding");if(1===w.length)throw new p("Malformed padding: exactly one additional character");b=writeBytes(m,decodeBase64Chunk(w,s,!1),b)}v=t.length;break}var A=f(t,y);++y;if("="===A){if(w.length<2)throw new p("Padding is too early");y=skipAsciiWhitespace(t,y);if(2===w.length){if(y===t.length){if("stop-before-partial"===l)break;throw new p("Malformed padding: only one =")}if("="===f(t,y)){++y;y=skipAsciiWhitespace(t,y)}}if(y<t.length)throw new p("Unexpected character after padding");b=writeBytes(m,decodeBase64Chunk(w,s,"strict"===l),b);v=t.length;break}if(!o(s,A))throw new p("Unexpected character");var x=n-b;if(1===x&&2===w.length||2===x&&3===w.length)break;if(4===(w+=A).length){b=writeBytes(m,decodeBase64Chunk(w,s,!1),b);w="";v=y;if(b===n)break}}return{bytes:m,read:v,written:b}}},9286:(t,e,i)=>{var n=i(4402),s=i(8469),r=n.Set,a=n.add;t.exports=function(t){var e=new r;s(t,(function(t){a(e,t)}));return e}},9297:(t,e,i)=>{var n=i(9504),s=i(8981),r=n({}.hasOwnProperty);t.exports=Object.hasOwn||function hasOwn(t,e){return r(s(t),e)}},9306:(t,e,i)=>{var n=i(4901),s=i(6823),r=TypeError;t.exports=function(t){if(n(t))return t;throw new r(s(t)+" is not a function")}},9314:(t,e,i)=>{var n=i(6518),s=i(9565),r=i(8551),a=i(1767),o=i(4149),l=i(9590),h=i(9539),c=i(9462),d=i(684),u=i(4549),p=i(6395),g=!p&&!d("drop",0),f=!p&&!g&&u("drop",RangeError),m=p||g||f,b=c((function(){for(var t,e=this.iterator,i=this.next;this.remaining;){this.remaining--;t=r(s(i,e));if(this.done=!!t.done)return}t=r(s(i,e));if(!(this.done=!!t.done))return t.value}));n({target:"Iterator",proto:!0,real:!0,forced:m},{drop:function drop(t){r(this);var e;try{e=l(o(+t))}catch(t){h(this,"throw",t)}return f?s(f,this,e):new b(a(this),{remaining:e})}})},9429:(t,e,i)=>{var n=i(4576),s=i(6193);t.exports=function(t){if(s){try{return n.process.getBuiltinModule(t)}catch(t){}try{return Function('return require("'+t+'")')()}catch(t){}}}},9432:(t,e,i)=>{var n=i(6518),s=i(4576),r=i(5370),a=i(9143),o=s.Uint8Array,l=!o||!o.fromBase64||!function(){try{o.fromBase64("",null)}catch(t){return!0}}();o&&n({target:"Uint8Array",stat:!0,forced:l},{fromBase64:function fromBase64(t){var e=a(t,arguments.length>1?arguments[1]:void 0,null,9007199254740991);return r(o,e.bytes)}})},9433:(t,e,i)=>{var n=i(4576),s=Object.defineProperty;t.exports=function(t,e){try{s(n,t,{value:e,configurable:!0,writable:!0})}catch(i){n[t]=e}return e}},9462:(t,e,i)=>{var n=i(9565),s=i(2360),r=i(6699),a=i(6279),o=i(8227),l=i(1181),h=i(5966),c=i(7657).IteratorPrototype,d=i(2529),u=i(9539),p=i(1385),g=o("toStringTag"),f="IteratorHelper",m="WrapForValidIterator",b="normal",v="throw",w=l.set,createIteratorProxyPrototype=function(t){var e=l.getterFor(t?m:f);return a(s(c),{next:function next(){var i=e(this);if(t)return i.nextHandler();if(i.done)return d(void 0,!0);try{var n=i.nextHandler();return i.returnHandlerResult?n:d(n,i.done)}catch(t){i.done=!0;throw t}},return:function(){var i=e(this),s=i.iterator;i.done=!0;if(t){var r=h(s,"return");return r?n(r,s):d(void 0,!0)}if(i.inner)try{u(i.inner.iterator,b)}catch(t){return u(s,v,t)}if(i.openIters)try{p(i.openIters,b)}catch(t){return u(s,v,t)}s&&u(s,b);return d(void 0,!0)}})},y=createIteratorProxyPrototype(!0),A=createIteratorProxyPrototype(!1);r(A,g,"Iterator Helper");t.exports=function(t,e,i){var n=function Iterator(n,s){if(s){s.iterator=n.iterator;s.next=n.next}else s=n;s.type=e?m:f;s.returnHandlerResult=!!i;s.nextHandler=t;s.counter=0;s.done=!1;w(this,s)};n.prototype=e?y:A;return n}},9504:(t,e,i)=>{var n=i(616),s=Function.prototype,r=s.call,a=n&&s.bind.bind(r,r);t.exports=n?a:function(t){return function(){return r.apply(t,arguments)}}},9519:(t,e,i)=>{var n,s,r=i(4576),a=i(2839),o=r.process,l=r.Deno,h=o&&o.versions||l&&l.version,c=h&&h.v8;c&&(s=(n=c.split("."))[0]>0&&n[0]<4?1:+(n[0]+n[1]));!s&&a&&(!(n=a.match(/Edge\/(\d+)/))||n[1]>=74)&&(n=a.match(/Chrome\/(\d+)/))&&(s=+n[1]);t.exports=s},9539:(t,e,i)=>{var n=i(9565),s=i(8551),r=i(5966);t.exports=function(t,e,i){var a,o;s(t);try{if(!(a=r(t,"return"))){if("throw"===e)throw i;return i}a=n(a,t)}catch(t){o=!0;a=t}if("throw"===e)throw i;if(o)throw a;s(a);return i}},9565:(t,e,i)=>{var n=i(616),s=Function.prototype.call;t.exports=n?s.bind(s):function(){return s.apply(s,arguments)}},9577:(t,e,i)=>{var n=i(9928),s=i(4644),r=i(1108),a=i(1291),o=i(5854),l=s.aTypedArray,h=s.getTypedArrayConstructor,c=s.exportTypedArrayMethod,d=function(){try{new Int8Array(1).with(2,{valueOf:function(){throw 8}})}catch(t){return 8===t}}(),u=d&&function(){try{new Int8Array(1).with(-.5,1)}catch(t){return!0}}();c("with",{with:function(t,e){var i=l(this),s=a(t),c=r(i)?o(e):+e;return n(i,h(i),s,c)}}.with,!d||u)},9590:(t,e,i)=>{var n=i(1291),s=RangeError;t.exports=function(t){var e=n(t);if(e<0)throw new s("The argument can't be less than 0");return e}},9617:(t,e,i)=>{var n=i(5397),s=i(5610),r=i(6198),createMethod=function(t){return function(e,i,a){var o=n(e),l=r(o);if(0===l)return!t&&-1;var h,c=s(a,l);if(t&&i!=i){for(;l>c;)if((h=o[c++])!=h)return!0}else for(;l>c;c++)if((t||c in o)&&o[c]===i)return t||c||0;return!t&&-1}};t.exports={includes:createMethod(!0),indexOf:createMethod(!1)}},9631:(t,e,i)=>{var n=i(6518),s=i(4576),r=i(9504),a=i(3972),o=i(4154),l=i(5169),h=i(2804),c=i(944),d=h.i2c,u=h.i2cUrl,p=r("".charAt),g=s.Uint8Array,f=!g||!g.prototype.toBase64||!function(){try{(new g).toBase64(null)}catch(t){return!0}}();g&&n({target:"Uint8Array",proto:!0,forced:f},{toBase64:function toBase64(){var t=o(this),e=arguments.length?a(arguments[0]):void 0,i="base64"===c(e)?d:u,n=!!e&&!!e.omitPadding;l(this.buffer);for(var s,r="",h=0,g=t.length,at=function(t){return p(i,s>>6*t&63)};h+2<g;h+=3){s=(t[h]<<16)+(t[h+1]<<8)+t[h+2];r+=at(3)+at(2)+at(1)+at(0)}if(h+2===g){s=(t[h]<<16)+(t[h+1]<<8);r+=at(3)+at(2)+at(1)+(n?"":"=")}else if(h+1===g){s=t[h]<<16;r+=at(3)+at(2)+(n?"":"==")}return r}})},9797:(t,e,i)=>{var n=i(6518),s=i(4576),r=i(3463),a=i(4154),o=i(5169),l=i(2303);s.Uint8Array&&n({target:"Uint8Array",proto:!0},{setFromHex:function setFromHex(t){a(this);r(t);o(this.buffer);var e=l(t,this).read;return{read:e,written:e/2}}})},9835:t=>{t.exports=function(t){try{var e=new Set,i={size:0,has:function(){return!0},keys:function(){return Object.defineProperty({},"next",{get:function(){e.clear();e.add(4);return function(){return{done:!0}}}})}},n=e[t](i);return 1===n.size&&4===n.values().next().value}catch(t){return!1}}},9928:(t,e,i)=>{var n=i(6198),s=i(1291),r=RangeError;t.exports=function(t,e,i,a){var o=n(t),l=s(i),h=l<0?o+l:l;if(h>=o||h<0)throw new r("Incorrect index");for(var c=new e(o),d=0;d<o;d++)c[d]=d===h?a:t[d];return c}}},e={};function __webpack_require__(i){var n=e[i];if(void 0!==n)return n.exports;var s=e[i]={exports:{}};t[i].call(s.exports,s,s.exports,__webpack_require__);return s.exports}__webpack_require__(4114),__webpack_require__(6573),__webpack_require__(8100),__webpack_require__(7936),__webpack_require__(8111),__webpack_require__(8237),__webpack_require__(1689),__webpack_require__(9577),__webpack_require__(4235),__webpack_require__(9432),__webpack_require__(1549),__webpack_require__(9797),__webpack_require__(9631),__webpack_require__(5623),__webpack_require__(4979),__webpack_require__(5781);const i=!("object"!=typeof process||process+""!="[object process]"||process.versions.nw||process.versions.electron&&process.type&&"browser"!==process.type),n=[.001,0,0,.001,0,0],s=1.35,r=1,a=2,o=4,l=16,h=32,c=64,d=128,u=256,p={DISABLE:0,ENABLE:1,ENABLE_FORMS:2,ENABLE_STORAGE:3},g="pdfjs_internal_editor_",f={DISABLE:-1,NONE:0,FREETEXT:3,HIGHLIGHT:9,STAMP:13,INK:15,SIGNATURE:101,COMMENT:102},m={RESIZE:1,CREATE:2,FREETEXT_SIZE:11,FREETEXT_COLOR:12,FREETEXT_OPACITY:13,INK_COLOR:21,INK_THICKNESS:22,INK_OPACITY:23,HIGHLIGHT_COLOR:31,HIGHLIGHT_THICKNESS:32,HIGHLIGHT_FREE:33,HIGHLIGHT_SHOW_ALL:34,DRAW_STEP:41},b={PRINT:4,MODIFY_CONTENTS:8,COPY:16,MODIFY_ANNOTATIONS:32,FILL_INTERACTIVE_FORMS:256,COPY_FOR_ACCESSIBILITY:512,ASSEMBLE:1024,PRINT_HIGH_QUALITY:2048},v=0,w=1,y=2,A=3,x=3,_=4,E={GRAYSCALE_1BPP:1,RGB_24BPP:2,RGBA_32BPP:3},S={TEXT:1,LINK:2,FREETEXT:3,LINE:4,SQUARE:5,CIRCLE:6,POLYGON:7,POLYLINE:8,HIGHLIGHT:9,UNDERLINE:10,SQUIGGLY:11,STRIKEOUT:12,STAMP:13,CARET:14,INK:15,POPUP:16,FILEATTACHMENT:17,SOUND:18,MOVIE:19,WIDGET:20,SCREEN:21,PRINTERMARK:22,TRAPNET:23,WATERMARK:24,THREED:25,REDACT:26},C=1,T=2,M=3,D=4,P=5,k={ERRORS:0,WARNINGS:1,INFOS:5},I={dependency:1,setLineWidth:2,setLineCap:3,setLineJoin:4,setMiterLimit:5,setDash:6,setRenderingIntent:7,setFlatness:8,setGState:9,save:10,restore:11,transform:12,moveTo:13,lineTo:14,curveTo:15,curveTo2:16,curveTo3:17,closePath:18,rectangle:19,stroke:20,closeStroke:21,fill:22,eoFill:23,fillStroke:24,eoFillStroke:25,closeFillStroke:26,closeEOFillStroke:27,endPath:28,clip:29,eoClip:30,beginText:31,endText:32,setCharSpacing:33,setWordSpacing:34,setHScale:35,setLeading:36,setFont:37,setTextRenderingMode:38,setTextRise:39,moveText:40,setLeadingMoveText:41,setTextMatrix:42,nextLine:43,showText:44,showSpacedText:45,nextLineShowText:46,nextLineSetSpacingShowText:47,setCharWidth:48,setCharWidthAndBounds:49,setStrokeColorSpace:50,setFillColorSpace:51,setStrokeColor:52,setStrokeColorN:53,setFillColor:54,setFillColorN:55,setStrokeGray:56,setFillGray:57,setStrokeRGBColor:58,setFillRGBColor:59,setStrokeCMYKColor:60,setFillCMYKColor:61,shadingFill:62,beginInlineImage:63,beginImageData:64,endInlineImage:65,paintXObject:66,markPoint:67,markPointProps:68,beginMarkedContent:69,beginMarkedContentProps:70,endMarkedContent:71,beginCompat:72,endCompat:73,paintFormXObjectBegin:74,paintFormXObjectEnd:75,beginGroup:76,endGroup:77,beginAnnotation:80,endAnnotation:81,paintImageMaskXObject:83,paintImageMaskXObjectGroup:84,paintImageXObject:85,paintInlineImageXObject:86,paintInlineImageXObjectGroup:87,paintImageXObjectRepeat:88,paintImageMaskXObjectRepeat:89,paintSolidColorImageMask:90,constructPath:91,setStrokeTransparent:92,setFillTransparent:93,rawFillPath:94},R=0,L=1,O=2,N=3,B={NEED_PASSWORD:1,INCORRECT_PASSWORD:2};let U=k.WARNINGS;function setVerbosityLevel(t){Number.isInteger(t)&&(U=t)}function getVerbosityLevel(){return U}function info(t){U>=k.INFOS&&console.log(`Info: ${t}`)}function warn(t){U>=k.WARNINGS&&console.log(`Warning: ${t}`)}function unreachable(t){throw new Error(t)}function assert(t,e){t||unreachable(e)}function createValidAbsoluteUrl(t,e=null,i=null){if(!t)return null;if(i&&"string"==typeof t){if(i.addDefaultProtocol&&t.startsWith("www.")){const e=t.match(/\./g);e?.length>=2&&(t=`http://${t}`)}if(i.tryConvertEncoding)try{t=function stringToUTF8String(t){return decodeURIComponent(escape(t))}(t)}catch{}}const n=e?URL.parse(t,e):URL.parse(t);return function _isValidProtocol(t){switch(t?.protocol){case"http:":case"https:":case"ftp:":case"mailto:":case"tel:":return!0;default:return!1}}(n)?n:null}function updateUrlHash(t,e,i=!1){const n=URL.parse(t);if(n){n.hash=e;return n.href}return i&&createValidAbsoluteUrl(t,"http://example.com")?t.split("#",1)[0]+""+(e?`#${e}`:""):""}function shadow(t,e,i,n=!1){Object.defineProperty(t,e,{value:i,enumerable:!n,configurable:!0,writable:!1});return i}const H=function BaseExceptionClosure(){function BaseException(t,e){this.message=t;this.name=e}BaseException.prototype=new Error;BaseException.constructor=BaseException;return BaseException}();class PasswordException extends H{constructor(t,e){super(t,"PasswordException");this.code=e}}class UnknownErrorException extends H{constructor(t,e){super(t,"UnknownErrorException");this.details=e}}class InvalidPDFException extends H{constructor(t){super(t,"InvalidPDFException")}}class ResponseException extends H{constructor(t,e,i){super(t,"ResponseException");this.status=e;this.missing=i}}class FormatError extends H{constructor(t){super(t,"FormatError")}}class AbortException extends H{constructor(t){super(t,"AbortException")}}function bytesToString(t){"object"==typeof t&&void 0!==t?.length||unreachable("Invalid argument for bytesToString");const e=t.length,i=8192;if(e<i)return String.fromCharCode.apply(null,t);const n=[];for(let s=0;s<e;s+=i){const r=Math.min(s+i,e),a=t.subarray(s,r);n.push(String.fromCharCode.apply(null,a))}return n.join("")}function stringToBytes(t){"string"!=typeof t&&unreachable("Invalid argument for stringToBytes");const e=t.length,i=new Uint8Array(e);for(let n=0;n<e;++n)i[n]=255&t.charCodeAt(n);return i}class util_FeatureTest{static get isLittleEndian(){return shadow(this,"isLittleEndian",function isLittleEndian(){const t=new Uint8Array(4);t[0]=1;return 1===new Uint32Array(t.buffer,0,1)[0]}())}static get isEvalSupported(){return shadow(this,"isEvalSupported",function isEvalSupported(){try{new Function("");return!0}catch{return!1}}())}static get isOffscreenCanvasSupported(){return shadow(this,"isOffscreenCanvasSupported","undefined"!=typeof OffscreenCanvas)}static get isImageDecoderSupported(){return shadow(this,"isImageDecoderSupported","undefined"!=typeof ImageDecoder)}static get platform(){const{platform:t,userAgent:e}=navigator;return shadow(this,"platform",{isAndroid:e.includes("Android"),isLinux:t.includes("Linux"),isMac:t.includes("Mac"),isWindows:t.includes("Win"),isFirefox:e.includes("Firefox")})}static get isCSSRoundSupported(){return shadow(this,"isCSSRoundSupported",globalThis.CSS?.supports?.("width: round(1.5px, 1px)"))}}const z=Array.from(Array(256).keys(),(t=>t.toString(16).padStart(2,"0")));class Util{static makeHexColor(t,e,i){return`#${z[t]}${z[e]}${z[i]}`}static scaleMinMax(t,e){let i;if(t[0]){if(t[0]<0){i=e[0];e[0]=e[2];e[2]=i}e[0]*=t[0];e[2]*=t[0];if(t[3]<0){i=e[1];e[1]=e[3];e[3]=i}e[1]*=t[3];e[3]*=t[3]}else{i=e[0];e[0]=e[1];e[1]=i;i=e[2];e[2]=e[3];e[3]=i;if(t[1]<0){i=e[1];e[1]=e[3];e[3]=i}e[1]*=t[1];e[3]*=t[1];if(t[2]<0){i=e[0];e[0]=e[2];e[2]=i}e[0]*=t[2];e[2]*=t[2]}e[0]+=t[4];e[1]+=t[5];e[2]+=t[4];e[3]+=t[5]}static transform(t,e){return[t[0]*e[0]+t[2]*e[1],t[1]*e[0]+t[3]*e[1],t[0]*e[2]+t[2]*e[3],t[1]*e[2]+t[3]*e[3],t[0]*e[4]+t[2]*e[5]+t[4],t[1]*e[4]+t[3]*e[5]+t[5]]}static applyTransform(t,e,i=0){const n=t[i],s=t[i+1];t[i]=n*e[0]+s*e[2]+e[4];t[i+1]=n*e[1]+s*e[3]+e[5]}static applyTransformToBezier(t,e,i=0){const n=e[0],s=e[1],r=e[2],a=e[3],o=e[4],l=e[5];for(let e=0;e<6;e+=2){const h=t[i+e],c=t[i+e+1];t[i+e]=h*n+c*r+o;t[i+e+1]=h*s+c*a+l}}static applyInverseTransform(t,e){const i=t[0],n=t[1],s=e[0]*e[3]-e[1]*e[2];t[0]=(i*e[3]-n*e[2]+e[2]*e[5]-e[4]*e[3])/s;t[1]=(-i*e[1]+n*e[0]+e[4]*e[1]-e[5]*e[0])/s}static axialAlignedBoundingBox(t,e,i){const n=e[0],s=e[1],r=e[2],a=e[3],o=e[4],l=e[5],h=t[0],c=t[1],d=t[2],u=t[3];let p=n*h+o,g=p,f=n*d+o,m=f,b=a*c+l,v=b,w=a*u+l,y=w;if(0!==s||0!==r){const t=s*h,e=s*d,i=r*c,n=r*u;p+=i;m+=i;f+=n;g+=n;b+=t;y+=t;w+=e;v+=e}i[0]=Math.min(i[0],p,f,g,m);i[1]=Math.min(i[1],b,w,v,y);i[2]=Math.max(i[2],p,f,g,m);i[3]=Math.max(i[3],b,w,v,y)}static inverseTransform(t){const e=t[0]*t[3]-t[1]*t[2];return[t[3]/e,-t[1]/e,-t[2]/e,t[0]/e,(t[2]*t[5]-t[4]*t[3])/e,(t[4]*t[1]-t[5]*t[0])/e]}static singularValueDecompose2dScale(t,e){const i=t[0],n=t[1],s=t[2],r=t[3],a=i**2+n**2,o=i*s+n*r,l=s**2+r**2,h=(a+l)/2,c=Math.sqrt(h**2-(a*l-o**2));e[0]=Math.sqrt(h+c||1);e[1]=Math.sqrt(h-c||1)}static normalizeRect(t){const e=t.slice(0);if(t[0]>t[2]){e[0]=t[2];e[2]=t[0]}if(t[1]>t[3]){e[1]=t[3];e[3]=t[1]}return e}static intersect(t,e){const i=Math.max(Math.min(t[0],t[2]),Math.min(e[0],e[2])),n=Math.min(Math.max(t[0],t[2]),Math.max(e[0],e[2]));if(i>n)return null;const s=Math.max(Math.min(t[1],t[3]),Math.min(e[1],e[3])),r=Math.min(Math.max(t[1],t[3]),Math.max(e[1],e[3]));return s>r?null:[i,s,n,r]}static pointBoundingBox(t,e,i){i[0]=Math.min(i[0],t);i[1]=Math.min(i[1],e);i[2]=Math.max(i[2],t);i[3]=Math.max(i[3],e)}static rectBoundingBox(t,e,i,n,s){s[0]=Math.min(s[0],t,i);s[1]=Math.min(s[1],e,n);s[2]=Math.max(s[2],t,i);s[3]=Math.max(s[3],e,n)}static#t(t,e,i,n,s,r,a,o,l,h){if(l<=0||l>=1)return;const c=1-l,d=l*l,u=d*l,p=c*(c*(c*t+3*l*e)+3*d*i)+u*n,g=c*(c*(c*s+3*l*r)+3*d*a)+u*o;h[0]=Math.min(h[0],p);h[1]=Math.min(h[1],g);h[2]=Math.max(h[2],p);h[3]=Math.max(h[3],g)}static#e(t,e,i,n,s,r,a,o,l,h,c,d){if(Math.abs(l)<1e-12){Math.abs(h)>=1e-12&&this.#t(t,e,i,n,s,r,a,o,-c/h,d);return}const u=h**2-4*c*l;if(u<0)return;const p=Math.sqrt(u),g=2*l;this.#t(t,e,i,n,s,r,a,o,(-h+p)/g,d);this.#t(t,e,i,n,s,r,a,o,(-h-p)/g,d)}static bezierBoundingBox(t,e,i,n,s,r,a,o,l){l[0]=Math.min(l[0],t,a);l[1]=Math.min(l[1],e,o);l[2]=Math.max(l[2],t,a);l[3]=Math.max(l[3],e,o);this.#e(t,i,s,a,e,n,r,o,3*(3*(i-s)-t+a),6*(t-2*i+s),3*(i-t),l);this.#e(t,i,s,a,e,n,r,o,3*(3*(n-r)-e+o),6*(e-2*n+r),3*(n-e),l)}}let j=null,G=null;function normalizeUnicode(t){if(!j){j=/([\u00a0\u00b5\u037e\u0eb3\u2000-\u200a\u202f\u2126\ufb00-\ufb04\ufb06\ufb20-\ufb36\ufb38-\ufb3c\ufb3e\ufb40-\ufb41\ufb43-\ufb44\ufb46-\ufba1\ufba4-\ufba9\ufbae-\ufbb1\ufbd3-\ufbdc\ufbde-\ufbe7\ufbea-\ufbf8\ufbfc-\ufbfd\ufc00-\ufc5d\ufc64-\ufcf1\ufcf5-\ufd3d\ufd88\ufdf4\ufdfa-\ufdfb\ufe71\ufe77\ufe79\ufe7b\ufe7d]+)|(\ufb05+)/gu;G=new Map([["ﬅ","ſt"]])}return t.replaceAll(j,((t,e,i)=>e?e.normalize("NFKC"):G.get(i)))}function getUuid(){if("function"==typeof crypto.randomUUID)return crypto.randomUUID();const t=new Uint8Array(32);crypto.getRandomValues(t);return bytesToString(t)}const W="pdfjs_internal_id_";function MathClamp(t,e,i){return Math.min(Math.max(t,e),i)}function toBase64Util(t){return Uint8Array.prototype.toBase64?t.toBase64():btoa(bytesToString(t))}"function"!=typeof Math.sumPrecise&&(Math.sumPrecise=function(t){return t.reduce(((t,e)=>t+e),0)});"function"!=typeof AbortSignal.any&&(AbortSignal.any=function(t){const e=new AbortController,{signal:i}=e;for(const n of t)if(n.aborted){e.abort(n.reason);return i}for(const n of t)n.addEventListener("abort",(()=>{e.abort(n.reason)}),{signal:i});return i});__webpack_require__(1701),__webpack_require__(4628),__webpack_require__(7642),__webpack_require__(8004),__webpack_require__(3853),__webpack_require__(5876),__webpack_require__(2475),__webpack_require__(5024),__webpack_require__(1698),__webpack_require__(4603),__webpack_require__(7566),__webpack_require__(8721),__webpack_require__(9314),__webpack_require__(1148),__webpack_require__(3579),__webpack_require__(8335);const V="http://www.w3.org/2000/svg";class PixelsPerInch{static CSS=96;static PDF=72;static PDF_TO_CSS_UNITS=this.CSS/this.PDF}async function fetchData(t,e="text"){if(isValidFetchUrl(t,document.baseURI)){const i=await fetch(t);if(!i.ok)throw new Error(i.statusText);switch(e){case"arraybuffer":return i.arrayBuffer();case"blob":return i.blob();case"json":return i.json()}return i.text()}return new Promise(((i,n)=>{const s=new XMLHttpRequest;s.open("GET",t,!0);s.responseType=e;s.onreadystatechange=()=>{if(s.readyState===XMLHttpRequest.DONE)if(200!==s.status&&0!==s.status)n(new Error(s.statusText));else{switch(e){case"arraybuffer":case"blob":case"json":i(s.response);return}i(s.responseText)}};s.send(null)}))}class PageViewport{constructor({viewBox:t,userUnit:e,scale:i,rotation:n,offsetX:s=0,offsetY:r=0,dontFlip:a=!1}){this.viewBox=t;this.userUnit=e;this.scale=i;this.rotation=n;this.offsetX=s;this.offsetY=r;i*=e;const o=(t[2]+t[0])/2,l=(t[3]+t[1])/2;let h,c,d,u,p,g,f,m;(n%=360)<0&&(n+=360);switch(n){case 180:h=-1;c=0;d=0;u=1;break;case 90:h=0;c=1;d=1;u=0;break;case 270:h=0;c=-1;d=-1;u=0;break;case 0:h=1;c=0;d=0;u=-1;break;default:throw new Error("PageViewport: Invalid rotation, must be a multiple of 90 degrees.")}if(a){d=-d;u=-u}if(0===h){p=Math.abs(l-t[1])*i+s;g=Math.abs(o-t[0])*i+r;f=(t[3]-t[1])*i;m=(t[2]-t[0])*i}else{p=Math.abs(o-t[0])*i+s;g=Math.abs(l-t[1])*i+r;f=(t[2]-t[0])*i;m=(t[3]-t[1])*i}this.transform=[h*i,c*i,d*i,u*i,p-h*i*o-d*i*l,g-c*i*o-u*i*l];this.width=f;this.height=m}get rawDims(){const t=this.viewBox;return shadow(this,"rawDims",{pageWidth:t[2]-t[0],pageHeight:t[3]-t[1],pageX:t[0],pageY:t[1]})}clone({scale:t=this.scale,rotation:e=this.rotation,offsetX:i=this.offsetX,offsetY:n=this.offsetY,dontFlip:s=!1}={}){return new PageViewport({viewBox:this.viewBox.slice(),userUnit:this.userUnit,scale:t,rotation:e,offsetX:i,offsetY:n,dontFlip:s})}convertToViewportPoint(t,e){const i=[t,e];Util.applyTransform(i,this.transform);return i}convertToViewportRectangle(t){const e=[t[0],t[1]];Util.applyTransform(e,this.transform);const i=[t[2],t[3]];Util.applyTransform(i,this.transform);return[e[0],e[1],i[0],i[1]]}convertToPdfPoint(t,e){const i=[t,e];Util.applyInverseTransform(i,this.transform);return i}}class RenderingCancelledException extends H{constructor(t,e=0){super(t,"RenderingCancelledException");this.extraDelay=e}}function isDataScheme(t){const e=t.length;let i=0;for(;i<e&&""===t[i].trim();)i++;return"data:"===t.substring(i,i+5).toLowerCase()}function isPdfFile(t){return"string"==typeof t&&/\.pdf$/i.test(t)}function getFilenameFromUrl(t){[t]=t.split(/[#?]/,1);return t.substring(t.lastIndexOf("/")+1)}function getPdfFilenameFromUrl(t,e="document.pdf"){if("string"!=typeof t)return e;if(isDataScheme(t)){warn('getPdfFilenameFromUrl: ignore "data:"-URL for performance reasons.');return e}const i=(t=>{try{return new URL(t)}catch{try{return new URL(decodeURIComponent(t))}catch{try{return new URL(t,"https://foo.bar")}catch{try{return new URL(decodeURIComponent(t),"https://foo.bar")}catch{return null}}}}})(t);if(!i)return e;const decode=t=>{try{let e=decodeURIComponent(t);if(e.includes("/")){e=e.split("/").at(-1);return e.test(/^\.pdf$/i)?e:t}return e}catch{return t}},n=/\.pdf$/i,s=i.pathname.split("/").at(-1);if(n.test(s))return decode(s);if(i.searchParams.size>0){const t=Array.from(i.searchParams.values()).reverse();for(const e of t)if(n.test(e))return decode(e);const e=Array.from(i.searchParams.keys()).reverse();for(const t of e)if(n.test(t))return decode(t)}if(i.hash){const t=/[^/?#=]+\.pdf\b(?!.*\.pdf\b)/i.exec(i.hash);if(t)return decode(t[0])}return e}class StatTimer{started=Object.create(null);times=[];time(t){t in this.started&&warn(`Timer is already running for ${t}`);this.started[t]=Date.now()}timeEnd(t){t in this.started||warn(`Timer has not been started for ${t}`);this.times.push({name:t,start:this.started[t],end:Date.now()});delete this.started[t]}toString(){const t=[];let e=0;for(const{name:t}of this.times)e=Math.max(t.length,e);for(const{name:i,start:n,end:s}of this.times)t.push(`${i.padEnd(e)} ${s-n}ms\n`);return t.join("")}}function isValidFetchUrl(t,e){const i=e?URL.parse(t,e):URL.parse(t);return"http:"===i?.protocol||"https:"===i?.protocol}function noContextMenu(t){t.preventDefault()}function stopEvent(t){t.preventDefault();t.stopPropagation()}class PDFDateString{static#i;static toDateObject(t){if(t instanceof Date)return t;if(!t||"string"!=typeof t)return null;this.#i||=new RegExp("^D:(\\d{4})(\\d{2})?(\\d{2})?(\\d{2})?(\\d{2})?(\\d{2})?([Z|+|-])?(\\d{2})?'?(\\d{2})?'?");const e=this.#i.exec(t);if(!e)return null;const i=parseInt(e[1],10);let n=parseInt(e[2],10);n=n>=1&&n<=12?n-1:0;let s=parseInt(e[3],10);s=s>=1&&s<=31?s:1;let r=parseInt(e[4],10);r=r>=0&&r<=23?r:0;let a=parseInt(e[5],10);a=a>=0&&a<=59?a:0;let o=parseInt(e[6],10);o=o>=0&&o<=59?o:0;const l=e[7]||"Z";let h=parseInt(e[8],10);h=h>=0&&h<=23?h:0;let c=parseInt(e[9],10)||0;c=c>=0&&c<=59?c:0;if("-"===l){r+=h;a+=c}else if("+"===l){r-=h;a-=c}return new Date(Date.UTC(i,n,s,r,a,o))}}function getXfaPageViewport(t,{scale:e=1,rotation:i=0}){const{width:n,height:s}=t.attributes.style,r=[0,0,parseInt(n),parseInt(s)];return new PageViewport({viewBox:r,userUnit:1,scale:e,rotation:i})}function getRGB(t){if(t.startsWith("#")){const e=parseInt(t.slice(1),16);return[(16711680&e)>>16,(65280&e)>>8,255&e]}if(t.startsWith("rgb("))return t.slice(4,-1).split(",").map((t=>parseInt(t)));if(t.startsWith("rgba("))return t.slice(5,-1).split(",").map((t=>parseInt(t))).slice(0,3);warn(`Not a valid color format: "${t}"`);return[0,0,0]}function getCurrentTransform(t){const{a:e,b:i,c:n,d:s,e:r,f:a}=t.getTransform();return[e,i,n,s,r,a]}function getCurrentTransformInverse(t){const{a:e,b:i,c:n,d:s,e:r,f:a}=t.getTransform().invertSelf();return[e,i,n,s,r,a]}function setLayerDimensions(t,e,i=!1,n=!0){if(e instanceof PageViewport){const{pageWidth:n,pageHeight:s}=e.rawDims,{style:r}=t,a=util_FeatureTest.isCSSRoundSupported,o=`var(--total-scale-factor) * ${n}px`,l=`var(--total-scale-factor) * ${s}px`,h=a?`round(down, ${o}, var(--scale-round-x))`:`calc(${o})`,c=a?`round(down, ${l}, var(--scale-round-y))`:`calc(${l})`;if(i&&e.rotation%180!=0){r.width=c;r.height=h}else{r.width=h;r.height=c}}n&&t.setAttribute("data-main-rotation",e.rotation)}class OutputScale{constructor(){const{pixelRatio:t}=OutputScale;this.sx=t;this.sy=t}get scaled(){return 1!==this.sx||1!==this.sy}get symmetric(){return this.sx===this.sy}limitCanvas(t,e,i,n,s=-1){let r=1/0,a=1/0,o=1/0;(i=OutputScale.capPixels(i,s))>0&&(r=Math.sqrt(i/(t*e)));if(-1!==n){a=n/t;o=n/e}const l=Math.min(r,a,o);if(this.sx>l||this.sy>l){this.sx=l;this.sy=l;return!0}return!1}static get pixelRatio(){return globalThis.devicePixelRatio||1}static capPixels(t,e){if(e>=0){const i=Math.ceil(window.screen.availWidth*window.screen.availHeight*this.pixelRatio**2*(1+e/100));return t>0?Math.min(t,i):i}return t}}const $=["image/apng","image/avif","image/bmp","image/gif","image/jpeg","image/png","image/svg+xml","image/webp","image/x-icon"];class EditorToolbar{#n=null;#s=null;#r;#a=null;#o=null;#l=null;#h=null;static#c=null;constructor(t){this.#r=t;EditorToolbar.#c||=Object.freeze({freetext:"pdfjs-editor-remove-freetext-button",highlight:"pdfjs-editor-remove-highlight-button",ink:"pdfjs-editor-remove-ink-button",stamp:"pdfjs-editor-remove-stamp-button",signature:"pdfjs-editor-remove-signature-button"})}render(){const t=this.#n=document.createElement("div");t.classList.add("editToolbar","hidden");t.setAttribute("role","toolbar");const e=this.#r._uiManager._signal;t.addEventListener("contextmenu",noContextMenu,{signal:e});t.addEventListener("pointerdown",EditorToolbar.#d,{signal:e});const i=this.#a=document.createElement("div");i.className="buttons";t.append(i);const n=this.#r.toolbarPosition;if(n){const{style:e}=t,i="ltr"===this.#r._uiManager.direction?1-n[0]:n[0];e.insetInlineEnd=100*i+"%";e.top=`calc(${100*n[1]}% + var(--editor-toolbar-vert-offset))`}return t}get div(){return this.#n}static#d(t){t.stopPropagation()}#u(t){this.#r._focusEventsAllowed=!1;stopEvent(t)}#p(t){this.#r._focusEventsAllowed=!0;stopEvent(t)}#g(t){const e=this.#r._uiManager._signal;t.addEventListener("focusin",this.#u.bind(this),{capture:!0,signal:e});t.addEventListener("focusout",this.#p.bind(this),{capture:!0,signal:e});t.addEventListener("contextmenu",noContextMenu,{signal:e})}hide(){this.#n.classList.add("hidden");this.#s?.hideDropdown()}show(){this.#n.classList.remove("hidden");this.#o?.shown();this.#l?.shown()}addDeleteButton(){const{editorType:t,_uiManager:e}=this.#r,i=document.createElement("button");i.className="delete";i.tabIndex=0;i.setAttribute("data-l10n-id",EditorToolbar.#c[t]);this.#g(i);i.addEventListener("click",(t=>{e.delete()}),{signal:e._signal});this.#a.append(i)}get#f(){const t=document.createElement("div");t.className="divider";return t}async addAltText(t){const e=await t.render();this.#g(e);this.#a.append(e,this.#f);this.#o=t}addComment(t){if(this.#l)return;const e=t.render();if(e){this.#g(e);this.#a.prepend(e,this.#f);this.#l=t;t.toolbar=this}}addColorPicker(t){if(this.#s)return;this.#s=t;const e=t.renderButton();this.#g(e);this.#a.append(e,this.#f)}async addEditSignatureButton(t){const e=this.#h=await t.renderEditButton(this.#r);this.#g(e);this.#a.append(e,this.#f)}async addButton(t,e){switch(t){case"colorPicker":this.addColorPicker(e);break;case"altText":await this.addAltText(e);break;case"editSignature":await this.addEditSignatureButton(e);break;case"delete":this.addDeleteButton();break;case"comment":this.addComment(e)}}updateEditSignatureButton(t){this.#h&&(this.#h.title=t)}remove(){this.#n.remove();this.#s?.destroy();this.#s=null}}class HighlightToolbar{#a=null;#n=null;#m;constructor(t){this.#m=t}#b(){const t=this.#n=document.createElement("div");t.className="editToolbar";t.setAttribute("role","toolbar");t.addEventListener("contextmenu",noContextMenu,{signal:this.#m._signal});const e=this.#a=document.createElement("div");e.className="buttons";t.append(e);this.#v();return t}#w(t,e){let i=0,n=0;for(const s of t){const t=s.y+s.height;if(t<i)continue;const r=s.x+(e?s.width:0);if(t>i){n=r;i=t}else e?r>n&&(n=r):r<n&&(n=r)}return[e?1-n:n,i]}show(t,e,i){const[n,s]=this.#w(e,i),{style:r}=this.#n||=this.#b();t.append(this.#n);r.insetInlineEnd=100*n+"%";r.top=`calc(${100*s}% + var(--editor-toolbar-vert-offset))`}hide(){this.#n.remove()}#v(){const t=document.createElement("button");t.className="highlightButton";t.tabIndex=0;t.setAttribute("data-l10n-id","pdfjs-highlight-floating-button1");const e=document.createElement("span");t.append(e);e.className="visuallyHidden";e.setAttribute("data-l10n-id","pdfjs-highlight-floating-button-label");const i=this.#m._signal;t.addEventListener("contextmenu",noContextMenu,{signal:i});t.addEventListener("click",(()=>{this.#m.highlightSelection("floating_button")}),{signal:i});this.#a.append(t)}}function bindEvents(t,e,i){for(const n of i)e.addEventListener(n,t[n].bind(t))}class IdManager{#y=0;get id(){return`${g}${this.#y++}`}}class ImageManager{#A=getUuid();#y=0;#x=null;static get _isSVGFittingCanvas(){const t=new OffscreenCanvas(1,3).getContext("2d",{willReadFrequently:!0}),e=new Image;e.src='data:image/svg+xml;charset=UTF-8,<svg viewBox="0 0 1 1" width="1" height="1" xmlns="http://www.w3.org/2000/svg"><rect width="1" height="1" style="fill:red;"/></svg>';return shadow(this,"_isSVGFittingCanvas",e.decode().then((()=>{t.drawImage(e,0,0,1,1,0,0,1,3);return 0===new Uint32Array(t.getImageData(0,0,1,1).data.buffer)[0]})))}async#_(t,e){this.#x||=new Map;let i=this.#x.get(t);if(null===i)return null;if(i?.bitmap){i.refCounter+=1;return i}try{i||={bitmap:null,id:`image_${this.#A}_${this.#y++}`,refCounter:0,isSvg:!1};let t;if("string"==typeof e){i.url=e;t=await fetchData(e,"blob")}else e instanceof File?t=i.file=e:e instanceof Blob&&(t=e);if("image/svg+xml"===t.type){const e=ImageManager._isSVGFittingCanvas,n=new FileReader,s=new Image,r=new Promise(((t,r)=>{s.onload=()=>{i.bitmap=s;i.isSvg=!0;t()};n.onload=async()=>{const t=i.svgUrl=n.result;s.src=await e?`${t}#svgView(preserveAspectRatio(none))`:t};s.onerror=n.onerror=r}));n.readAsDataURL(t);await r}else i.bitmap=await createImageBitmap(t);i.refCounter=1}catch(t){warn(t);i=null}this.#x.set(t,i);i&&this.#x.set(i.id,i);return i}async getFromFile(t){const{lastModified:e,name:i,size:n,type:s}=t;return this.#_(`${e}_${i}_${n}_${s}`,t)}async getFromUrl(t){return this.#_(t,t)}async getFromBlob(t,e){const i=await e;return this.#_(t,i)}async getFromId(t){this.#x||=new Map;const e=this.#x.get(t);if(!e)return null;if(e.bitmap){e.refCounter+=1;return e}if(e.file)return this.getFromFile(e.file);if(e.blobPromise){const{blobPromise:t}=e;delete e.blobPromise;return this.getFromBlob(e.id,t)}return this.getFromUrl(e.url)}getFromCanvas(t,e){this.#x||=new Map;let i=this.#x.get(t);if(i?.bitmap){i.refCounter+=1;return i}const n=new OffscreenCanvas(e.width,e.height);n.getContext("2d").drawImage(e,0,0);i={bitmap:n.transferToImageBitmap(),id:`image_${this.#A}_${this.#y++}`,refCounter:1,isSvg:!1};this.#x.set(t,i);this.#x.set(i.id,i);return i}getSvgUrl(t){const e=this.#x.get(t);return e?.isSvg?e.svgUrl:null}deleteId(t){this.#x||=new Map;const e=this.#x.get(t);if(!e)return;e.refCounter-=1;if(0!==e.refCounter)return;const{bitmap:i}=e;if(!e.url&&!e.file){const t=new OffscreenCanvas(i.width,i.height);t.getContext("bitmaprenderer").transferFromImageBitmap(i);e.blobPromise=t.convertToBlob()}i.close?.();e.bitmap=null}isValidId(t){return t.startsWith(`image_${this.#A}_`)}}class CommandManager{#E=[];#S=!1;#C;#T=-1;constructor(t=128){this.#C=t}add({cmd:t,undo:e,post:i,mustExec:n,type:s=NaN,overwriteIfSameType:r=!1,keepUndo:a=!1}){n&&t();if(this.#S)return;const o={cmd:t,undo:e,post:i,type:s};if(-1===this.#T){this.#E.length>0&&(this.#E.length=0);this.#T=0;this.#E.push(o);return}if(r&&this.#E[this.#T].type===s){a&&(o.undo=this.#E[this.#T].undo);this.#E[this.#T]=o;return}const l=this.#T+1;if(l===this.#C)this.#E.splice(0,1);else{this.#T=l;l<this.#E.length&&this.#E.splice(l)}this.#E.push(o)}undo(){if(-1===this.#T)return;this.#S=!0;const{undo:t,post:e}=this.#E[this.#T];t();e?.();this.#S=!1;this.#T-=1}redo(){if(this.#T<this.#E.length-1){this.#T+=1;this.#S=!0;const{cmd:t,post:e}=this.#E[this.#T];t();e?.();this.#S=!1}}hasSomethingToUndo(){return-1!==this.#T}hasSomethingToRedo(){return this.#T<this.#E.length-1}cleanType(t){if(-1!==this.#T){for(let e=this.#T;e>=0;e--)if(this.#E[e].type!==t){this.#E.splice(e+1,this.#T-e);this.#T=e;return}this.#E.length=0;this.#T=-1}}destroy(){this.#E=null}}class KeyboardManager{constructor(t){this.buffer=[];this.callbacks=new Map;this.allKeys=new Set;const{isMac:e}=util_FeatureTest.platform;for(const[i,n,s={}]of t)for(const t of i){const i=t.startsWith("mac+");if(e&&i){this.callbacks.set(t.slice(4),{callback:n,options:s});this.allKeys.add(t.split("+").at(-1))}else if(!e&&!i){this.callbacks.set(t,{callback:n,options:s});this.allKeys.add(t.split("+").at(-1))}}}#M(t){t.altKey&&this.buffer.push("alt");t.ctrlKey&&this.buffer.push("ctrl");t.metaKey&&this.buffer.push("meta");t.shiftKey&&this.buffer.push("shift");this.buffer.push(t.key);const e=this.buffer.join("+");this.buffer.length=0;return e}exec(t,e){if(!this.allKeys.has(e.key))return;const i=this.callbacks.get(this.#M(e));if(!i)return;const{callback:n,options:{bubbles:s=!1,args:r=[],checker:a=null}}=i;if(!a||a(t,e)){n.bind(t,...r,e)();s||stopEvent(e)}}}class ColorManager{static _colorsMapping=new Map([["CanvasText",[0,0,0]],["Canvas",[255,255,255]]]);get _colors(){const t=new Map([["CanvasText",null],["Canvas",null]]);!function getColorValues(t){const e=document.createElement("span");e.style.visibility="hidden";e.style.colorScheme="only light";document.body.append(e);for(const i of t.keys()){e.style.color=i;const n=window.getComputedStyle(e).color;t.set(i,getRGB(n))}e.remove()}(t);return shadow(this,"_colors",t)}convert(t){const e=getRGB(t);if(!window.matchMedia("(forced-colors: active)").matches)return e;for(const[t,i]of this._colors)if(i.every(((t,i)=>t===e[i])))return ColorManager._colorsMapping.get(t);return e}getHexCode(t){const e=this._colors.get(t);return e?Util.makeHexColor(...e):t}}class AnnotationEditorUIManager{#D=new AbortController;#P=null;#k=new Map;#I=new Map;#R=null;#F=null;#L=null;#O=new CommandManager;#N=null;#B=null;#U=null;#H=0;#z=new Set;#j=null;#G=null;#W=new Set;_editorUndoBar=null;#V=!1;#$=!1;#q=!1;#X=null;#K=null;#Y=null;#Q=null;#J=!1;#Z=null;#tt=new IdManager;#et=!1;#it=!1;#nt=null;#st=null;#rt=null;#at=null;#ot=null;#lt=f.NONE;#ht=new Set;#ct=null;#dt=null;#ut=null;#pt=null;#gt={isEditing:!1,isEmpty:!0,hasSomethingToUndo:!1,hasSomethingToRedo:!1,hasSelectedEditor:!1,hasSelectedText:!1};#ft=[0,0];#mt=null;#bt=null;#vt=null;#wt=null;#yt=null;static TRANSLATE_SMALL=1;static TRANSLATE_BIG=10;static get _keyboardManager(){const t=AnnotationEditorUIManager.prototype,arrowChecker=t=>t.#bt.contains(document.activeElement)&&"BUTTON"!==document.activeElement.tagName&&t.hasSomethingToControl(),textInputChecker=(t,{target:e})=>{if(e instanceof HTMLInputElement){const{type:t}=e;return"text"!==t&&"number"!==t}return!0},e=this.TRANSLATE_SMALL,i=this.TRANSLATE_BIG;return shadow(this,"_keyboardManager",new KeyboardManager([[["ctrl+a","mac+meta+a"],t.selectAll,{checker:textInputChecker}],[["ctrl+z","mac+meta+z"],t.undo,{checker:textInputChecker}],[["ctrl+y","ctrl+shift+z","mac+meta+shift+z","ctrl+shift+Z","mac+meta+shift+Z"],t.redo,{checker:textInputChecker}],[["Backspace","alt+Backspace","ctrl+Backspace","shift+Backspace","mac+Backspace","mac+alt+Backspace","mac+ctrl+Backspace","Delete","ctrl+Delete","shift+Delete","mac+Delete"],t.delete,{checker:textInputChecker}],[["Enter","mac+Enter"],t.addNewEditorFromKeyboard,{checker:(t,{target:e})=>!(e instanceof HTMLButtonElement)&&t.#bt.contains(e)&&!t.isEnterHandled}],[[" ","mac+ "],t.addNewEditorFromKeyboard,{checker:(t,{target:e})=>!(e instanceof HTMLButtonElement)&&t.#bt.contains(document.activeElement)}],[["Escape","mac+Escape"],t.unselectAll],[["ArrowLeft","mac+ArrowLeft"],t.translateSelectedEditors,{args:[-e,0],checker:arrowChecker}],[["ctrl+ArrowLeft","mac+shift+ArrowLeft"],t.translateSelectedEditors,{args:[-i,0],checker:arrowChecker}],[["ArrowRight","mac+ArrowRight"],t.translateSelectedEditors,{args:[e,0],checker:arrowChecker}],[["ctrl+ArrowRight","mac+shift+ArrowRight"],t.translateSelectedEditors,{args:[i,0],checker:arrowChecker}],[["ArrowUp","mac+ArrowUp"],t.translateSelectedEditors,{args:[0,-e],checker:arrowChecker}],[["ctrl+ArrowUp","mac+shift+ArrowUp"],t.translateSelectedEditors,{args:[0,-i],checker:arrowChecker}],[["ArrowDown","mac+ArrowDown"],t.translateSelectedEditors,{args:[0,e],checker:arrowChecker}],[["ctrl+ArrowDown","mac+shift+ArrowDown"],t.translateSelectedEditors,{args:[0,i],checker:arrowChecker}]]))}constructor(t,e,i,n,s,r,a,o,l,h,c,d,u,p,g,f){const m=this._signal=this.#D.signal;this.#bt=t;this.#vt=e;this.#wt=i;this.#R=n;this.#N=s;this.#dt=r;this._eventBus=a;a._on("editingaction",this.onEditingAction.bind(this),{signal:m});a._on("pagechanging",this.onPageChanging.bind(this),{signal:m});a._on("scalechanging",this.onScaleChanging.bind(this),{signal:m});a._on("rotationchanging",this.onRotationChanging.bind(this),{signal:m});a._on("setpreference",this.onSetPreference.bind(this),{signal:m});a._on("switchannotationeditorparams",(t=>this.updateParams(t.type,t.value)),{signal:m});this.#At();this.#xt();this.#_t();this.#F=o.annotationStorage;this.#X=o.filterFactory;this.#ut=l;this.#Q=h||null;this.#V=c;this.#$=d;this.#q=u;this.#ot=p||null;this.viewParameters={realScale:PixelsPerInch.PDF_TO_CSS_UNITS,rotation:0};this.isShiftKeyDown=!1;this._editorUndoBar=g||null;this._supportsPinchToZoom=!1!==f}destroy(){this.#yt?.resolve();this.#yt=null;this.#D?.abort();this.#D=null;this._signal=null;for(const t of this.#I.values())t.destroy();this.#I.clear();this.#k.clear();this.#W.clear();this.#at?.clear();this.#P=null;this.#ht.clear();this.#O.destroy();this.#R?.destroy();this.#N?.destroy();this.#dt?.destroy();this.#Z?.hide();this.#Z=null;this.#rt?.destroy();this.#rt=null;if(this.#K){clearTimeout(this.#K);this.#K=null}if(this.#mt){clearTimeout(this.#mt);this.#mt=null}this._editorUndoBar?.destroy()}combinedSignal(t){return AbortSignal.any([this._signal,t.signal])}get mlManager(){return this.#ot}get useNewAltTextFlow(){return this.#$}get useNewAltTextWhenAddingImage(){return this.#q}get hcmFilter(){return shadow(this,"hcmFilter",this.#ut?this.#X.addHCMFilter(this.#ut.foreground,this.#ut.background):"none")}get direction(){return shadow(this,"direction",getComputedStyle(this.#bt).direction)}get _highlightColors(){return shadow(this,"_highlightColors",this.#Q?new Map(this.#Q.split(",").map((t=>{(t=t.split("=").map((t=>t.trim())))[1]=t[1].toUpperCase();return t}))):null)}get highlightColors(){const{_highlightColors:t}=this;if(!t)return shadow(this,"highlightColors",null);const e=new Map,i=!!this.#ut;for(const[n,s]of t){const t=n.endsWith("_HCM");i&&t?e.set(n.replace("_HCM",""),s):i||t||e.set(n,s)}return shadow(this,"highlightColors",e)}get highlightColorNames(){return shadow(this,"highlightColorNames",this.highlightColors?new Map(Array.from(this.highlightColors,(t=>t.reverse()))):null)}getNonHCMColor(t){if(!this._highlightColors)return t;const e=this.highlightColorNames.get(t);return this._highlightColors.get(e)||t}getNonHCMColorName(t){return this.highlightColorNames.get(t)||t}setCurrentDrawingSession(t){if(t){this.unselectAll();this.disableUserSelect(!0)}else this.disableUserSelect(!1);this.#U=t}setMainHighlightColorPicker(t){this.#rt=t}editAltText(t,e=!1){this.#R?.editAltText(this,t,e)}hasCommentManager(){return!!this.#N}editComment(t,e){this.#N?.open(this,t,e)}getSignature(t){this.#dt?.getSignature({uiManager:this,editor:t})}get signatureManager(){return this.#dt}switchToMode(t,e){this._eventBus.on("annotationeditormodechanged",e,{once:!0,signal:this._signal});this._eventBus.dispatch("showannotationeditorui",{source:this,mode:t})}setPreference(t,e){this._eventBus.dispatch("setpreference",{source:this,name:t,value:e})}onSetPreference({name:t,value:e}){if("enableNewAltTextWhenAddingImage"===t)this.#q=e}onPageChanging({pageNumber:t}){this.#H=t-1}focusMainContainer(){this.#bt.focus()}findParent(t,e){for(const i of this.#I.values()){const{x:n,y:s,width:r,height:a}=i.div.getBoundingClientRect();if(t>=n&&t<=n+r&&e>=s&&e<=s+a)return i}return null}disableUserSelect(t=!1){this.#vt.classList.toggle("noUserSelect",t)}addShouldRescale(t){this.#W.add(t)}removeShouldRescale(t){this.#W.delete(t)}onScaleChanging({scale:t}){this.commitOrRemove();this.viewParameters.realScale=t*PixelsPerInch.PDF_TO_CSS_UNITS;for(const t of this.#W)t.onScaleChanging();this.#U?.onScaleChanging()}onRotationChanging({pagesRotation:t}){this.commitOrRemove();this.viewParameters.rotation=t}#Et({anchorNode:t}){return t.nodeType===Node.TEXT_NODE?t.parentElement:t}#St(t){const{currentLayer:e}=this;if(e.hasTextLayer(t))return e;for(const e of this.#I.values())if(e.hasTextLayer(t))return e;return null}highlightSelection(t=""){const e=document.getSelection();if(!e||e.isCollapsed)return;const{anchorNode:i,anchorOffset:n,focusNode:s,focusOffset:r}=e,a=e.toString(),o=this.#Et(e).closest(".textLayer"),l=this.getSelectionBoxes(o);if(!l)return;e.empty();const h=this.#St(o),c=this.#lt===f.NONE,callback=()=>{h?.createAndAddNewEditor({x:0,y:0},!1,{methodOfCreation:t,boxes:l,anchorNode:i,anchorOffset:n,focusNode:s,focusOffset:r,text:a});c&&this.showAllEditors("highlight",!0,!0)};c?this.switchToMode(f.HIGHLIGHT,callback):callback()}#Ct(){const t=document.getSelection();if(!t||t.isCollapsed)return;const e=this.#Et(t).closest(".textLayer"),i=this.getSelectionBoxes(e);if(i){this.#Z||=new HighlightToolbar(this);this.#Z.show(e,i,"ltr"===this.direction)}}addToAnnotationStorage(t){t.isEmpty()||!this.#F||this.#F.has(t.id)||this.#F.setValue(t.id,t)}a11yAlert(t,e=null){const i=this.#wt;if(i){i.setAttribute("data-l10n-id",t);e?i.setAttribute("data-l10n-args",JSON.stringify(e)):i.removeAttribute("data-l10n-args")}}#Tt(){const t=document.getSelection();if(!t||t.isCollapsed){if(this.#ct){this.#Z?.hide();this.#ct=null;this.#Mt({hasSelectedText:!1})}return}const{anchorNode:e}=t;if(e===this.#ct)return;const i=this.#Et(t).closest(".textLayer");if(i){this.#Z?.hide();this.#ct=e;this.#Mt({hasSelectedText:!0});if(this.#lt===f.HIGHLIGHT||this.#lt===f.NONE){this.#lt===f.HIGHLIGHT&&this.showAllEditors("highlight",!0,!0);this.#J=this.isShiftKeyDown;if(!this.isShiftKeyDown){const t=this.#lt===f.HIGHLIGHT?this.#St(i):null;t?.toggleDrawing();const e=new AbortController,n=this.combinedSignal(e),pointerup=i=>{if("pointerup"!==i.type||0===i.button){e.abort();t?.toggleDrawing(!0);"pointerup"===i.type&&this.#Dt("main_toolbar")}};window.addEventListener("pointerup",pointerup,{signal:n});window.addEventListener("blur",pointerup,{signal:n})}}}else if(this.#ct){this.#Z?.hide();this.#ct=null;this.#Mt({hasSelectedText:!1})}}#Dt(t=""){this.#lt===f.HIGHLIGHT?this.highlightSelection(t):this.#V&&this.#Ct()}#At(){document.addEventListener("selectionchange",this.#Tt.bind(this),{signal:this._signal})}#Pt(){if(this.#Y)return;this.#Y=new AbortController;const t=this.combinedSignal(this.#Y);window.addEventListener("focus",this.focus.bind(this),{signal:t});window.addEventListener("blur",this.blur.bind(this),{signal:t})}#kt(){this.#Y?.abort();this.#Y=null}blur(){this.isShiftKeyDown=!1;if(this.#J){this.#J=!1;this.#Dt("main_toolbar")}if(!this.hasSelection)return;const{activeElement:t}=document;for(const e of this.#ht)if(e.div.contains(t)){this.#st=[e,t];e._focusEventsAllowed=!1;break}}focus(){if(!this.#st)return;const[t,e]=this.#st;this.#st=null;e.addEventListener("focusin",(()=>{t._focusEventsAllowed=!0}),{once:!0,signal:this._signal});e.focus()}#_t(){if(this.#nt)return;this.#nt=new AbortController;const t=this.combinedSignal(this.#nt);window.addEventListener("keydown",this.keydown.bind(this),{signal:t});window.addEventListener("keyup",this.keyup.bind(this),{signal:t})}#It(){this.#nt?.abort();this.#nt=null}#Rt(){if(this.#B)return;this.#B=new AbortController;const t=this.combinedSignal(this.#B);document.addEventListener("copy",this.copy.bind(this),{signal:t});document.addEventListener("cut",this.cut.bind(this),{signal:t});document.addEventListener("paste",this.paste.bind(this),{signal:t})}#Ft(){this.#B?.abort();this.#B=null}#xt(){const t=this._signal;document.addEventListener("dragover",this.dragOver.bind(this),{signal:t});document.addEventListener("drop",this.drop.bind(this),{signal:t})}addEditListeners(){this.#_t();this.#Rt()}removeEditListeners(){this.#It();this.#Ft()}dragOver(t){for(const{type:e}of t.dataTransfer.items)for(const i of this.#G)if(i.isHandlingMimeForPasting(e)){t.dataTransfer.dropEffect="copy";t.preventDefault();return}}drop(t){for(const e of t.dataTransfer.items)for(const i of this.#G)if(i.isHandlingMimeForPasting(e.type)){i.paste(e,this.currentLayer);t.preventDefault();return}}copy(t){t.preventDefault();this.#P?.commitOrRemove();if(!this.hasSelection)return;const e=[];for(const t of this.#ht){const i=t.serialize(!0);i&&e.push(i)}0!==e.length&&t.clipboardData.setData("application/pdfjs",JSON.stringify(e))}cut(t){this.copy(t);this.delete()}async paste(t){t.preventDefault();const{clipboardData:e}=t;for(const t of e.items)for(const e of this.#G)if(e.isHandlingMimeForPasting(t.type)){e.paste(t,this.currentLayer);return}let i=e.getData("application/pdfjs");if(!i)return;try{i=JSON.parse(i)}catch(t){warn(`paste: "${t.message}".`);return}if(!Array.isArray(i))return;this.unselectAll();const n=this.currentLayer;try{const t=[];for(const e of i){const i=await n.deserialize(e);if(!i)return;t.push(i)}const cmd=()=>{for(const e of t)this.#Lt(e);this.#Ot(t)},undo=()=>{for(const e of t)e.remove()};this.addCommands({cmd,undo,mustExec:!0})}catch(t){warn(`paste: "${t.message}".`)}}keydown(t){this.isShiftKeyDown||"Shift"!==t.key||(this.isShiftKeyDown=!0);this.#lt===f.NONE||this.isEditorHandlingKeyboard||AnnotationEditorUIManager._keyboardManager.exec(this,t)}keyup(t){if(this.isShiftKeyDown&&"Shift"===t.key){this.isShiftKeyDown=!1;if(this.#J){this.#J=!1;this.#Dt("main_toolbar")}}}onEditingAction({name:t}){switch(t){case"undo":case"redo":case"delete":case"selectAll":this[t]();break;case"highlightSelection":this.highlightSelection("context_menu")}}#Mt(t){if(Object.entries(t).some((([t,e])=>this.#gt[t]!==e))){this._eventBus.dispatch("annotationeditorstateschanged",{source:this,details:Object.assign(this.#gt,t)});this.#lt===f.HIGHLIGHT&&!1===t.hasSelectedEditor&&this.#Nt([[m.HIGHLIGHT_FREE,!0]])}}#Nt(t){this._eventBus.dispatch("annotationeditorparamschanged",{source:this,details:t})}setEditingState(t){if(t){this.#Pt();this.#Rt();this.#Mt({isEditing:this.#lt!==f.NONE,isEmpty:this.#Bt(),hasSomethingToUndo:this.#O.hasSomethingToUndo(),hasSomethingToRedo:this.#O.hasSomethingToRedo(),hasSelectedEditor:!1})}else{this.#kt();this.#Ft();this.#Mt({isEditing:!1});this.disableUserSelect(!1)}}registerEditorTypes(t){if(!this.#G){this.#G=t;for(const t of this.#G)this.#Nt(t.defaultPropertiesToUpdate)}}getId(){return this.#tt.id}get currentLayer(){return this.#I.get(this.#H)}getLayer(t){return this.#I.get(t)}get currentPageIndex(){return this.#H}addLayer(t){this.#I.set(t.pageIndex,t);this.#et?t.enable():t.disable()}removeLayer(t){this.#I.delete(t.pageIndex)}async updateMode(t,e=null,i=!1,n=!1,s=!1){if(this.#lt!==t){if(this.#yt){await this.#yt.promise;if(!this.#yt)return}this.#yt=Promise.withResolvers();this.#U?.commitOrRemove();this.#lt=t;if(t!==f.NONE){t===f.SIGNATURE&&await(this.#dt?.loadSignatures());this.setEditingState(!0);await this.#Ut();this.unselectAll();for(const e of this.#I.values())e.updateMode(t);if(e){for(const t of this.#k.values())if(t.annotationElementId===e||t.id===e){this.setSelected(t);s?t.editComment():n&&t.enterInEditMode()}else t.unselect();this.#yt.resolve()}else{i&&this.addNewEditorFromKeyboard();this.#yt.resolve()}}else{this.setEditingState(!1);this.#Ht();this._editorUndoBar?.hide();this.#yt.resolve()}}}addNewEditorFromKeyboard(){this.currentLayer.canCreateNewEmptyEditor()&&this.currentLayer.addNewEditor()}updateToolbar(t){t.mode!==this.#lt&&this._eventBus.dispatch("switchannotationeditormode",{source:this,...t})}updateParams(t,e){if(this.#G){switch(t){case m.CREATE:this.currentLayer.addNewEditor(e);return;case m.HIGHLIGHT_SHOW_ALL:this._eventBus.dispatch("reporttelemetry",{source:this,details:{type:"editing",data:{type:"highlight",action:"toggle_visibility"}}});(this.#pt||=new Map).set(t,e);this.showAllEditors("highlight",e)}if(this.hasSelection)for(const i of this.#ht)i.updateParams(t,e);else for(const i of this.#G)i.updateDefaultParams(t,e)}}showAllEditors(t,e,i=!1){for(const i of this.#k.values())i.editorType===t&&i.show(e);(this.#pt?.get(m.HIGHLIGHT_SHOW_ALL)??!0)!==e&&this.#Nt([[m.HIGHLIGHT_SHOW_ALL,e]])}enableWaiting(t=!1){if(this.#it!==t){this.#it=t;for(const e of this.#I.values()){t?e.disableClick():e.enableClick();e.div.classList.toggle("waiting",t)}}}async#Ut(){if(!this.#et){this.#et=!0;const t=[];for(const e of this.#I.values())t.push(e.enable());await Promise.all(t);for(const t of this.#k.values())t.enable()}}#Ht(){this.unselectAll();if(this.#et){this.#et=!1;for(const t of this.#I.values())t.disable();for(const t of this.#k.values())t.disable()}}getEditors(t){const e=[];for(const i of this.#k.values())i.pageIndex===t&&e.push(i);return e}getEditor(t){return this.#k.get(t)}addEditor(t){this.#k.set(t.id,t)}removeEditor(t){if(t.div.contains(document.activeElement)){this.#K&&clearTimeout(this.#K);this.#K=setTimeout((()=>{this.focusMainContainer();this.#K=null}),0)}this.#k.delete(t.id);t.annotationElementId&&this.#at?.delete(t.annotationElementId);this.unselect(t);t.annotationElementId&&this.#z.has(t.annotationElementId)||this.#F?.remove(t.id)}addDeletedAnnotationElement(t){this.#z.add(t.annotationElementId);this.addChangedExistingAnnotation(t);t.deleted=!0}isDeletedAnnotationElement(t){return this.#z.has(t)}removeDeletedAnnotationElement(t){this.#z.delete(t.annotationElementId);this.removeChangedExistingAnnotation(t);t.deleted=!1}#Lt(t){const e=this.#I.get(t.pageIndex);if(e)e.addOrRebuild(t);else{this.addEditor(t);this.addToAnnotationStorage(t)}}setActiveEditor(t){if(this.#P!==t){this.#P=t;t&&this.#Nt(t.propertiesToUpdate)}}get#zt(){let t=null;for(t of this.#ht);return t}updateUI(t){this.#zt===t&&this.#Nt(t.propertiesToUpdate)}updateUIForDefaultProperties(t){this.#Nt(t.defaultPropertiesToUpdate)}toggleSelected(t){if(this.#ht.has(t)){this.#ht.delete(t);t.unselect();this.#Mt({hasSelectedEditor:this.hasSelection})}else{this.#ht.add(t);t.select();this.#Nt(t.propertiesToUpdate);this.#Mt({hasSelectedEditor:!0})}}setSelected(t){this.updateToolbar({mode:t.mode,editId:t.id});this.#U?.commitOrRemove();for(const e of this.#ht)e!==t&&e.unselect();this.#ht.clear();this.#ht.add(t);t.select();this.#Nt(t.propertiesToUpdate);this.#Mt({hasSelectedEditor:!0})}isSelected(t){return this.#ht.has(t)}get firstSelectedEditor(){return this.#ht.values().next().value}unselect(t){t.unselect();this.#ht.delete(t);this.#Mt({hasSelectedEditor:this.hasSelection})}get hasSelection(){return 0!==this.#ht.size}get isEnterHandled(){return 1===this.#ht.size&&this.firstSelectedEditor.isEnterHandled}undo(){this.#O.undo();this.#Mt({hasSomethingToUndo:this.#O.hasSomethingToUndo(),hasSomethingToRedo:!0,isEmpty:this.#Bt()});this._editorUndoBar?.hide()}redo(){this.#O.redo();this.#Mt({hasSomethingToUndo:!0,hasSomethingToRedo:this.#O.hasSomethingToRedo(),isEmpty:this.#Bt()})}addCommands(t){this.#O.add(t);this.#Mt({hasSomethingToUndo:!0,hasSomethingToRedo:!1,isEmpty:this.#Bt()})}cleanUndoStack(t){this.#O.cleanType(t)}#Bt(){if(0===this.#k.size)return!0;if(1===this.#k.size)for(const t of this.#k.values())return t.isEmpty();return!1}delete(){this.commitOrRemove();const t=this.currentLayer?.endDrawingSession(!0);if(!this.hasSelection&&!t)return;const e=t?[t]:[...this.#ht],undo=()=>{for(const t of e)this.#Lt(t)};this.addCommands({cmd:()=>{this._editorUndoBar?.show(undo,1===e.length?e[0].editorType:e.length);for(const t of e)t.remove()},undo,mustExec:!0})}commitOrRemove(){this.#P?.commitOrRemove()}hasSomethingToControl(){return this.#P||this.hasSelection}#Ot(t){for(const t of this.#ht)t.unselect();this.#ht.clear();for(const e of t)if(!e.isEmpty()){this.#ht.add(e);e.select()}this.#Mt({hasSelectedEditor:this.hasSelection})}selectAll(){for(const t of this.#ht)t.commit();this.#Ot(this.#k.values())}unselectAll(){if(this.#P){this.#P.commitOrRemove();if(this.#lt!==f.NONE)return}if(!this.#U?.commitOrRemove()&&this.hasSelection){for(const t of this.#ht)t.unselect();this.#ht.clear();this.#Mt({hasSelectedEditor:!1})}}translateSelectedEditors(t,e,i=!1){i||this.commitOrRemove();if(!this.hasSelection)return;this.#ft[0]+=t;this.#ft[1]+=e;const[n,s]=this.#ft,r=[...this.#ht];this.#mt&&clearTimeout(this.#mt);this.#mt=setTimeout((()=>{this.#mt=null;this.#ft[0]=this.#ft[1]=0;this.addCommands({cmd:()=>{for(const t of r)if(this.#k.has(t.id)){t.translateInPage(n,s);t.translationDone()}},undo:()=>{for(const t of r)if(this.#k.has(t.id)){t.translateInPage(-n,-s);t.translationDone()}},mustExec:!1})}),1e3);for(const i of r){i.translateInPage(t,e);i.translationDone()}}setUpDragSession(){if(this.hasSelection){this.disableUserSelect(!0);this.#j=new Map;for(const t of this.#ht)this.#j.set(t,{savedX:t.x,savedY:t.y,savedPageIndex:t.pageIndex,newX:0,newY:0,newPageIndex:-1})}}endDragSession(){if(!this.#j)return!1;this.disableUserSelect(!1);const t=this.#j;this.#j=null;let e=!1;for(const[{x:i,y:n,pageIndex:s},r]of t){r.newX=i;r.newY=n;r.newPageIndex=s;e||=i!==r.savedX||n!==r.savedY||s!==r.savedPageIndex}if(!e)return!1;const move=(t,e,i,n)=>{if(this.#k.has(t.id)){const s=this.#I.get(n);if(s)t._setParentAndPosition(s,e,i);else{t.pageIndex=n;t.x=e;t.y=i}}};this.addCommands({cmd:()=>{for(const[e,{newX:i,newY:n,newPageIndex:s}]of t)move(e,i,n,s)},undo:()=>{for(const[e,{savedX:i,savedY:n,savedPageIndex:s}]of t)move(e,i,n,s)},mustExec:!0});return!0}dragSelectedEditors(t,e){if(this.#j)for(const i of this.#j.keys())i.drag(t,e)}rebuild(t){if(null===t.parent){const e=this.getLayer(t.pageIndex);if(e){e.changeParent(t);e.addOrRebuild(t)}else{this.addEditor(t);this.addToAnnotationStorage(t);t.rebuild()}}else t.parent.addOrRebuild(t)}get isEditorHandlingKeyboard(){return this.getActive()?.shouldGetKeyboardEvents()||1===this.#ht.size&&this.firstSelectedEditor.shouldGetKeyboardEvents()}isActive(t){return this.#P===t}getActive(){return this.#P}getMode(){return this.#lt}get imageManager(){return shadow(this,"imageManager",new ImageManager)}getSelectionBoxes(t){if(!t)return null;const e=document.getSelection();for(let i=0,n=e.rangeCount;i<n;i++)if(!t.contains(e.getRangeAt(i).commonAncestorContainer))return null;const{x:i,y:n,width:s,height:r}=t.getBoundingClientRect();let a;switch(t.getAttribute("data-main-rotation")){case"90":a=(t,e,a,o)=>({x:(e-n)/r,y:1-(t+a-i)/s,width:o/r,height:a/s});break;case"180":a=(t,e,a,o)=>({x:1-(t+a-i)/s,y:1-(e+o-n)/r,width:a/s,height:o/r});break;case"270":a=(t,e,a,o)=>({x:1-(e+o-n)/r,y:(t-i)/s,width:o/r,height:a/s});break;default:a=(t,e,a,o)=>({x:(t-i)/s,y:(e-n)/r,width:a/s,height:o/r})}const o=[];for(let t=0,i=e.rangeCount;t<i;t++){const i=e.getRangeAt(t);if(!i.collapsed)for(const{x:t,y:e,width:n,height:s}of i.getClientRects())0!==n&&0!==s&&o.push(a(t,e,n,s))}return 0===o.length?null:o}addChangedExistingAnnotation({annotationElementId:t,id:e}){(this.#L||=new Map).set(t,e)}removeChangedExistingAnnotation({annotationElementId:t}){this.#L?.delete(t)}renderAnnotationElement(t){const e=this.#L?.get(t.data.id);if(!e)return;const i=this.#F.getRawValue(e);i&&(this.#lt!==f.NONE||i.hasBeenModified)&&i.renderAnnotationElement(t)}setMissingCanvas(t,e,i){const n=this.#at?.get(t);if(n){n.setCanvas(e,i);this.#at.delete(t)}}addMissingCanvas(t,e){(this.#at||=new Map).set(t,e)}}class AltText{#o=null;#jt=!1;#Gt=null;#Wt=null;#Vt=null;#$t=null;#qt=!1;#Xt=null;#r=null;#Kt=null;#Yt=null;#Qt=!1;static#Jt=null;static _l10n=null;constructor(t){this.#r=t;this.#Qt=t._uiManager.useNewAltTextFlow;AltText.#Jt||=Object.freeze({added:"pdfjs-editor-new-alt-text-added-button","added-label":"pdfjs-editor-new-alt-text-added-button-label",missing:"pdfjs-editor-new-alt-text-missing-button","missing-label":"pdfjs-editor-new-alt-text-missing-button-label",review:"pdfjs-editor-new-alt-text-to-review-button","review-label":"pdfjs-editor-new-alt-text-to-review-button-label"})}static initialize(t){AltText._l10n??=t}async render(){const t=this.#Gt=document.createElement("button");t.className="altText";t.tabIndex="0";const e=this.#Wt=document.createElement("span");t.append(e);if(this.#Qt){t.classList.add("new");t.setAttribute("data-l10n-id",AltText.#Jt.missing);e.setAttribute("data-l10n-id",AltText.#Jt["missing-label"])}else{t.setAttribute("data-l10n-id","pdfjs-editor-alt-text-button");e.setAttribute("data-l10n-id","pdfjs-editor-alt-text-button-label")}const i=this.#r._uiManager._signal;t.addEventListener("contextmenu",noContextMenu,{signal:i});t.addEventListener("pointerdown",(t=>t.stopPropagation()),{signal:i});const onClick=t=>{t.preventDefault();this.#r._uiManager.editAltText(this.#r);this.#Qt&&this.#r._reportTelemetry({action:"pdfjs.image.alt_text.image_status_label_clicked",data:{label:this.#Zt}})};t.addEventListener("click",onClick,{capture:!0,signal:i});t.addEventListener("keydown",(e=>{if(e.target===t&&"Enter"===e.key){this.#qt=!0;onClick(e)}}),{signal:i});await this.#te();return t}get#Zt(){return(this.#o?"added":null===this.#o&&this.guessedText&&"review")||"missing"}finish(){if(this.#Gt){this.#Gt.focus({focusVisible:this.#qt});this.#qt=!1}}isEmpty(){return this.#Qt?null===this.#o:!this.#o&&!this.#jt}hasData(){return this.#Qt?null!==this.#o||!!this.#Kt:this.isEmpty()}get guessedText(){return this.#Kt}async setGuessedText(t){if(null===this.#o){this.#Kt=t;this.#Yt=await AltText._l10n.get("pdfjs-editor-new-alt-text-generated-alt-text-with-disclaimer",{generatedAltText:t});this.#te()}}toggleAltTextBadge(t=!1){if(this.#Qt&&!this.#o){if(!this.#Xt){const t=this.#Xt=document.createElement("div");t.className="noAltTextBadge";this.#r.div.append(t)}this.#Xt.classList.toggle("hidden",!t)}else{this.#Xt?.remove();this.#Xt=null}}serialize(t){let e=this.#o;t||this.#Kt!==e||(e=this.#Yt);return{altText:e,decorative:this.#jt,guessedText:this.#Kt,textWithDisclaimer:this.#Yt}}get data(){return{altText:this.#o,decorative:this.#jt}}set data({altText:t,decorative:e,guessedText:i,textWithDisclaimer:n,cancel:s=!1}){if(i){this.#Kt=i;this.#Yt=n}if(this.#o!==t||this.#jt!==e){if(!s){this.#o=t;this.#jt=e}this.#te()}}toggle(t=!1){if(this.#Gt){if(!t&&this.#$t){clearTimeout(this.#$t);this.#$t=null}this.#Gt.disabled=!t}}shown(){this.#r._reportTelemetry({action:"pdfjs.image.alt_text.image_status_label_displayed",data:{label:this.#Zt}})}destroy(){this.#Gt?.remove();this.#Gt=null;this.#Wt=null;this.#Vt=null;this.#Xt?.remove();this.#Xt=null}async#te(){const t=this.#Gt;if(!t)return;if(this.#Qt){t.classList.toggle("done",!!this.#o);t.setAttribute("data-l10n-id",AltText.#Jt[this.#Zt]);this.#Wt?.setAttribute("data-l10n-id",AltText.#Jt[`${this.#Zt}-label`]);if(!this.#o){this.#Vt?.remove();return}}else{if(!this.#o&&!this.#jt){t.classList.remove("done");this.#Vt?.remove();return}t.classList.add("done");t.setAttribute("data-l10n-id","pdfjs-editor-alt-text-edit-button")}let e=this.#Vt;if(!e){this.#Vt=e=document.createElement("span");e.className="tooltip";e.setAttribute("role","tooltip");e.id=`alt-text-tooltip-${this.#r.id}`;const i=100,n=this.#r._uiManager._signal;n.addEventListener("abort",(()=>{clearTimeout(this.#$t);this.#$t=null}),{once:!0});t.addEventListener("mouseenter",(()=>{this.#$t=setTimeout((()=>{this.#$t=null;this.#Vt.classList.add("show");this.#r._reportTelemetry({action:"alt_text_tooltip"})}),i)}),{signal:n});t.addEventListener("mouseleave",(()=>{if(this.#$t){clearTimeout(this.#$t);this.#$t=null}this.#Vt?.classList.remove("show")}),{signal:n})}if(this.#jt)e.setAttribute("data-l10n-id","pdfjs-editor-alt-text-decorative-tooltip");else{e.removeAttribute("data-l10n-id");e.textContent=this.#o}e.parentNode||t.append(e);const i=this.#r.getElementForAltText();i?.setAttribute("aria-describedby",e.id)}}class Comment{#ee=null;#ie=!1;#r=null;#ne=null;#se=null;#re=null;#ae=!1;constructor(t){this.#r=t;this.toolbar=null}render(){if(!this.#r._uiManager.hasCommentManager())return null;const t=this.#ee=document.createElement("button");t.className="comment";t.tabIndex="0";t.setAttribute("data-l10n-id","pdfjs-editor-edit-comment-button");const e=this.#r._uiManager._signal;t.addEventListener("contextmenu",noContextMenu,{signal:e});t.addEventListener("pointerdown",(t=>t.stopPropagation()),{signal:e});const onClick=t=>{t.preventDefault();this.edit()};t.addEventListener("click",onClick,{capture:!0,signal:e});t.addEventListener("keydown",(e=>{if(e.target===t&&"Enter"===e.key){this.#ie=!0;onClick(e)}}),{signal:e});return t}edit(){const{bottom:t,left:e,right:i}=this.#r.getClientDimensions(),n={top:t};"ltr"===this.#r._uiManager.direction?n.right=i:n.left=e;this.#r._uiManager.editComment(this.#r,n)}finish(){if(this.#ee){this.#ee.focus({focusVisible:this.#ie});this.#ie=!1}}isDeleted(){return this.#ae||""===this.#se}hasBeenEdited(){return this.isDeleted()||this.#se!==this.#ne}serialize(){return this.data}get data(){return{text:this.#se,date:this.#re,deleted:this.#ae}}set data(t){if(null!==t){this.#se=t;this.#re=new Date;this.#ae=!1}else{this.#se="";this.#ae=!0}}setInitialText(t){this.#ne=t;this.data=t}toggle(t=!1){this.#ee&&(this.#ee.disabled=!t)}shown(){}destroy(){this.#ee?.remove();this.#ee=null;this.#se="";this.#re=null;this.#r=null;this.#ie=!1;this.#ae=!1}}class TouchManager{#bt;#oe=!1;#le=null;#he;#ce;#de;#ue;#pe=null;#ge;#fe=null;#me;#be=null;constructor({container:t,isPinchingDisabled:e=null,isPinchingStopped:i=null,onPinchStart:n=null,onPinching:s=null,onPinchEnd:r=null,signal:a}){this.#bt=t;this.#le=i;this.#he=e;this.#ce=n;this.#de=s;this.#ue=r;this.#me=new AbortController;this.#ge=AbortSignal.any([a,this.#me.signal]);t.addEventListener("touchstart",this.#ve.bind(this),{passive:!1,signal:this.#ge})}get MIN_TOUCH_DISTANCE_TO_PINCH(){return 35/OutputScale.pixelRatio}#ve(t){if(this.#he?.())return;if(1===t.touches.length){if(this.#pe)return;const t=this.#pe=new AbortController,e=AbortSignal.any([this.#ge,t.signal]),i=this.#bt,n={capture:!0,signal:e,passive:!1},cancelPointerDown=t=>{if("touch"===t.pointerType){this.#pe?.abort();this.#pe=null}};i.addEventListener("pointerdown",(t=>{if("touch"===t.pointerType){stopEvent(t);cancelPointerDown(t)}}),n);i.addEventListener("pointerup",cancelPointerDown,n);i.addEventListener("pointercancel",cancelPointerDown,n);return}if(!this.#be){this.#be=new AbortController;const t=AbortSignal.any([this.#ge,this.#be.signal]),e=this.#bt,i={signal:t,capture:!1,passive:!1};e.addEventListener("touchmove",this.#we.bind(this),i);const n=this.#ye.bind(this);e.addEventListener("touchend",n,i);e.addEventListener("touchcancel",n,i);i.capture=!0;e.addEventListener("pointerdown",stopEvent,i);e.addEventListener("pointermove",stopEvent,i);e.addEventListener("pointercancel",stopEvent,i);e.addEventListener("pointerup",stopEvent,i);this.#ce?.()}stopEvent(t);if(2!==t.touches.length||this.#le?.()){this.#fe=null;return}let[e,i]=t.touches;e.identifier>i.identifier&&([e,i]=[i,e]);this.#fe={touch0X:e.screenX,touch0Y:e.screenY,touch1X:i.screenX,touch1Y:i.screenY}}#we(t){if(!this.#fe||2!==t.touches.length)return;stopEvent(t);let[e,i]=t.touches;e.identifier>i.identifier&&([e,i]=[i,e]);const{screenX:n,screenY:s}=e,{screenX:r,screenY:a}=i,o=this.#fe,{touch0X:l,touch0Y:h,touch1X:c,touch1Y:d}=o,u=c-l,p=d-h,g=r-n,f=a-s,m=Math.hypot(g,f)||1,b=Math.hypot(u,p)||1;if(!this.#oe&&Math.abs(b-m)<=TouchManager.MIN_TOUCH_DISTANCE_TO_PINCH)return;o.touch0X=n;o.touch0Y=s;o.touch1X=r;o.touch1Y=a;if(!this.#oe){this.#oe=!0;return}const v=[(n+r)/2,(s+a)/2];this.#de?.(v,b,m)}#ye(t){if(!(t.touches.length>=2)){if(this.#be){this.#be.abort();this.#be=null;this.#ue?.()}if(this.#fe){stopEvent(t);this.#fe=null;this.#oe=!1}}}destroy(){this.#me?.abort();this.#me=null;this.#pe?.abort();this.#pe=null}}class AnnotationEditor{#Ae=null;#xe=null;#o=null;#l=null;#_e=!1;#Ee=null;#Se="";#Ce=!1;#Te=null;#Me=null;#De=null;#Pe=null;#ke="";#Ie=!1;#Re=null;#Fe=!1;#Le=!1;#Oe=!1;#Ne=null;#Be=0;#Ue=0;#He=null;#ze=null;isSelected=!1;_isCopy=!1;_editToolbar=null;_initialOptions=Object.create(null);_initialData=null;_isVisible=!0;_uiManager=null;_focusEventsAllowed=!0;static _l10n=null;static _l10nResizer=null;#je=!1;#Ge=AnnotationEditor._zIndex++;static _borderLineWidth=-1;static _colorManager=new ColorManager;static _zIndex=1;static _telemetryTimeout=1e3;static get _resizerKeyboardManager(){const t=AnnotationEditor.prototype._resizeWithKeyboard,e=AnnotationEditorUIManager.TRANSLATE_SMALL,i=AnnotationEditorUIManager.TRANSLATE_BIG;return shadow(this,"_resizerKeyboardManager",new KeyboardManager([[["ArrowLeft","mac+ArrowLeft"],t,{args:[-e,0]}],[["ctrl+ArrowLeft","mac+shift+ArrowLeft"],t,{args:[-i,0]}],[["ArrowRight","mac+ArrowRight"],t,{args:[e,0]}],[["ctrl+ArrowRight","mac+shift+ArrowRight"],t,{args:[i,0]}],[["ArrowUp","mac+ArrowUp"],t,{args:[0,-e]}],[["ctrl+ArrowUp","mac+shift+ArrowUp"],t,{args:[0,-i]}],[["ArrowDown","mac+ArrowDown"],t,{args:[0,e]}],[["ctrl+ArrowDown","mac+shift+ArrowDown"],t,{args:[0,i]}],[["Escape","mac+Escape"],AnnotationEditor.prototype._stopResizingWithKeyboard]]))}constructor(t){this.parent=t.parent;this.id=t.id;this.width=this.height=null;this.pageIndex=t.parent.pageIndex;this.name=t.name;this.div=null;this._uiManager=t.uiManager;this.annotationElementId=null;this._willKeepAspectRatio=!1;this._initialOptions.isCentered=t.isCentered;this._structTreeParentId=null;this.annotationElementId=t.annotationElementId||null;const{rotation:e,rawDims:{pageWidth:i,pageHeight:n,pageX:s,pageY:r}}=this.parent.viewport;this.rotation=e;this.pageRotation=(360+e-this._uiManager.viewParameters.rotation)%360;this.pageDimensions=[i,n];this.pageTranslation=[s,r];const[a,o]=this.parentDimensions;this.x=t.x/a;this.y=t.y/o;this.isAttachedToDOM=!1;this.deleted=!1}get editorType(){return Object.getPrototypeOf(this).constructor._type}get mode(){return Object.getPrototypeOf(this).constructor._editorType}static get isDrawer(){return!1}static get _defaultLineColor(){return shadow(this,"_defaultLineColor",this._colorManager.getHexCode("CanvasText"))}static deleteAnnotationElement(t){const e=new FakeEditor({id:t.parent.getNextId(),parent:t.parent,uiManager:t._uiManager});e.annotationElementId=t.annotationElementId;e.deleted=!0;e._uiManager.addToAnnotationStorage(e)}static initialize(t,e){AnnotationEditor._l10n??=t;AnnotationEditor._l10nResizer||=Object.freeze({topLeft:"pdfjs-editor-resizer-top-left",topMiddle:"pdfjs-editor-resizer-top-middle",topRight:"pdfjs-editor-resizer-top-right",middleRight:"pdfjs-editor-resizer-middle-right",bottomRight:"pdfjs-editor-resizer-bottom-right",bottomMiddle:"pdfjs-editor-resizer-bottom-middle",bottomLeft:"pdfjs-editor-resizer-bottom-left",middleLeft:"pdfjs-editor-resizer-middle-left"});if(-1!==AnnotationEditor._borderLineWidth)return;const i=getComputedStyle(document.documentElement);AnnotationEditor._borderLineWidth=parseFloat(i.getPropertyValue("--outline-width"))||0}static updateDefaultParams(t,e){}static get defaultPropertiesToUpdate(){return[]}static isHandlingMimeForPasting(t){return!1}static paste(t,e){unreachable("Not implemented")}get propertiesToUpdate(){return[]}get _isDraggable(){return this.#je}set _isDraggable(t){this.#je=t;this.div?.classList.toggle("draggable",t)}get isEnterHandled(){return!0}center(){const[t,e]=this.pageDimensions;switch(this.parentRotation){case 90:this.x-=this.height*e/(2*t);this.y+=this.width*t/(2*e);break;case 180:this.x+=this.width/2;this.y+=this.height/2;break;case 270:this.x+=this.height*e/(2*t);this.y-=this.width*t/(2*e);break;default:this.x-=this.width/2;this.y-=this.height/2}this.fixAndSetPosition()}addCommands(t){this._uiManager.addCommands(t)}get currentLayer(){return this._uiManager.currentLayer}setInBackground(){this.div.style.zIndex=0}setInForeground(){this.div.style.zIndex=this.#Ge}setParent(t){if(null!==t){this.pageIndex=t.pageIndex;this.pageDimensions=t.pageDimensions}else this.#We();this.parent=t}focusin(t){this._focusEventsAllowed&&(this.#Ie?this.#Ie=!1:this.parent.setSelected(this))}focusout(t){if(!this._focusEventsAllowed)return;if(!this.isAttachedToDOM)return;const e=t.relatedTarget;if(!e?.closest(`#${this.id}`)){t.preventDefault();this.parent?.isMultipleSelection||this.commitOrRemove()}}commitOrRemove(){this.isEmpty()?this.remove():this.commit()}commit(){this.isInEditMode()&&this.addToAnnotationStorage()}addToAnnotationStorage(){this._uiManager.addToAnnotationStorage(this)}setAt(t,e,i,n){const[s,r]=this.parentDimensions;[i,n]=this.screenToPageTranslation(i,n);this.x=(t+i)/s;this.y=(e+n)/r;this.fixAndSetPosition()}_moveAfterPaste(t,e){const[i,n]=this.parentDimensions;this.setAt(t*i,e*n,this.width*i,this.height*n);this._onTranslated()}#Ve([t,e],i,n){[i,n]=this.screenToPageTranslation(i,n);this.x+=i/t;this.y+=n/e;this._onTranslating(this.x,this.y);this.fixAndSetPosition()}translate(t,e){this.#Ve(this.parentDimensions,t,e)}translateInPage(t,e){this.#Re||=[this.x,this.y,this.width,this.height];this.#Ve(this.pageDimensions,t,e);this.div.scrollIntoView({block:"nearest"})}translationDone(){this._onTranslated(this.x,this.y)}drag(t,e){this.#Re||=[this.x,this.y,this.width,this.height];const{div:i,parentDimensions:[n,s]}=this;this.x+=t/n;this.y+=e/s;if(this.parent&&(this.x<0||this.x>1||this.y<0||this.y>1)){const{x:t,y:e}=this.div.getBoundingClientRect();if(this.parent.findNewParent(this,t,e)){this.x-=Math.floor(this.x);this.y-=Math.floor(this.y)}}let{x:r,y:a}=this;const[o,l]=this.getBaseTranslation();r+=o;a+=l;const{style:h}=i;h.left=`${(100*r).toFixed(2)}%`;h.top=`${(100*a).toFixed(2)}%`;this._onTranslating(r,a);i.scrollIntoView({block:"nearest"})}_onTranslating(t,e){}_onTranslated(t,e){}get _hasBeenMoved(){return!!this.#Re&&(this.#Re[0]!==this.x||this.#Re[1]!==this.y)}get _hasBeenResized(){return!!this.#Re&&(this.#Re[2]!==this.width||this.#Re[3]!==this.height)}getBaseTranslation(){const[t,e]=this.parentDimensions,{_borderLineWidth:i}=AnnotationEditor,n=i/t,s=i/e;switch(this.rotation){case 90:return[-n,s];case 180:return[n,s];case 270:return[n,-s];default:return[-n,-s]}}get _mustFixPosition(){return!0}fixAndSetPosition(t=this.rotation){const{div:{style:e},pageDimensions:[i,n]}=this;let{x:s,y:r,width:a,height:o}=this;a*=i;o*=n;s*=i;r*=n;if(this._mustFixPosition)switch(t){case 0:s=MathClamp(s,0,i-a);r=MathClamp(r,0,n-o);break;case 90:s=MathClamp(s,0,i-o);r=MathClamp(r,a,n);break;case 180:s=MathClamp(s,a,i);r=MathClamp(r,o,n);break;case 270:s=MathClamp(s,o,i);r=MathClamp(r,0,n-a)}this.x=s/=i;this.y=r/=n;const[l,h]=this.getBaseTranslation();s+=l;r+=h;e.left=`${(100*s).toFixed(2)}%`;e.top=`${(100*r).toFixed(2)}%`;this.moveInDOM()}static#$e(t,e,i){switch(i){case 90:return[e,-t];case 180:return[-t,-e];case 270:return[-e,t];default:return[t,e]}}screenToPageTranslation(t,e){return AnnotationEditor.#$e(t,e,this.parentRotation)}pageTranslationToScreen(t,e){return AnnotationEditor.#$e(t,e,360-this.parentRotation)}#qe(t){switch(t){case 90:{const[t,e]=this.pageDimensions;return[0,-t/e,e/t,0]}case 180:return[-1,0,0,-1];case 270:{const[t,e]=this.pageDimensions;return[0,t/e,-e/t,0]}default:return[1,0,0,1]}}get parentScale(){return this._uiManager.viewParameters.realScale}get parentRotation(){return(this._uiManager.viewParameters.rotation+this.pageRotation)%360}get parentDimensions(){const{parentScale:t,pageDimensions:[e,i]}=this;return[e*t,i*t]}setDims(t,e){const[i,n]=this.parentDimensions,{style:s}=this.div;s.width=`${(100*t/i).toFixed(2)}%`;this.#Ce||(s.height=`${(100*e/n).toFixed(2)}%`)}fixDims(){const{style:t}=this.div,{height:e,width:i}=t,n=i.endsWith("%"),s=!this.#Ce&&e.endsWith("%");if(n&&s)return;const[r,a]=this.parentDimensions;n||(t.width=`${(100*parseFloat(i)/r).toFixed(2)}%`);this.#Ce||s||(t.height=`${(100*parseFloat(e)/a).toFixed(2)}%`)}getInitialTranslation(){return[0,0]}#Xe(){if(this.#Te)return;this.#Te=document.createElement("div");this.#Te.classList.add("resizers");const t=this._willKeepAspectRatio?["topLeft","topRight","bottomRight","bottomLeft"]:["topLeft","topMiddle","topRight","middleRight","bottomRight","bottomMiddle","bottomLeft","middleLeft"],e=this._uiManager._signal;for(const i of t){const t=document.createElement("div");this.#Te.append(t);t.classList.add("resizer",i);t.setAttribute("data-resizer-name",i);t.addEventListener("pointerdown",this.#Ke.bind(this,i),{signal:e});t.addEventListener("contextmenu",noContextMenu,{signal:e});t.tabIndex=-1}this.div.prepend(this.#Te)}#Ke(t,e){e.preventDefault();const{isMac:i}=util_FeatureTest.platform;if(0!==e.button||e.ctrlKey&&i)return;this.#o?.toggle(!1);const n=this._isDraggable;this._isDraggable=!1;this.#Me=[e.screenX,e.screenY];const s=new AbortController,r=this._uiManager.combinedSignal(s);this.parent.togglePointerEvents(!1);window.addEventListener("pointermove",this.#Ye.bind(this,t),{passive:!0,capture:!0,signal:r});window.addEventListener("touchmove",stopEvent,{passive:!1,signal:r});window.addEventListener("contextmenu",noContextMenu,{signal:r});this.#De={savedX:this.x,savedY:this.y,savedWidth:this.width,savedHeight:this.height};const a=this.parent.div.style.cursor,o=this.div.style.cursor;this.div.style.cursor=this.parent.div.style.cursor=window.getComputedStyle(e.target).cursor;const pointerUpCallback=()=>{s.abort();this.parent.togglePointerEvents(!0);this.#o?.toggle(!0);this._isDraggable=n;this.parent.div.style.cursor=a;this.div.style.cursor=o;this.#Qe()};window.addEventListener("pointerup",pointerUpCallback,{signal:r});window.addEventListener("blur",pointerUpCallback,{signal:r})}#Je(t,e,i,n){this.width=i;this.height=n;this.x=t;this.y=e;const[s,r]=this.parentDimensions;this.setDims(s*i,r*n);this.fixAndSetPosition();this._onResized()}_onResized(){}#Qe(){if(!this.#De)return;const{savedX:t,savedY:e,savedWidth:i,savedHeight:n}=this.#De;this.#De=null;const s=this.x,r=this.y,a=this.width,o=this.height;s===t&&r===e&&a===i&&o===n||this.addCommands({cmd:this.#Je.bind(this,s,r,a,o),undo:this.#Je.bind(this,t,e,i,n),mustExec:!0})}static _round(t){return Math.round(1e4*t)/1e4}#Ye(t,e){const[i,n]=this.parentDimensions,s=this.x,r=this.y,a=this.width,o=this.height,l=AnnotationEditor.MIN_SIZE/i,h=AnnotationEditor.MIN_SIZE/n,c=this.#qe(this.rotation),transf=(t,e)=>[c[0]*t+c[2]*e,c[1]*t+c[3]*e],d=this.#qe(360-this.rotation);let u,p,g=!1,f=!1;switch(t){case"topLeft":g=!0;u=(t,e)=>[0,0];p=(t,e)=>[t,e];break;case"topMiddle":u=(t,e)=>[t/2,0];p=(t,e)=>[t/2,e];break;case"topRight":g=!0;u=(t,e)=>[t,0];p=(t,e)=>[0,e];break;case"middleRight":f=!0;u=(t,e)=>[t,e/2];p=(t,e)=>[0,e/2];break;case"bottomRight":g=!0;u=(t,e)=>[t,e];p=(t,e)=>[0,0];break;case"bottomMiddle":u=(t,e)=>[t/2,e];p=(t,e)=>[t/2,0];break;case"bottomLeft":g=!0;u=(t,e)=>[0,e];p=(t,e)=>[t,0];break;case"middleLeft":f=!0;u=(t,e)=>[0,e/2];p=(t,e)=>[t,e/2]}const m=u(a,o),b=p(a,o);let v=transf(...b);const w=AnnotationEditor._round(s+v[0]),y=AnnotationEditor._round(r+v[1]);let A,x,_=1,E=1;if(e.fromKeyboard)({deltaX:A,deltaY:x}=e);else{const{screenX:t,screenY:i}=e,[n,s]=this.#Me;[A,x]=this.screenToPageTranslation(t-n,i-s);this.#Me[0]=t;this.#Me[1]=i}[A,x]=(S=A/i,C=x/n,[d[0]*S+d[2]*C,d[1]*S+d[3]*C]);var S,C;if(g){const t=Math.hypot(a,o);_=E=Math.max(Math.min(Math.hypot(b[0]-m[0]-A,b[1]-m[1]-x)/t,1/a,1/o),l/a,h/o)}else f?_=MathClamp(Math.abs(b[0]-m[0]-A),l,1)/a:E=MathClamp(Math.abs(b[1]-m[1]-x),h,1)/o;const T=AnnotationEditor._round(a*_),M=AnnotationEditor._round(o*E);v=transf(...p(T,M));const D=w-v[0],P=y-v[1];this.#Re||=[this.x,this.y,this.width,this.height];this.width=T;this.height=M;this.x=D;this.y=P;this.setDims(i*T,n*M);this.fixAndSetPosition();this._onResizing()}_onResizing(){}altTextFinish(){this.#o?.finish()}get toolbarButtons(){return null}async addEditToolbar(){if(this._editToolbar||this.#Le)return this._editToolbar;this._editToolbar=new EditorToolbar(this);this.div.append(this._editToolbar.render());this._editToolbar.addButton("comment",this.addCommentButton());const{toolbarButtons:t}=this;if(t)for(const[e,i]of t)await this._editToolbar.addButton(e,i);this._editToolbar.addButton("delete");return this._editToolbar}removeEditToolbar(){if(this._editToolbar){this._editToolbar.remove();this._editToolbar=null;this.#o?.destroy()}}addContainer(t){const e=this._editToolbar?.div;e?e.before(t):this.div.append(t)}getClientDimensions(){return this.div.getBoundingClientRect()}createAltText(){if(!this.#o){AltText.initialize(AnnotationEditor._l10n);this.#o=new AltText(this);if(this.#Ae){this.#o.data=this.#Ae;this.#Ae=null}}return this.#o}get altTextData(){return this.#o?.data}set altTextData(t){this.#o&&(this.#o.data=t)}get guessedAltText(){return this.#o?.guessedText}async setGuessedAltText(t){await(this.#o?.setGuessedText(t))}serializeAltText(t){return this.#o?.serialize(t)}hasAltText(){return!!this.#o&&!this.#o.isEmpty()}hasAltTextData(){return this.#o?.hasData()??!1}addCommentButton(){return this.#l?this.#l:this.#l=new Comment(this)}get commentColor(){return null}get comment(){const t=this.#l;return{text:t.data.text,date:t.data.date,deleted:t.isDeleted(),color:this.commentColor}}set comment(t){this.#l||(this.#l=new Comment(this));this.#l.data=t}setCommentData(t){this.#l||(this.#l=new Comment(this));this.#l.setInitialText(t)}get hasEditedComment(){return this.#l?.hasBeenEdited()}async editComment(){this.#l||(this.#l=new Comment(this));this.#l.edit()}addComment(t){this.hasEditedComment&&(t.popup={contents:this.comment.text,deleted:this.comment.deleted})}render(){const t=this.div=document.createElement("div");t.setAttribute("data-editor-rotation",(360-this.rotation)%360);t.className=this.name;t.setAttribute("id",this.id);t.tabIndex=this.#_e?-1:0;t.setAttribute("role","application");this.defaultL10nId&&t.setAttribute("data-l10n-id",this.defaultL10nId);this._isVisible||t.classList.add("hidden");this.setInForeground();this.#Ze();const[e,i]=this.parentDimensions;if(this.parentRotation%180!=0){t.style.maxWidth=`${(100*i/e).toFixed(2)}%`;t.style.maxHeight=`${(100*e/i).toFixed(2)}%`}const[n,s]=this.getInitialTranslation();this.translate(n,s);bindEvents(this,t,["keydown","pointerdown","dblclick"]);this.isResizable&&this._uiManager._supportsPinchToZoom&&(this.#ze||=new TouchManager({container:t,isPinchingDisabled:()=>!this.isSelected,onPinchStart:this.#ti.bind(this),onPinching:this.#ei.bind(this),onPinchEnd:this.#ii.bind(this),signal:this._uiManager._signal}));this._uiManager._editorUndoBar?.hide();return t}#ti(){this.#De={savedX:this.x,savedY:this.y,savedWidth:this.width,savedHeight:this.height};this.#o?.toggle(!1);this.parent.togglePointerEvents(!1)}#ei(t,e,i){let n=i/e*.7+1-.7;if(1===n)return;const s=this.#qe(this.rotation),transf=(t,e)=>[s[0]*t+s[2]*e,s[1]*t+s[3]*e],[r,a]=this.parentDimensions,o=this.x,l=this.y,h=this.width,c=this.height,d=AnnotationEditor.MIN_SIZE/r,u=AnnotationEditor.MIN_SIZE/a;n=Math.max(Math.min(n,1/h,1/c),d/h,u/c);const p=AnnotationEditor._round(h*n),g=AnnotationEditor._round(c*n);if(p===h&&g===c)return;this.#Re||=[o,l,h,c];const f=transf(h/2,c/2),m=AnnotationEditor._round(o+f[0]),b=AnnotationEditor._round(l+f[1]),v=transf(p/2,g/2);this.x=m-v[0];this.y=b-v[1];this.width=p;this.height=g;this.setDims(r*p,a*g);this.fixAndSetPosition();this._onResizing()}#ii(){this.#o?.toggle(!0);this.parent.togglePointerEvents(!0);this.#Qe()}pointerdown(t){const{isMac:e}=util_FeatureTest.platform;if(0!==t.button||t.ctrlKey&&e)t.preventDefault();else{this.#Ie=!0;this._isDraggable?this.#ni(t):this.#si(t)}}#si(t){const{isMac:e}=util_FeatureTest.platform;t.ctrlKey&&!e||t.shiftKey||t.metaKey&&e?this.parent.toggleSelected(this):this.parent.setSelected(this)}#ni(t){const{isSelected:e}=this;this._uiManager.setUpDragSession();let i=!1;const n=new AbortController,s=this._uiManager.combinedSignal(n),r={capture:!0,passive:!1,signal:s},cancelDrag=t=>{n.abort();this.#Ee=null;this.#Ie=!1;this._uiManager.endDragSession()||this.#si(t);i&&this._onStopDragging()};if(e){this.#Be=t.clientX;this.#Ue=t.clientY;this.#Ee=t.pointerId;this.#Se=t.pointerType;window.addEventListener("pointermove",(t=>{if(!i){i=!0;this._onStartDragging()}const{clientX:e,clientY:n,pointerId:s}=t;if(s!==this.#Ee){stopEvent(t);return}const[r,a]=this.screenToPageTranslation(e-this.#Be,n-this.#Ue);this.#Be=e;this.#Ue=n;this._uiManager.dragSelectedEditors(r,a)}),r);window.addEventListener("touchmove",stopEvent,r);window.addEventListener("pointerdown",(t=>{t.pointerType===this.#Se&&(this.#ze||t.isPrimary)&&cancelDrag(t);stopEvent(t)}),r)}const pointerUpCallback=t=>{this.#Ee&&this.#Ee!==t.pointerId?stopEvent(t):cancelDrag(t)};window.addEventListener("pointerup",pointerUpCallback,{signal:s});window.addEventListener("blur",pointerUpCallback,{signal:s})}_onStartDragging(){}_onStopDragging(){}moveInDOM(){this.#Ne&&clearTimeout(this.#Ne);this.#Ne=setTimeout((()=>{this.#Ne=null;this.parent?.moveEditorInDOM(this)}),0)}_setParentAndPosition(t,e,i){t.changeParent(this);this.x=e;this.y=i;this.fixAndSetPosition();this._onTranslated()}getRect(t,e,i=this.rotation){const n=this.parentScale,[s,r]=this.pageDimensions,[a,o]=this.pageTranslation,l=t/n,h=e/n,c=this.x*s,d=this.y*r,u=this.width*s,p=this.height*r;switch(i){case 0:return[c+l+a,r-d-h-p+o,c+l+u+a,r-d-h+o];case 90:return[c+h+a,r-d+l+o,c+h+p+a,r-d+l+u+o];case 180:return[c-l-u+a,r-d+h+o,c-l+a,r-d+h+p+o];case 270:return[c-h-p+a,r-d-l-u+o,c-h+a,r-d-l+o];default:throw new Error("Invalid rotation")}}getRectInCurrentCoords(t,e){const[i,n,s,r]=t,a=s-i,o=r-n;switch(this.rotation){case 0:return[i,e-r,a,o];case 90:return[i,e-n,o,a];case 180:return[s,e-n,a,o];case 270:return[s,e-r,o,a];default:throw new Error("Invalid rotation")}}onceAdded(t){}isEmpty(){return!1}enableEditMode(){if(this.isInEditMode())return!1;this.parent.setEditingState(!1);this.#Le=!0;return!0}disableEditMode(){if(!this.isInEditMode())return!1;this.parent.setEditingState(!0);this.#Le=!1;return!0}isInEditMode(){return this.#Le}shouldGetKeyboardEvents(){return this.#Oe}needsToBeRebuilt(){return this.div&&!this.isAttachedToDOM}get isOnScreen(){const{top:t,left:e,bottom:i,right:n}=this.getClientDimensions(),{innerHeight:s,innerWidth:r}=window;return e<r&&n>0&&t<s&&i>0}#Ze(){if(this.#Pe||!this.div)return;this.#Pe=new AbortController;const t=this._uiManager.combinedSignal(this.#Pe);this.div.addEventListener("focusin",this.focusin.bind(this),{signal:t});this.div.addEventListener("focusout",this.focusout.bind(this),{signal:t})}rebuild(){this.#Ze()}rotate(t){}resize(){}serializeDeleted(){return{id:this.annotationElementId,deleted:!0,pageIndex:this.pageIndex,popupRef:this._initialData?.popupRef||""}}serialize(t=!1,e=null){unreachable("An editor must be serializable")}static async deserialize(t,e,i){const n=new this.prototype.constructor({parent:e,id:e.getNextId(),uiManager:i,annotationElementId:t.annotationElementId});n.rotation=t.rotation;n.#Ae=t.accessibilityData;n._isCopy=t.isCopy||!1;const[s,r]=n.pageDimensions,[a,o,l,h]=n.getRectInCurrentCoords(t.rect,r);n.x=a/s;n.y=o/r;n.width=l/s;n.height=h/r;return n}get hasBeenModified(){return!!this.annotationElementId&&(this.deleted||null!==this.serialize())}remove(){this.#Pe?.abort();this.#Pe=null;this.isEmpty()||this.commit();this.parent?this.parent.remove(this):this._uiManager.removeEditor(this);if(this.#Ne){clearTimeout(this.#Ne);this.#Ne=null}this.#We();this.removeEditToolbar();if(this.#He){for(const t of this.#He.values())clearTimeout(t);this.#He=null}this.parent=null;this.#ze?.destroy();this.#ze=null}get isResizable(){return!1}makeResizable(){if(this.isResizable){this.#Xe();this.#Te.classList.remove("hidden")}}get toolbarPosition(){return null}keydown(t){if(!this.isResizable||t.target!==this.div||"Enter"!==t.key)return;this._uiManager.setSelected(this);this.#De={savedX:this.x,savedY:this.y,savedWidth:this.width,savedHeight:this.height};const e=this.#Te.children;if(!this.#xe){this.#xe=Array.from(e);const t=this.#ri.bind(this),i=this.#ai.bind(this),n=this._uiManager._signal;for(const e of this.#xe){const s=e.getAttribute("data-resizer-name");e.setAttribute("role","spinbutton");e.addEventListener("keydown",t,{signal:n});e.addEventListener("blur",i,{signal:n});e.addEventListener("focus",this.#oi.bind(this,s),{signal:n});e.setAttribute("data-l10n-id",AnnotationEditor._l10nResizer[s])}}const i=this.#xe[0];let n=0;for(const t of e){if(t===i)break;n++}const s=(360-this.rotation+this.parentRotation)%360/90*(this.#xe.length/4);if(s!==n){if(s<n)for(let t=0;t<n-s;t++)this.#Te.append(this.#Te.firstChild);else if(s>n)for(let t=0;t<s-n;t++)this.#Te.firstChild.before(this.#Te.lastChild);let t=0;for(const i of e){const e=this.#xe[t++].getAttribute("data-resizer-name");i.setAttribute("data-l10n-id",AnnotationEditor._l10nResizer[e])}}this.#li(0);this.#Oe=!0;this.#Te.firstChild.focus({focusVisible:!0});t.preventDefault();t.stopImmediatePropagation()}#ri(t){AnnotationEditor._resizerKeyboardManager.exec(this,t)}#ai(t){this.#Oe&&t.relatedTarget?.parentNode!==this.#Te&&this.#We()}#oi(t){this.#ke=this.#Oe?t:""}#li(t){if(this.#xe)for(const e of this.#xe)e.tabIndex=t}_resizeWithKeyboard(t,e){this.#Oe&&this.#Ye(this.#ke,{deltaX:t,deltaY:e,fromKeyboard:!0})}#We(){this.#Oe=!1;this.#li(-1);this.#Qe()}_stopResizingWithKeyboard(){this.#We();this.div.focus()}select(){if(!this.isSelected||!this._editToolbar){this.isSelected=!0;this.makeResizable();this.div?.classList.add("selectedEditor");if(this._editToolbar){this._editToolbar?.show();this.#o?.toggleAltTextBadge(!1)}else this.addEditToolbar().then((()=>{this.div?.classList.contains("selectedEditor")&&this._editToolbar?.show()}))}}unselect(){if(this.isSelected){this.isSelected=!1;this.#Te?.classList.add("hidden");this.div?.classList.remove("selectedEditor");this.div?.contains(document.activeElement)&&this._uiManager.currentLayer.div.focus({preventScroll:!0});this._editToolbar?.hide();this.#o?.toggleAltTextBadge(!0)}}updateParams(t,e){}disableEditing(){}enableEditing(){}get canChangeContent(){return!1}enterInEditMode(){if(this.canChangeContent){this.enableEditMode();this.div.focus()}}dblclick(t){this.enterInEditMode();this.parent.updateToolbar({mode:this.constructor._editorType,editId:this.id})}getElementForAltText(){return this.div}get contentDiv(){return this.div}get isEditing(){return this.#Fe}set isEditing(t){this.#Fe=t;if(this.parent)if(t){this.parent.setSelected(this);this.parent.setActiveEditor(this)}else this.parent.setActiveEditor(null)}setAspectRatio(t,e){this.#Ce=!0;const i=t/e,{style:n}=this.div;n.aspectRatio=i;n.height="auto"}static get MIN_SIZE(){return 16}static canCreateNewEmptyEditor(){return!0}get telemetryInitialData(){return{action:"added"}}get telemetryFinalData(){return null}_reportTelemetry(t,e=!1){if(e){this.#He||=new Map;const{action:e}=t;let i=this.#He.get(e);i&&clearTimeout(i);i=setTimeout((()=>{this._reportTelemetry(t);this.#He.delete(e);0===this.#He.size&&(this.#He=null)}),AnnotationEditor._telemetryTimeout);this.#He.set(e,i)}else{t.type||=this.editorType;this._uiManager._eventBus.dispatch("reporttelemetry",{source:this,details:{type:"editing",data:t}})}}show(t=this._isVisible){this.div.classList.toggle("hidden",!t);this._isVisible=t}enable(){this.div&&(this.div.tabIndex=0);this.#_e=!1}disable(){this.div&&(this.div.tabIndex=-1);this.#_e=!0}renderAnnotationElement(t){let e=t.container.querySelector(".annotationContent");if(e){if("CANVAS"===e.nodeName){const t=e;e=document.createElement("div");e.classList.add("annotationContent",this.editorType);t.before(e)}}else{e=document.createElement("div");e.classList.add("annotationContent",this.editorType);t.container.prepend(e)}return e}resetAnnotationElement(t){const{firstChild:e}=t.container;"DIV"===e?.nodeName&&e.classList.contains("annotationContent")&&e.remove()}}class FakeEditor extends AnnotationEditor{constructor(t){super(t);this.annotationElementId=t.annotationElementId;this.deleted=!0}serialize(){return this.serializeDeleted()}}const q=3285377520,X=4294901760,K=65535;class MurmurHash3_64{constructor(t){this.h1=t?4294967295&t:q;this.h2=t?4294967295&t:q}update(t){let e,i;if("string"==typeof t){e=new Uint8Array(2*t.length);i=0;for(let n=0,s=t.length;n<s;n++){const s=t.charCodeAt(n);if(s<=255)e[i++]=s;else{e[i++]=s>>>8;e[i++]=255&s}}}else{if(!ArrayBuffer.isView(t))throw new Error("Invalid data format, must be a string or TypedArray.");e=t.slice();i=e.byteLength}const n=i>>2,s=i-4*n,r=new Uint32Array(e.buffer,0,n);let a=0,o=0,l=this.h1,h=this.h2;const c=3432918353,d=461845907,u=11601,p=13715;for(let t=0;t<n;t++)if(1&t){a=r[t];a=a*c&X|a*u&K;a=a<<15|a>>>17;a=a*d&X|a*p&K;l^=a;l=l<<13|l>>>19;l=5*l+3864292196}else{o=r[t];o=o*c&X|o*u&K;o=o<<15|o>>>17;o=o*d&X|o*p&K;h^=o;h=h<<13|h>>>19;h=5*h+3864292196}a=0;switch(s){case 3:a^=e[4*n+2]<<16;case 2:a^=e[4*n+1]<<8;case 1:a^=e[4*n];a=a*c&X|a*u&K;a=a<<15|a>>>17;a=a*d&X|a*p&K;1&n?l^=a:h^=a}this.h1=l;this.h2=h}hexdigest(){let t=this.h1,e=this.h2;t^=e>>>1;t=3981806797*t&X|36045*t&K;e=4283543511*e&X|(2950163797*(e<<16|t>>>16)&X)>>>16;t^=e>>>1;t=444984403*t&X|60499*t&K;e=3301882366*e&X|(3120437893*(e<<16|t>>>16)&X)>>>16;t^=e>>>1;return(t>>>0).toString(16).padStart(8,"0")+(e>>>0).toString(16).padStart(8,"0")}}const Y=Object.freeze({map:null,hash:"",transfer:void 0});class AnnotationStorage{#hi=!1;#ci=null;#di=new Map;constructor(){this.onSetModified=null;this.onResetModified=null;this.onAnnotationEditor=null}getValue(t,e){const i=this.#di.get(t);return void 0===i?e:Object.assign(e,i)}getRawValue(t){return this.#di.get(t)}remove(t){this.#di.delete(t);0===this.#di.size&&this.resetModified();if("function"==typeof this.onAnnotationEditor){for(const t of this.#di.values())if(t instanceof AnnotationEditor)return;this.onAnnotationEditor(null)}}setValue(t,e){const i=this.#di.get(t);let n=!1;if(void 0!==i){for(const[t,s]of Object.entries(e))if(i[t]!==s){n=!0;i[t]=s}}else{n=!0;this.#di.set(t,e)}n&&this.#ui();e instanceof AnnotationEditor&&"function"==typeof this.onAnnotationEditor&&this.onAnnotationEditor(e.constructor._type)}has(t){return this.#di.has(t)}get size(){return this.#di.size}#ui(){if(!this.#hi){this.#hi=!0;"function"==typeof this.onSetModified&&this.onSetModified()}}resetModified(){if(this.#hi){this.#hi=!1;"function"==typeof this.onResetModified&&this.onResetModified()}}get print(){return new PrintAnnotationStorage(this)}get serializable(){if(0===this.#di.size)return Y;const t=new Map,e=new MurmurHash3_64,i=[],n=Object.create(null);let s=!1;for(const[i,r]of this.#di){const a=r instanceof AnnotationEditor?r.serialize(!1,n):r;if(a){t.set(i,a);e.update(`${i}:${JSON.stringify(a)}`);s||=!!a.bitmap}}if(s)for(const e of t.values())e.bitmap&&i.push(e.bitmap);return t.size>0?{map:t,hash:e.hexdigest(),transfer:i}:Y}get editorStats(){let t=null;const e=new Map;for(const i of this.#di.values()){if(!(i instanceof AnnotationEditor))continue;const n=i.telemetryFinalData;if(!n)continue;const{type:s}=n;e.has(s)||e.set(s,Object.getPrototypeOf(i).constructor);t||=Object.create(null);const r=t[s]||=new Map;for(const[t,e]of Object.entries(n)){if("type"===t)continue;let i=r.get(t);if(!i){i=new Map;r.set(t,i)}const n=i.get(e)??0;i.set(e,n+1)}}for(const[i,n]of e)t[i]=n.computeTelemetryFinalData(t[i]);return t}resetModifiedIds(){this.#ci=null}get modifiedIds(){if(this.#ci)return this.#ci;const t=[];for(const e of this.#di.values())e instanceof AnnotationEditor&&e.annotationElementId&&e.serialize()&&t.push(e.annotationElementId);return this.#ci={ids:new Set(t),hash:t.join(",")}}[Symbol.iterator](){return this.#di.entries()}}class PrintAnnotationStorage extends AnnotationStorage{#pi;constructor(t){super();const{map:e,hash:i,transfer:n}=t.serializable,s=structuredClone(e,n?{transfer:n}:null);this.#pi={map:s,hash:i,transfer:n}}get print(){unreachable("Should not call PrintAnnotationStorage.print")}get serializable(){return this.#pi}get modifiedIds(){return shadow(this,"modifiedIds",{ids:new Set,hash:""})}}class FontLoader{#gi=new Set;constructor({ownerDocument:t=globalThis.document,styleElement:e=null}){this._document=t;this.nativeFontFaces=new Set;this.styleElement=null;this.loadingRequests=[];this.loadTestFontId=0}addNativeFontFace(t){this.nativeFontFaces.add(t);this._document.fonts.add(t)}removeNativeFontFace(t){this.nativeFontFaces.delete(t);this._document.fonts.delete(t)}insertRule(t){if(!this.styleElement){this.styleElement=this._document.createElement("style");this._document.documentElement.getElementsByTagName("head")[0].append(this.styleElement)}const e=this.styleElement.sheet;e.insertRule(t,e.cssRules.length)}clear(){for(const t of this.nativeFontFaces)this._document.fonts.delete(t);this.nativeFontFaces.clear();this.#gi.clear();if(this.styleElement){this.styleElement.remove();this.styleElement=null}}async loadSystemFont({systemFontInfo:t,disableFontFace:e,_inspectFont:i}){if(t&&!this.#gi.has(t.loadedName)){assert(!e,"loadSystemFont shouldn't be called when `disableFontFace` is set.");if(this.isFontLoadingAPISupported){const{loadedName:e,src:n,style:s}=t,r=new FontFace(e,n,s);this.addNativeFontFace(r);try{await r.load();this.#gi.add(e);i?.(t)}catch{warn(`Cannot load system font: ${t.baseFontName}, installing it could help to improve PDF rendering.`);this.removeNativeFontFace(r)}}else unreachable("Not implemented: loadSystemFont without the Font Loading API.")}}async bind(t){if(t.attached||t.missingFile&&!t.systemFontInfo)return;t.attached=!0;if(t.systemFontInfo){await this.loadSystemFont(t);return}if(this.isFontLoadingAPISupported){const e=t.createNativeFontFace();if(e){this.addNativeFontFace(e);try{await e.loaded}catch(i){warn(`Failed to load font '${e.family}': '${i}'.`);t.disableFontFace=!0;throw i}}return}const e=t.createFontFaceRule();if(e){this.insertRule(e);if(this.isSyncFontLoadingSupported)return;await new Promise((e=>{const i=this._queueLoadingCallback(e);this._prepareFontLoadEvent(t,i)}))}}get isFontLoadingAPISupported(){return shadow(this,"isFontLoadingAPISupported",!!this._document?.fonts)}get isSyncFontLoadingSupported(){return shadow(this,"isSyncFontLoadingSupported",i||util_FeatureTest.platform.isFirefox)}_queueLoadingCallback(t){const{loadingRequests:e}=this,i={done:!1,complete:function completeRequest(){assert(!i.done,"completeRequest() cannot be called twice.");i.done=!0;for(;e.length>0&&e[0].done;){const t=e.shift();setTimeout(t.callback,0)}},callback:t};e.push(i);return i}get _loadTestFont(){return shadow(this,"_loadTestFont",atob("T1RUTwALAIAAAwAwQ0ZGIDHtZg4AAAOYAAAAgUZGVE1lkzZwAAAEHAAAABxHREVGABQAFQAABDgAAAAeT1MvMlYNYwkAAAEgAAAAYGNtYXABDQLUAAACNAAAAUJoZWFk/xVFDQAAALwAAAA2aGhlYQdkA+oAAAD0AAAAJGhtdHgD6AAAAAAEWAAAAAZtYXhwAAJQAAAAARgAAAAGbmFtZVjmdH4AAAGAAAAAsXBvc3T/hgAzAAADeAAAACAAAQAAAAEAALZRFsRfDzz1AAsD6AAAAADOBOTLAAAAAM4KHDwAAAAAA+gDIQAAAAgAAgAAAAAAAAABAAADIQAAAFoD6AAAAAAD6AABAAAAAAAAAAAAAAAAAAAAAQAAUAAAAgAAAAQD6AH0AAUAAAKKArwAAACMAooCvAAAAeAAMQECAAACAAYJAAAAAAAAAAAAAQAAAAAAAAAAAAAAAFBmRWQAwAAuAC4DIP84AFoDIQAAAAAAAQAAAAAAAAAAACAAIAABAAAADgCuAAEAAAAAAAAAAQAAAAEAAAAAAAEAAQAAAAEAAAAAAAIAAQAAAAEAAAAAAAMAAQAAAAEAAAAAAAQAAQAAAAEAAAAAAAUAAQAAAAEAAAAAAAYAAQAAAAMAAQQJAAAAAgABAAMAAQQJAAEAAgABAAMAAQQJAAIAAgABAAMAAQQJAAMAAgABAAMAAQQJAAQAAgABAAMAAQQJAAUAAgABAAMAAQQJAAYAAgABWABYAAAAAAAAAwAAAAMAAAAcAAEAAAAAADwAAwABAAAAHAAEACAAAAAEAAQAAQAAAC7//wAAAC7////TAAEAAAAAAAABBgAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAMAAAAAAAD/gwAyAAAAAQAAAAAAAAAAAAAAAAAAAAABAAQEAAEBAQJYAAEBASH4DwD4GwHEAvgcA/gXBIwMAYuL+nz5tQXkD5j3CBLnEQACAQEBIVhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYAAABAQAADwACAQEEE/t3Dov6fAH6fAT+fPp8+nwHDosMCvm1Cvm1DAz6fBQAAAAAAAABAAAAAMmJbzEAAAAAzgTjFQAAAADOBOQpAAEAAAAAAAAADAAUAAQAAAABAAAAAgABAAAAAAAAAAAD6AAAAAAAAA=="))}_prepareFontLoadEvent(t,e){function int32(t,e){return t.charCodeAt(e)<<24|t.charCodeAt(e+1)<<16|t.charCodeAt(e+2)<<8|255&t.charCodeAt(e+3)}function spliceString(t,e,i,n){return t.substring(0,e)+n+t.substring(e+i)}let i,n;const s=this._document.createElement("canvas");s.width=1;s.height=1;const r=s.getContext("2d");let a=0;const o=`lt${Date.now()}${this.loadTestFontId++}`;let l=this._loadTestFont;l=spliceString(l,976,o.length,o);const h=1482184792;let c=int32(l,16);for(i=0,n=o.length-3;i<n;i+=4)c=c-h+int32(o,i)|0;i<o.length&&(c=c-h+int32(o+"XXX",i)|0);l=spliceString(l,16,4,function string32(t){return String.fromCharCode(t>>24&255,t>>16&255,t>>8&255,255&t)}(c));const d=`@font-face {font-family:"${o}";src:${`url(data:font/opentype;base64,${btoa(l)});`}}`;this.insertRule(d);const u=this._document.createElement("div");u.style.visibility="hidden";u.style.width=u.style.height="10px";u.style.position="absolute";u.style.top=u.style.left="0px";for(const e of[t.loadedName,o]){const t=this._document.createElement("span");t.textContent="Hi";t.style.fontFamily=e;u.append(t)}this._document.body.append(u);!function isFontReady(t,e){if(++a>30){warn("Load test font never loaded.");e();return}r.font="30px "+t;r.fillText(".",0,20);r.getImageData(0,0,1,1).data[3]>0?e():setTimeout(isFontReady.bind(null,t,e))}(o,(()=>{u.remove();e.complete()}))}}class FontFaceObject{constructor(t,e=null){this.compiledGlyphs=Object.create(null);for(const e in t)this[e]=t[e];this._inspectFont=e}createNativeFontFace(){if(!this.data||this.disableFontFace)return null;let t;if(this.cssFontInfo){const e={weight:this.cssFontInfo.fontWeight};this.cssFontInfo.italicAngle&&(e.style=`oblique ${this.cssFontInfo.italicAngle}deg`);t=new FontFace(this.cssFontInfo.fontFamily,this.data,e)}else t=new FontFace(this.loadedName,this.data,{});this._inspectFont?.(this);return t}createFontFaceRule(){if(!this.data||this.disableFontFace)return null;const t=`url(data:${this.mimetype};base64,${toBase64Util(this.data)});`;let e;if(this.cssFontInfo){let i=`font-weight: ${this.cssFontInfo.fontWeight};`;this.cssFontInfo.italicAngle&&(i+=`font-style: oblique ${this.cssFontInfo.italicAngle}deg;`);e=`@font-face {font-family:"${this.cssFontInfo.fontFamily}";${i}src:${t}}`}else e=`@font-face {font-family:"${this.loadedName}";src:${t}}`;this._inspectFont?.(this,t);return e}getPathGenerator(t,e){if(void 0!==this.compiledGlyphs[e])return this.compiledGlyphs[e];const i=this.loadedName+"_path_"+e;let n;try{n=t.get(i)}catch(t){warn(`getPathGenerator - ignoring character: "${t}".`)}const s=new Path2D(n||"");this.fontExtraProperties||t.delete(i);return this.compiledGlyphs[e]=s}}function getFactoryUrlProp(t){if("string"!=typeof t)return null;if(t.endsWith("/"))return t;throw new Error(`Invalid factory url: "${t}" must include trailing slash.`)}const isRefProxy=t=>"object"==typeof t&&Number.isInteger(t?.num)&&t.num>=0&&Number.isInteger(t?.gen)&&t.gen>=0,Q=function _isValidExplicitDest(t,e,i){if(!Array.isArray(i)||i.length<2)return!1;const[n,s,...r]=i;if(!t(n)&&!Number.isInteger(n))return!1;if(!e(s))return!1;const a=r.length;let o=!0;switch(s.name){case"XYZ":if(a<2||a>3)return!1;break;case"Fit":case"FitB":return 0===a;case"FitH":case"FitBH":case"FitV":case"FitBV":if(a>1)return!1;break;case"FitR":if(4!==a)return!1;o=!1;break;default:return!1}for(const t of r)if(!("number"==typeof t||o&&null===t))return!1;return!0}.bind(null,isRefProxy,(t=>"object"==typeof t&&"string"==typeof t?.name));class LoopbackPort{#fi=new Map;#mi=Promise.resolve();postMessage(t,e){const i={data:structuredClone(t,e?{transfer:e}:null)};this.#mi.then((()=>{for(const[t]of this.#fi)t.call(this,i)}))}addEventListener(t,e,i=null){let n=null;if(i?.signal instanceof AbortSignal){const{signal:s}=i;if(s.aborted){warn("LoopbackPort - cannot use an `aborted` signal.");return}const onAbort=()=>this.removeEventListener(t,e);n=()=>s.removeEventListener("abort",onAbort);s.addEventListener("abort",onAbort)}this.#fi.set(e,n)}removeEventListener(t,e){const i=this.#fi.get(e);i?.();this.#fi.delete(e)}terminate(){for(const[,t]of this.#fi)t?.();this.#fi.clear()}}const J=1,Z=2,tt=1,et=2,it=3,nt=4,st=5,rt=6,ot=7,lt=8;function onFn(){}function wrapReason(t){if(t instanceof AbortException||t instanceof InvalidPDFException||t instanceof PasswordException||t instanceof ResponseException||t instanceof UnknownErrorException)return t;t instanceof Error||"object"==typeof t&&null!==t||unreachable('wrapReason: Expected "reason" to be a (possibly cloned) Error.');switch(t.name){case"AbortException":return new AbortException(t.message);case"InvalidPDFException":return new InvalidPDFException(t.message);case"PasswordException":return new PasswordException(t.message,t.code);case"ResponseException":return new ResponseException(t.message,t.status,t.missing);case"UnknownErrorException":return new UnknownErrorException(t.message,t.details)}return new UnknownErrorException(t.message,t.toString())}class MessageHandler{#bi=new AbortController;constructor(t,e,i){this.sourceName=t;this.targetName=e;this.comObj=i;this.callbackId=1;this.streamId=1;this.streamSinks=Object.create(null);this.streamControllers=Object.create(null);this.callbackCapabilities=Object.create(null);this.actionHandler=Object.create(null);i.addEventListener("message",this.#vi.bind(this),{signal:this.#bi.signal})}#vi({data:t}){if(t.targetName!==this.sourceName)return;if(t.stream){this.#wi(t);return}if(t.callback){const e=t.callbackId,i=this.callbackCapabilities[e];if(!i)throw new Error(`Cannot resolve callback ${e}`);delete this.callbackCapabilities[e];if(t.callback===J)i.resolve(t.data);else{if(t.callback!==Z)throw new Error("Unexpected callback case");i.reject(wrapReason(t.reason))}return}const e=this.actionHandler[t.action];if(!e)throw new Error(`Unknown action from worker: ${t.action}`);if(t.callbackId){const i=this.sourceName,n=t.sourceName,s=this.comObj;Promise.try(e,t.data).then((function(e){s.postMessage({sourceName:i,targetName:n,callback:J,callbackId:t.callbackId,data:e})}),(function(e){s.postMessage({sourceName:i,targetName:n,callback:Z,callbackId:t.callbackId,reason:wrapReason(e)})}))}else t.streamId?this.#yi(t):e(t.data)}on(t,e){const i=this.actionHandler;if(i[t])throw new Error(`There is already an actionName called "${t}"`);i[t]=e}send(t,e,i){this.comObj.postMessage({sourceName:this.sourceName,targetName:this.targetName,action:t,data:e},i)}sendWithPromise(t,e,i){const n=this.callbackId++,s=Promise.withResolvers();this.callbackCapabilities[n]=s;try{this.comObj.postMessage({sourceName:this.sourceName,targetName:this.targetName,action:t,callbackId:n,data:e},i)}catch(t){s.reject(t)}return s.promise}sendWithStream(t,e,i,n){const s=this.streamId++,r=this.sourceName,a=this.targetName,o=this.comObj;return new ReadableStream({start:i=>{const l=Promise.withResolvers();this.streamControllers[s]={controller:i,startCall:l,pullCall:null,cancelCall:null,isClosed:!1};o.postMessage({sourceName:r,targetName:a,action:t,streamId:s,data:e,desiredSize:i.desiredSize},n);return l.promise},pull:t=>{const e=Promise.withResolvers();this.streamControllers[s].pullCall=e;o.postMessage({sourceName:r,targetName:a,stream:rt,streamId:s,desiredSize:t.desiredSize});return e.promise},cancel:t=>{assert(t instanceof Error,"cancel must have a valid reason");const e=Promise.withResolvers();this.streamControllers[s].cancelCall=e;this.streamControllers[s].isClosed=!0;o.postMessage({sourceName:r,targetName:a,stream:tt,streamId:s,reason:wrapReason(t)});return e.promise}},i)}#yi(t){const e=t.streamId,i=this.sourceName,n=t.sourceName,s=this.comObj,r=this,a=this.actionHandler[t.action],o={enqueue(t,r=1,a){if(this.isCancelled)return;const o=this.desiredSize;this.desiredSize-=r;if(o>0&&this.desiredSize<=0){this.sinkCapability=Promise.withResolvers();this.ready=this.sinkCapability.promise}s.postMessage({sourceName:i,targetName:n,stream:nt,streamId:e,chunk:t},a)},close(){if(!this.isCancelled){this.isCancelled=!0;s.postMessage({sourceName:i,targetName:n,stream:it,streamId:e});delete r.streamSinks[e]}},error(t){assert(t instanceof Error,"error must have a valid reason");if(!this.isCancelled){this.isCancelled=!0;s.postMessage({sourceName:i,targetName:n,stream:st,streamId:e,reason:wrapReason(t)})}},sinkCapability:Promise.withResolvers(),onPull:null,onCancel:null,isCancelled:!1,desiredSize:t.desiredSize,ready:null};o.sinkCapability.resolve();o.ready=o.sinkCapability.promise;this.streamSinks[e]=o;Promise.try(a,t.data,o).then((function(){s.postMessage({sourceName:i,targetName:n,stream:lt,streamId:e,success:!0})}),(function(t){s.postMessage({sourceName:i,targetName:n,stream:lt,streamId:e,reason:wrapReason(t)})}))}#wi(t){const e=t.streamId,i=this.sourceName,n=t.sourceName,s=this.comObj,r=this.streamControllers[e],a=this.streamSinks[e];switch(t.stream){case lt:t.success?r.startCall.resolve():r.startCall.reject(wrapReason(t.reason));break;case ot:t.success?r.pullCall.resolve():r.pullCall.reject(wrapReason(t.reason));break;case rt:if(!a){s.postMessage({sourceName:i,targetName:n,stream:ot,streamId:e,success:!0});break}a.desiredSize<=0&&t.desiredSize>0&&a.sinkCapability.resolve();a.desiredSize=t.desiredSize;Promise.try(a.onPull||onFn).then((function(){s.postMessage({sourceName:i,targetName:n,stream:ot,streamId:e,success:!0})}),(function(t){s.postMessage({sourceName:i,targetName:n,stream:ot,streamId:e,reason:wrapReason(t)})}));break;case nt:assert(r,"enqueue should have stream controller");if(r.isClosed)break;r.controller.enqueue(t.chunk);break;case it:assert(r,"close should have stream controller");if(r.isClosed)break;r.isClosed=!0;r.controller.close();this.#Ai(r,e);break;case st:assert(r,"error should have stream controller");r.controller.error(wrapReason(t.reason));this.#Ai(r,e);break;case et:t.success?r.cancelCall.resolve():r.cancelCall.reject(wrapReason(t.reason));this.#Ai(r,e);break;case tt:if(!a)break;const o=wrapReason(t.reason);Promise.try(a.onCancel||onFn,o).then((function(){s.postMessage({sourceName:i,targetName:n,stream:et,streamId:e,success:!0})}),(function(t){s.postMessage({sourceName:i,targetName:n,stream:et,streamId:e,reason:wrapReason(t)})}));a.sinkCapability.reject(o);a.isCancelled=!0;delete this.streamSinks[e];break;default:throw new Error("Unexpected stream case")}}async#Ai(t,e){await Promise.allSettled([t.startCall?.promise,t.pullCall?.promise,t.cancelCall?.promise]);delete this.streamControllers[e]}destroy(){this.#bi?.abort();this.#bi=null}}class BaseCanvasFactory{#xi=!1;constructor({enableHWA:t=!1}){this.#xi=t}create(t,e){if(t<=0||e<=0)throw new Error("Invalid canvas size");const i=this._createCanvas(t,e);return{canvas:i,context:i.getContext("2d",{willReadFrequently:!this.#xi})}}reset(t,e,i){if(!t.canvas)throw new Error("Canvas is not specified");if(e<=0||i<=0)throw new Error("Invalid canvas size");t.canvas.width=e;t.canvas.height=i}destroy(t){if(!t.canvas)throw new Error("Canvas is not specified");t.canvas.width=0;t.canvas.height=0;t.canvas=null;t.context=null}_createCanvas(t,e){unreachable("Abstract method `_createCanvas` called.")}}class DOMCanvasFactory extends BaseCanvasFactory{constructor({ownerDocument:t=globalThis.document,enableHWA:e=!1}){super({enableHWA:e});this._document=t}_createCanvas(t,e){const i=this._document.createElement("canvas");i.width=t;i.height=e;return i}}class BaseCMapReaderFactory{constructor({baseUrl:t=null,isCompressed:e=!0}){this.baseUrl=t;this.isCompressed=e}async fetch({name:t}){if(!this.baseUrl)throw new Error("Ensure that the `cMapUrl` and `cMapPacked` API parameters are provided.");if(!t)throw new Error("CMap name must be specified.");const e=this.baseUrl+t+(this.isCompressed?".bcmap":"");return this._fetch(e).then((t=>({cMapData:t,isCompressed:this.isCompressed}))).catch((t=>{throw new Error(`Unable to load ${this.isCompressed?"binary ":""}CMap at: ${e}`)}))}async _fetch(t){unreachable("Abstract method `_fetch` called.")}}class DOMCMapReaderFactory extends BaseCMapReaderFactory{async _fetch(t){const e=await fetchData(t,this.isCompressed?"arraybuffer":"text");return e instanceof ArrayBuffer?new Uint8Array(e):stringToBytes(e)}}__webpack_require__(2489);class BaseFilterFactory{addFilter(t){return"none"}addHCMFilter(t,e){return"none"}addAlphaFilter(t){return"none"}addLuminosityFilter(t){return"none"}addHighlightHCMFilter(t,e,i,n,s){return"none"}destroy(t=!1){}}class DOMFilterFactory extends BaseFilterFactory{#_i;#Ei;#Si;#Ci;#Ti;#Mi;#y=0;constructor({docId:t,ownerDocument:e=globalThis.document}){super();this.#Ci=t;this.#Ti=e}get#x(){return this.#Ei||=new Map}get#Di(){return this.#Mi||=new Map}get#Pi(){if(!this.#Si){const t=this.#Ti.createElement("div"),{style:e}=t;e.visibility="hidden";e.contain="strict";e.width=e.height=0;e.position="absolute";e.top=e.left=0;e.zIndex=-1;const i=this.#Ti.createElementNS(V,"svg");i.setAttribute("width",0);i.setAttribute("height",0);this.#Si=this.#Ti.createElementNS(V,"defs");t.append(i);i.append(this.#Si);this.#Ti.body.append(t)}return this.#Si}#ki(t){if(1===t.length){const e=t[0],i=new Array(256);for(let t=0;t<256;t++)i[t]=e[t]/255;const n=i.join(",");return[n,n,n]}const[e,i,n]=t,s=new Array(256),r=new Array(256),a=new Array(256);for(let t=0;t<256;t++){s[t]=e[t]/255;r[t]=i[t]/255;a[t]=n[t]/255}return[s.join(","),r.join(","),a.join(",")]}#Ii(t){if(void 0===this.#_i){this.#_i="";const t=this.#Ti.URL;t!==this.#Ti.baseURI&&(isDataScheme(t)?warn('#createUrl: ignore "data:"-URL for performance reasons.'):this.#_i=updateUrlHash(t,""))}return`url(${this.#_i}#${t})`}addFilter(t){if(!t)return"none";let e=this.#x.get(t);if(e)return e;const[i,n,s]=this.#ki(t),r=1===t.length?i:`${i}${n}${s}`;e=this.#x.get(r);if(e){this.#x.set(t,e);return e}const a=`g_${this.#Ci}_transfer_map_${this.#y++}`,o=this.#Ii(a);this.#x.set(t,o);this.#x.set(r,o);const l=this.#Ri(a);this.#Fi(i,n,s,l);return o}addHCMFilter(t,e){const i=`${t}-${e}`,n="base";let s=this.#Di.get(n);if(s?.key===i)return s.url;if(s){s.filter?.remove();s.key=i;s.url="none";s.filter=null}else{s={key:i,url:"none",filter:null};this.#Di.set(n,s)}if(!t||!e)return s.url;const r=this.#Li(t);t=Util.makeHexColor(...r);const a=this.#Li(e);e=Util.makeHexColor(...a);this.#Pi.style.color="";if("#000000"===t&&"#ffffff"===e||t===e)return s.url;const o=new Array(256);for(let t=0;t<=255;t++){const e=t/255;o[t]=e<=.03928?e/12.92:((e+.055)/1.055)**2.4}const l=o.join(","),h=`g_${this.#Ci}_hcm_filter`,c=s.filter=this.#Ri(h);this.#Fi(l,l,l,c);this.#Oi(c);const getSteps=(t,e)=>{const i=r[t]/255,n=a[t]/255,s=new Array(e+1);for(let t=0;t<=e;t++)s[t]=i+t/e*(n-i);return s.join(",")};this.#Fi(getSteps(0,5),getSteps(1,5),getSteps(2,5),c);s.url=this.#Ii(h);return s.url}addAlphaFilter(t){let e=this.#x.get(t);if(e)return e;const[i]=this.#ki([t]),n=`alpha_${i}`;e=this.#x.get(n);if(e){this.#x.set(t,e);return e}const s=`g_${this.#Ci}_alpha_map_${this.#y++}`,r=this.#Ii(s);this.#x.set(t,r);this.#x.set(n,r);const a=this.#Ri(s);this.#Ni(i,a);return r}addLuminosityFilter(t){let e,i,n=this.#x.get(t||"luminosity");if(n)return n;if(t){[e]=this.#ki([t]);i=`luminosity_${e}`}else i="luminosity";n=this.#x.get(i);if(n){this.#x.set(t,n);return n}const s=`g_${this.#Ci}_luminosity_map_${this.#y++}`,r=this.#Ii(s);this.#x.set(t,r);this.#x.set(i,r);const a=this.#Ri(s);this.#Bi(a);t&&this.#Ni(e,a);return r}addHighlightHCMFilter(t,e,i,n,s){const r=`${e}-${i}-${n}-${s}`;let a=this.#Di.get(t);if(a?.key===r)return a.url;if(a){a.filter?.remove();a.key=r;a.url="none";a.filter=null}else{a={key:r,url:"none",filter:null};this.#Di.set(t,a)}if(!e||!i)return a.url;const[o,l]=[e,i].map(this.#Li.bind(this));let h=Math.round(.2126*o[0]+.7152*o[1]+.0722*o[2]),c=Math.round(.2126*l[0]+.7152*l[1]+.0722*l[2]),[d,u]=[n,s].map(this.#Li.bind(this));c<h&&([h,c,d,u]=[c,h,u,d]);this.#Pi.style.color="";const getSteps=(t,e,i)=>{const n=new Array(256),s=(c-h)/i,r=t/255,a=(e-t)/(255*i);let o=0;for(let t=0;t<=i;t++){const e=Math.round(h+t*s),i=r+t*a;for(let t=o;t<=e;t++)n[t]=i;o=e+1}for(let t=o;t<256;t++)n[t]=n[o-1];return n.join(",")},p=`g_${this.#Ci}_hcm_${t}_filter`,g=a.filter=this.#Ri(p);this.#Oi(g);this.#Fi(getSteps(d[0],u[0],5),getSteps(d[1],u[1],5),getSteps(d[2],u[2],5),g);a.url=this.#Ii(p);return a.url}destroy(t=!1){if(!t||!this.#Mi?.size){this.#Si?.parentNode.parentNode.remove();this.#Si=null;this.#Ei?.clear();this.#Ei=null;this.#Mi?.clear();this.#Mi=null;this.#y=0}}#Bi(t){const e=this.#Ti.createElementNS(V,"feColorMatrix");e.setAttribute("type","matrix");e.setAttribute("values","0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.3 0.59 0.11 0 0");t.append(e)}#Oi(t){const e=this.#Ti.createElementNS(V,"feColorMatrix");e.setAttribute("type","matrix");e.setAttribute("values","0.2126 0.7152 0.0722 0 0 0.2126 0.7152 0.0722 0 0 0.2126 0.7152 0.0722 0 0 0 0 0 1 0");t.append(e)}#Ri(t){const e=this.#Ti.createElementNS(V,"filter");e.setAttribute("color-interpolation-filters","sRGB");e.setAttribute("id",t);this.#Pi.append(e);return e}#Ui(t,e,i){const n=this.#Ti.createElementNS(V,e);n.setAttribute("type","discrete");n.setAttribute("tableValues",i);t.append(n)}#Fi(t,e,i,n){const s=this.#Ti.createElementNS(V,"feComponentTransfer");n.append(s);this.#Ui(s,"feFuncR",t);this.#Ui(s,"feFuncG",e);this.#Ui(s,"feFuncB",i)}#Ni(t,e){const i=this.#Ti.createElementNS(V,"feComponentTransfer");e.append(i);this.#Ui(i,"feFuncA",t)}#Li(t){this.#Pi.style.color=t;return getRGB(getComputedStyle(this.#Pi).getPropertyValue("color"))}}class BaseStandardFontDataFactory{constructor({baseUrl:t=null}){this.baseUrl=t}async fetch({filename:t}){if(!this.baseUrl)throw new Error("Ensure that the `standardFontDataUrl` API parameter is provided.");if(!t)throw new Error("Font filename must be specified.");const e=`${this.baseUrl}${t}`;return this._fetch(e).catch((t=>{throw new Error(`Unable to load font data at: ${e}`)}))}async _fetch(t){unreachable("Abstract method `_fetch` called.")}}class DOMStandardFontDataFactory extends BaseStandardFontDataFactory{async _fetch(t){const e=await fetchData(t,"arraybuffer");return new Uint8Array(e)}}class BaseWasmFactory{constructor({baseUrl:t=null}){this.baseUrl=t}async fetch({filename:t}){if(!this.baseUrl)throw new Error("Ensure that the `wasmUrl` API parameter is provided.");if(!t)throw new Error("Wasm filename must be specified.");const e=`${this.baseUrl}${t}`;return this._fetch(e).catch((t=>{throw new Error(`Unable to load wasm data at: ${e}`)}))}async _fetch(t){unreachable("Abstract method `_fetch` called.")}}class DOMWasmFactory extends BaseWasmFactory{async _fetch(t){const e=await fetchData(t,"arraybuffer");return new Uint8Array(e)}}if(i){let t;try{const e=process.getBuiltinModule("module").createRequire(import.meta.url);try{t=e("@napi-rs/canvas")}catch(t){warn(`Cannot load "@napi-rs/canvas" package: "${t}".`)}}catch(t){warn(`Cannot access the \`require\` function: "${t}".`)}globalThis.DOMMatrix||(t?.DOMMatrix?globalThis.DOMMatrix=t.DOMMatrix:warn("Cannot polyfill `DOMMatrix`, rendering may be broken."));globalThis.ImageData||(t?.ImageData?globalThis.ImageData=t.ImageData:warn("Cannot polyfill `ImageData`, rendering may be broken."));globalThis.Path2D||(t?.Path2D?globalThis.Path2D=t.Path2D:warn("Cannot polyfill `Path2D`, rendering may be broken."));globalThis.navigator?.language||(globalThis.navigator={language:"en-US",platform:"",userAgent:""})}async function node_utils_fetchData(t){const e=process.getBuiltinModule("fs"),i=await e.promises.readFile(t);return new Uint8Array(i)}class NodeFilterFactory extends BaseFilterFactory{}class NodeCanvasFactory extends BaseCanvasFactory{_createCanvas(t,e){return process.getBuiltinModule("module").createRequire(import.meta.url)("@napi-rs/canvas").createCanvas(t,e)}}class NodeCMapReaderFactory extends BaseCMapReaderFactory{async _fetch(t){return node_utils_fetchData(t)}}class NodeStandardFontDataFactory extends BaseStandardFontDataFactory{async _fetch(t){return node_utils_fetchData(t)}}class NodeWasmFactory extends BaseWasmFactory{async _fetch(t){return node_utils_fetchData(t)}}const ht="Fill",ct="Stroke",dt="Shading";function applyBoundingBox(t,e){if(!e)return;const i=e[2]-e[0],n=e[3]-e[1],s=new Path2D;s.rect(e[0],e[1],i,n);t.clip(s)}class BaseShadingPattern{isModifyingCurrentTransform(){return!1}getPattern(){unreachable("Abstract method `getPattern` called.")}}class RadialAxialShadingPattern extends BaseShadingPattern{constructor(t){super();this._type=t[1];this._bbox=t[2];this._colorStops=t[3];this._p0=t[4];this._p1=t[5];this._r0=t[6];this._r1=t[7];this.matrix=null}_createGradient(t){let e;"axial"===this._type?e=t.createLinearGradient(this._p0[0],this._p0[1],this._p1[0],this._p1[1]):"radial"===this._type&&(e=t.createRadialGradient(this._p0[0],this._p0[1],this._r0,this._p1[0],this._p1[1],this._r1));for(const t of this._colorStops)e.addColorStop(t[0],t[1]);return e}getPattern(t,e,i,n){let s;if(n===ct||n===ht){const r=e.current.getClippedPathBoundingBox(n,getCurrentTransform(t))||[0,0,0,0],a=Math.ceil(r[2]-r[0])||1,o=Math.ceil(r[3]-r[1])||1,l=e.cachedCanvases.getCanvas("pattern",a,o),h=l.context;h.clearRect(0,0,h.canvas.width,h.canvas.height);h.beginPath();h.rect(0,0,h.canvas.width,h.canvas.height);h.translate(-r[0],-r[1]);i=Util.transform(i,[1,0,0,1,r[0],r[1]]);h.transform(...e.baseTransform);this.matrix&&h.transform(...this.matrix);applyBoundingBox(h,this._bbox);h.fillStyle=this._createGradient(h);h.fill();s=t.createPattern(l.canvas,"no-repeat");const c=new DOMMatrix(i);s.setTransform(c)}else{applyBoundingBox(t,this._bbox);s=this._createGradient(t)}return s}}function drawTriangle(t,e,i,n,s,r,a,o){const l=e.coords,h=e.colors,c=t.data,d=4*t.width;let u;if(l[i+1]>l[n+1]){u=i;i=n;n=u;u=r;r=a;a=u}if(l[n+1]>l[s+1]){u=n;n=s;s=u;u=a;a=o;o=u}if(l[i+1]>l[n+1]){u=i;i=n;n=u;u=r;r=a;a=u}const p=(l[i]+e.offsetX)*e.scaleX,g=(l[i+1]+e.offsetY)*e.scaleY,f=(l[n]+e.offsetX)*e.scaleX,m=(l[n+1]+e.offsetY)*e.scaleY,b=(l[s]+e.offsetX)*e.scaleX,v=(l[s+1]+e.offsetY)*e.scaleY;if(g>=v)return;const w=h[r],y=h[r+1],A=h[r+2],x=h[a],_=h[a+1],E=h[a+2],S=h[o],C=h[o+1],T=h[o+2],M=Math.round(g),D=Math.round(v);let P,k,I,R,L,O,N,B;for(let t=M;t<=D;t++){if(t<m){const e=t<g?0:(g-t)/(g-m);P=p-(p-f)*e;k=w-(w-x)*e;I=y-(y-_)*e;R=A-(A-E)*e}else{let e;e=t>v?1:m===v?0:(m-t)/(m-v);P=f-(f-b)*e;k=x-(x-S)*e;I=_-(_-C)*e;R=E-(E-T)*e}let e;e=t<g?0:t>v?1:(g-t)/(g-v);L=p-(p-b)*e;O=w-(w-S)*e;N=y-(y-C)*e;B=A-(A-T)*e;const i=Math.round(Math.min(P,L)),n=Math.round(Math.max(P,L));let s=d*t+4*i;for(let t=i;t<=n;t++){e=(P-t)/(P-L);e<0?e=0:e>1&&(e=1);c[s++]=k-(k-O)*e|0;c[s++]=I-(I-N)*e|0;c[s++]=R-(R-B)*e|0;c[s++]=255}}}function drawFigure(t,e,i){const n=e.coords,s=e.colors;let r,a;switch(e.type){case"lattice":const o=e.verticesPerRow,l=Math.floor(n.length/o)-1,h=o-1;for(r=0;r<l;r++){let e=r*o;for(let r=0;r<h;r++,e++){drawTriangle(t,i,n[e],n[e+1],n[e+o],s[e],s[e+1],s[e+o]);drawTriangle(t,i,n[e+o+1],n[e+1],n[e+o],s[e+o+1],s[e+1],s[e+o])}}break;case"triangles":for(r=0,a=n.length;r<a;r+=3)drawTriangle(t,i,n[r],n[r+1],n[r+2],s[r],s[r+1],s[r+2]);break;default:throw new Error("illegal figure")}}class MeshShadingPattern extends BaseShadingPattern{constructor(t){super();this._coords=t[2];this._colors=t[3];this._figures=t[4];this._bounds=t[5];this._bbox=t[6];this._background=t[7];this.matrix=null}_createMeshCanvas(t,e,i){const n=Math.floor(this._bounds[0]),s=Math.floor(this._bounds[1]),r=Math.ceil(this._bounds[2])-n,a=Math.ceil(this._bounds[3])-s,o=Math.min(Math.ceil(Math.abs(r*t[0]*1.1)),3e3),l=Math.min(Math.ceil(Math.abs(a*t[1]*1.1)),3e3),h=r/o,c=a/l,d={coords:this._coords,colors:this._colors,offsetX:-n,offsetY:-s,scaleX:1/h,scaleY:1/c},u=o+4,p=l+4,g=i.getCanvas("mesh",u,p),f=g.context,m=f.createImageData(o,l);if(e){const t=m.data;for(let i=0,n=t.length;i<n;i+=4){t[i]=e[0];t[i+1]=e[1];t[i+2]=e[2];t[i+3]=255}}for(const t of this._figures)drawFigure(m,t,d);f.putImageData(m,2,2);return{canvas:g.canvas,offsetX:n-2*h,offsetY:s-2*c,scaleX:h,scaleY:c}}isModifyingCurrentTransform(){return!0}getPattern(t,e,i,n){applyBoundingBox(t,this._bbox);const s=new Float32Array(2);if(n===dt)Util.singularValueDecompose2dScale(getCurrentTransform(t),s);else if(this.matrix){Util.singularValueDecompose2dScale(this.matrix,s);const[t,i]=s;Util.singularValueDecompose2dScale(e.baseTransform,s);s[0]*=t;s[1]*=i}else Util.singularValueDecompose2dScale(e.baseTransform,s);const r=this._createMeshCanvas(s,n===dt?null:this._background,e.cachedCanvases);if(n!==dt){t.setTransform(...e.baseTransform);this.matrix&&t.transform(...this.matrix)}t.translate(r.offsetX,r.offsetY);t.scale(r.scaleX,r.scaleY);return t.createPattern(r.canvas,"no-repeat")}}class DummyShadingPattern extends BaseShadingPattern{getPattern(){return"hotpink"}}const ut=1,pt=2;class TilingPattern{static MAX_PATTERN_SIZE=3e3;constructor(t,e,i,n){this.color=t[1];this.operatorList=t[2];this.matrix=t[3];this.bbox=t[4];this.xstep=t[5];this.ystep=t[6];this.paintType=t[7];this.tilingType=t[8];this.ctx=e;this.canvasGraphicsFactory=i;this.baseTransform=n}createPatternCanvas(t){const{bbox:e,operatorList:i,paintType:n,tilingType:s,color:r,canvasGraphicsFactory:a}=this;let{xstep:o,ystep:l}=this;o=Math.abs(o);l=Math.abs(l);info("TilingType: "+s);const h=e[0],c=e[1],d=e[2],u=e[3],p=d-h,g=u-c,f=new Float32Array(2);Util.singularValueDecompose2dScale(this.matrix,f);const[m,b]=f;Util.singularValueDecompose2dScale(this.baseTransform,f);const v=m*f[0],w=b*f[1];let y=p,A=g,x=!1,_=!1;const E=Math.ceil(o*v),S=Math.ceil(l*w);E>=Math.ceil(p*v)?y=o:x=!0;S>=Math.ceil(g*w)?A=l:_=!0;const C=this.getSizeAndScale(y,this.ctx.canvas.width,v),T=this.getSizeAndScale(A,this.ctx.canvas.height,w),M=t.cachedCanvases.getCanvas("pattern",C.size,T.size),D=M.context,P=a.createCanvasGraphics(D);P.groupLevel=t.groupLevel;this.setFillAndStrokeStyleToContext(P,n,r);D.translate(-C.scale*h,-T.scale*c);P.transform(C.scale,0,0,T.scale,0,0);D.save();this.clipBbox(P,h,c,d,u);P.baseTransform=getCurrentTransform(P.ctx);P.executeOperatorList(i);P.endDrawing();D.restore();if(x||_){const e=M.canvas;x&&(y=o);_&&(A=l);const i=this.getSizeAndScale(y,this.ctx.canvas.width,v),n=this.getSizeAndScale(A,this.ctx.canvas.height,w),s=i.size,r=n.size,a=t.cachedCanvases.getCanvas("pattern-workaround",s,r),d=a.context,u=x?Math.floor(p/o):0,f=_?Math.floor(g/l):0;for(let t=0;t<=u;t++)for(let i=0;i<=f;i++)d.drawImage(e,s*t,r*i,s,r,0,0,s,r);return{canvas:a.canvas,scaleX:i.scale,scaleY:n.scale,offsetX:h,offsetY:c}}return{canvas:M.canvas,scaleX:C.scale,scaleY:T.scale,offsetX:h,offsetY:c}}getSizeAndScale(t,e,i){const n=Math.max(TilingPattern.MAX_PATTERN_SIZE,e);let s=Math.ceil(t*i);s>=n?s=n:i=s/t;return{scale:i,size:s}}clipBbox(t,e,i,n,s){const r=n-e,a=s-i;t.ctx.rect(e,i,r,a);Util.axialAlignedBoundingBox([e,i,n,s],getCurrentTransform(t.ctx),t.current.minMax);t.clip();t.endPath()}setFillAndStrokeStyleToContext(t,e,i){const n=t.ctx,s=t.current;switch(e){case ut:const{fillStyle:t,strokeStyle:r}=this.ctx;n.fillStyle=s.fillColor=t;n.strokeStyle=s.strokeColor=r;break;case pt:n.fillStyle=n.strokeStyle=i;s.fillColor=s.strokeColor=i;break;default:throw new FormatError(`Unsupported paint type: ${e}`)}}isModifyingCurrentTransform(){return!1}getPattern(t,e,i,n){let s=i;if(n!==dt){s=Util.transform(s,e.baseTransform);this.matrix&&(s=Util.transform(s,this.matrix))}const r=this.createPatternCanvas(e);let a=new DOMMatrix(s);a=a.translate(r.offsetX,r.offsetY);a=a.scale(1/r.scaleX,1/r.scaleY);const o=t.createPattern(r.canvas,"repeat");o.setTransform(a);return o}}function convertBlackAndWhiteToRGBA({src:t,srcPos:e=0,dest:i,width:n,height:s,nonBlackColor:r=4294967295,inverseDecode:a=!1}){const o=util_FeatureTest.isLittleEndian?4278190080:255,[l,h]=a?[r,o]:[o,r],c=n>>3,d=7&n,u=t.length;i=new Uint32Array(i.buffer);let p=0;for(let n=0;n<s;n++){for(const n=e+c;e<n;e++){const n=e<u?t[e]:255;i[p++]=128&n?h:l;i[p++]=64&n?h:l;i[p++]=32&n?h:l;i[p++]=16&n?h:l;i[p++]=8&n?h:l;i[p++]=4&n?h:l;i[p++]=2&n?h:l;i[p++]=1&n?h:l}if(0===d)continue;const n=e<u?t[e++]:255;for(let t=0;t<d;t++)i[p++]=n&1<<7-t?h:l}return{srcPos:e,destPos:p}}const gt=16,ft=new DOMMatrix,mt=new Float32Array(2),bt=new Float32Array([1/0,1/0,-1/0,-1/0]);class CachedCanvases{constructor(t){this.canvasFactory=t;this.cache=Object.create(null)}getCanvas(t,e,i){let n;if(void 0!==this.cache[t]){n=this.cache[t];this.canvasFactory.reset(n,e,i)}else{n=this.canvasFactory.create(e,i);this.cache[t]=n}return n}delete(t){delete this.cache[t]}clear(){for(const t in this.cache){const e=this.cache[t];this.canvasFactory.destroy(e);delete this.cache[t]}}}function drawImageAtIntegerCoords(t,e,i,n,s,r,a,o,l,h){const[c,d,u,p,g,f]=getCurrentTransform(t);if(0===d&&0===u){const m=a*c+g,b=Math.round(m),v=o*p+f,w=Math.round(v),y=(a+l)*c+g,A=Math.abs(Math.round(y)-b)||1,x=(o+h)*p+f,_=Math.abs(Math.round(x)-w)||1;t.setTransform(Math.sign(c),0,0,Math.sign(p),b,w);t.drawImage(e,i,n,s,r,0,0,A,_);t.setTransform(c,d,u,p,g,f);return[A,_]}if(0===c&&0===p){const m=o*u+g,b=Math.round(m),v=a*d+f,w=Math.round(v),y=(o+h)*u+g,A=Math.abs(Math.round(y)-b)||1,x=(a+l)*d+f,_=Math.abs(Math.round(x)-w)||1;t.setTransform(0,Math.sign(d),Math.sign(u),0,b,w);t.drawImage(e,i,n,s,r,0,0,_,A);t.setTransform(c,d,u,p,g,f);return[_,A]}t.drawImage(e,i,n,s,r,a,o,l,h);return[Math.hypot(c,d)*l,Math.hypot(u,p)*h]}class CanvasExtraState{alphaIsShape=!1;fontSize=0;fontSizeScale=1;textMatrix=null;textMatrixScale=1;fontMatrix=n;leading=0;x=0;y=0;lineX=0;lineY=0;charSpacing=0;wordSpacing=0;textHScale=1;textRenderingMode=v;textRise=0;fillColor="#000000";strokeColor="#000000";patternFill=!1;patternStroke=!1;fillAlpha=1;strokeAlpha=1;lineWidth=1;activeSMask=null;transferMaps="none";constructor(t,e){this.clipBox=new Float32Array([0,0,t,e]);this.minMax=bt.slice()}clone(){const t=Object.create(this);t.clipBox=this.clipBox.slice();t.minMax=this.minMax.slice();return t}getPathBoundingBox(t=ht,e=null){const i=this.minMax.slice();if(t===ct){e||unreachable("Stroke bounding box must include transform.");Util.singularValueDecompose2dScale(e,mt);const t=mt[0]*this.lineWidth/2,n=mt[1]*this.lineWidth/2;i[0]-=t;i[1]-=n;i[2]+=t;i[3]+=n}return i}updateClipFromPath(){const t=Util.intersect(this.clipBox,this.getPathBoundingBox());this.startNewPathAndClipBox(t||[0,0,0,0])}isEmptyClip(){return this.minMax[0]===1/0}startNewPathAndClipBox(t){this.clipBox.set(t,0);this.minMax.set(bt,0)}getClippedPathBoundingBox(t=ht,e=null){return Util.intersect(this.clipBox,this.getPathBoundingBox(t,e))}}function putBinaryImageData(t,e){if(e instanceof ImageData){t.putImageData(e,0,0);return}const i=e.height,n=e.width,s=i%gt,r=(i-s)/gt,a=0===s?r:r+1,o=t.createImageData(n,gt);let l,h=0;const c=e.data,d=o.data;let u,p,g,f;if(e.kind===E.GRAYSCALE_1BPP){const e=c.byteLength,i=new Uint32Array(d.buffer,0,d.byteLength>>2),f=i.length,m=n+7>>3,b=4294967295,v=util_FeatureTest.isLittleEndian?4278190080:255;for(u=0;u<a;u++){g=u<r?gt:s;l=0;for(p=0;p<g;p++){const t=e-h;let s=0;const r=t>m?n:8*t-7,a=-8&r;let o=0,d=0;for(;s<a;s+=8){d=c[h++];i[l++]=128&d?b:v;i[l++]=64&d?b:v;i[l++]=32&d?b:v;i[l++]=16&d?b:v;i[l++]=8&d?b:v;i[l++]=4&d?b:v;i[l++]=2&d?b:v;i[l++]=1&d?b:v}for(;s<r;s++){if(0===o){d=c[h++];o=128}i[l++]=d&o?b:v;o>>=1}}for(;l<f;)i[l++]=0;t.putImageData(o,0,u*gt)}}else if(e.kind===E.RGBA_32BPP){p=0;f=n*gt*4;for(u=0;u<r;u++){d.set(c.subarray(h,h+f));h+=f;t.putImageData(o,0,p);p+=gt}if(u<a){f=n*s*4;d.set(c.subarray(h,h+f));t.putImageData(o,0,p)}}else{if(e.kind!==E.RGB_24BPP)throw new Error(`bad image kind: ${e.kind}`);g=gt;f=n*g;for(u=0;u<a;u++){if(u>=r){g=s;f=n*g}l=0;for(p=f;p--;){d[l++]=c[h++];d[l++]=c[h++];d[l++]=c[h++];d[l++]=255}t.putImageData(o,0,u*gt)}}}function putBinaryImageMask(t,e){if(e.bitmap){t.drawImage(e.bitmap,0,0);return}const i=e.height,n=e.width,s=i%gt,r=(i-s)/gt,a=0===s?r:r+1,o=t.createImageData(n,gt);let l=0;const h=e.data,c=o.data;for(let e=0;e<a;e++){const i=e<r?gt:s;({srcPos:l}=convertBlackAndWhiteToRGBA({src:h,srcPos:l,dest:c,width:n,height:i,nonBlackColor:0}));t.putImageData(o,0,e*gt)}}function copyCtxState(t,e){const i=["strokeStyle","fillStyle","fillRule","globalAlpha","lineWidth","lineCap","lineJoin","miterLimit","globalCompositeOperation","font","filter"];for(const n of i)void 0!==t[n]&&(e[n]=t[n]);if(void 0!==t.setLineDash){e.setLineDash(t.getLineDash());e.lineDashOffset=t.lineDashOffset}}function resetCtxToDefault(t){t.strokeStyle=t.fillStyle="#000000";t.fillRule="nonzero";t.globalAlpha=1;t.lineWidth=1;t.lineCap="butt";t.lineJoin="miter";t.miterLimit=10;t.globalCompositeOperation="source-over";t.font="10px sans-serif";if(void 0!==t.setLineDash){t.setLineDash([]);t.lineDashOffset=0}const{filter:e}=t;"none"!==e&&""!==e&&(t.filter="none")}function getImageSmoothingEnabled(t,e){if(e)return!0;Util.singularValueDecompose2dScale(t,mt);const i=Math.fround(OutputScale.pixelRatio*PixelsPerInch.PDF_TO_CSS_UNITS);return mt[0]<=i&&mt[1]<=i}const vt=["butt","round","square"],wt=["miter","round","bevel"],yt={},At={};class CanvasGraphics{constructor(t,e,i,n,s,{optionalContentConfig:r,markedContentStack:a=null},o,l){this.ctx=t;this.current=new CanvasExtraState(this.ctx.canvas.width,this.ctx.canvas.height);this.stateStack=[];this.pendingClip=null;this.pendingEOFill=!1;this.res=null;this.xobjs=null;this.commonObjs=e;this.objs=i;this.canvasFactory=n;this.filterFactory=s;this.groupStack=[];this.baseTransform=null;this.baseTransformStack=[];this.groupLevel=0;this.smaskStack=[];this.smaskCounter=0;this.tempSMask=null;this.suspendedCtx=null;this.contentVisible=!0;this.markedContentStack=a||[];this.optionalContentConfig=r;this.cachedCanvases=new CachedCanvases(this.canvasFactory);this.cachedPatterns=new Map;this.annotationCanvasMap=o;this.viewportScale=1;this.outputScaleX=1;this.outputScaleY=1;this.pageColors=l;this._cachedScaleForStroking=[-1,0];this._cachedGetSinglePixelWidth=null;this._cachedBitmapsMap=new Map}getObject(t,e=null){return"string"==typeof t?t.startsWith("g_")?this.commonObjs.get(t):this.objs.get(t):e}beginDrawing({transform:t,viewport:e,transparency:i=!1,background:n=null}){const s=this.ctx.canvas.width,r=this.ctx.canvas.height,a=this.ctx.fillStyle;this.ctx.fillStyle=n||"#ffffff";this.ctx.fillRect(0,0,s,r);this.ctx.fillStyle=a;if(i){const t=this.cachedCanvases.getCanvas("transparent",s,r);this.compositeCtx=this.ctx;this.transparentCanvas=t.canvas;this.ctx=t.context;this.ctx.save();this.ctx.transform(...getCurrentTransform(this.compositeCtx))}this.ctx.save();resetCtxToDefault(this.ctx);if(t){this.ctx.transform(...t);this.outputScaleX=t[0];this.outputScaleY=t[0]}this.ctx.transform(...e.transform);this.viewportScale=e.scale;this.baseTransform=getCurrentTransform(this.ctx)}executeOperatorList(t,e,i,n){const s=t.argsArray,r=t.fnArray;let a=e||0;const o=s.length;if(o===a)return a;const l=o-a>10&&"function"==typeof i,h=l?Date.now()+15:0;let c=0;const d=this.commonObjs,u=this.objs;let p;for(;;){if(void 0!==n&&a===n.nextBreakPoint){n.breakIt(a,i);return a}p=r[a];if(p!==I.dependency)this[p].apply(this,s[a]);else for(const t of s[a]){const e=t.startsWith("g_")?d:u;if(!e.has(t)){e.get(t,i);return a}}a++;if(a===o)return a;if(l&&++c>10){if(Date.now()>h){i();return a}c=0}}}#Hi(){for(;this.stateStack.length||this.inSMaskMode;)this.restore();this.current.activeSMask=null;this.ctx.restore();if(this.transparentCanvas){this.ctx=this.compositeCtx;this.ctx.save();this.ctx.setTransform(1,0,0,1,0,0);this.ctx.drawImage(this.transparentCanvas,0,0);this.ctx.restore();this.transparentCanvas=null}}endDrawing(){this.#Hi();this.cachedCanvases.clear();this.cachedPatterns.clear();for(const t of this._cachedBitmapsMap.values()){for(const e of t.values())"undefined"!=typeof HTMLCanvasElement&&e instanceof HTMLCanvasElement&&(e.width=e.height=0);t.clear()}this._cachedBitmapsMap.clear();this.#zi()}#zi(){if(this.pageColors){const t=this.filterFactory.addHCMFilter(this.pageColors.foreground,this.pageColors.background);if("none"!==t){const e=this.ctx.filter;this.ctx.filter=t;this.ctx.drawImage(this.ctx.canvas,0,0);this.ctx.filter=e}}}_scaleImage(t,e){const i=t.width??t.displayWidth,n=t.height??t.displayHeight;let s,r,a=Math.max(Math.hypot(e[0],e[1]),1),o=Math.max(Math.hypot(e[2],e[3]),1),l=i,h=n,c="prescale1";for(;a>2&&l>1||o>2&&h>1;){let e=l,i=h;if(a>2&&l>1){e=l>=16384?Math.floor(l/2)-1||1:Math.ceil(l/2);a/=l/e}if(o>2&&h>1){i=h>=16384?Math.floor(h/2)-1||1:Math.ceil(h)/2;o/=h/i}s=this.cachedCanvases.getCanvas(c,e,i);r=s.context;r.clearRect(0,0,e,i);r.drawImage(t,0,0,l,h,0,0,e,i);t=s.canvas;l=e;h=i;c="prescale1"===c?"prescale2":"prescale1"}return{img:t,paintWidth:l,paintHeight:h}}_createMaskCanvas(t){const e=this.ctx,{width:i,height:n}=t,s=this.current.fillColor,r=this.current.patternFill,a=getCurrentTransform(e);let o,l,h,c;if((t.bitmap||t.data)&&t.count>1){const e=t.bitmap||t.data.buffer;l=JSON.stringify(r?a:[a.slice(0,4),s]);o=this._cachedBitmapsMap.get(e);if(!o){o=new Map;this._cachedBitmapsMap.set(e,o)}const i=o.get(l);if(i&&!r){return{canvas:i,offsetX:Math.round(Math.min(a[0],a[2])+a[4]),offsetY:Math.round(Math.min(a[1],a[3])+a[5])}}h=i}if(!h){c=this.cachedCanvases.getCanvas("maskCanvas",i,n);putBinaryImageMask(c.context,t)}let d=Util.transform(a,[1/i,0,0,-1/n,0,0]);d=Util.transform(d,[1,0,0,1,0,-n]);const u=bt.slice();Util.axialAlignedBoundingBox([0,0,i,n],d,u);const[p,g,f,m]=u,b=Math.round(f-p)||1,v=Math.round(m-g)||1,w=this.cachedCanvases.getCanvas("fillCanvas",b,v),y=w.context,A=p,x=g;y.translate(-A,-x);y.transform(...d);if(!h){h=this._scaleImage(c.canvas,getCurrentTransformInverse(y));h=h.img;o&&r&&o.set(l,h)}y.imageSmoothingEnabled=getImageSmoothingEnabled(getCurrentTransform(y),t.interpolate);drawImageAtIntegerCoords(y,h,0,0,h.width,h.height,0,0,i,n);y.globalCompositeOperation="source-in";const _=Util.transform(getCurrentTransformInverse(y),[1,0,0,1,-A,-x]);y.fillStyle=r?s.getPattern(e,this,_,ht):s;y.fillRect(0,0,i,n);if(o&&!r){this.cachedCanvases.delete("fillCanvas");o.set(l,w.canvas)}return{canvas:w.canvas,offsetX:Math.round(A),offsetY:Math.round(x)}}setLineWidth(t){t!==this.current.lineWidth&&(this._cachedScaleForStroking[0]=-1);this.current.lineWidth=t;this.ctx.lineWidth=t}setLineCap(t){this.ctx.lineCap=vt[t]}setLineJoin(t){this.ctx.lineJoin=wt[t]}setMiterLimit(t){this.ctx.miterLimit=t}setDash(t,e){const i=this.ctx;if(void 0!==i.setLineDash){i.setLineDash(t);i.lineDashOffset=e}}setRenderingIntent(t){}setFlatness(t){}setGState(t){for(const[e,i]of t)switch(e){case"LW":this.setLineWidth(i);break;case"LC":this.setLineCap(i);break;case"LJ":this.setLineJoin(i);break;case"ML":this.setMiterLimit(i);break;case"D":this.setDash(i[0],i[1]);break;case"RI":this.setRenderingIntent(i);break;case"FL":this.setFlatness(i);break;case"Font":this.setFont(i[0],i[1]);break;case"CA":this.current.strokeAlpha=i;break;case"ca":this.ctx.globalAlpha=this.current.fillAlpha=i;break;case"BM":this.ctx.globalCompositeOperation=i;break;case"SMask":this.current.activeSMask=i?this.tempSMask:null;this.tempSMask=null;this.checkSMaskState();break;case"TR":this.ctx.filter=this.current.transferMaps=this.filterFactory.addFilter(i)}}get inSMaskMode(){return!!this.suspendedCtx}checkSMaskState(){const t=this.inSMaskMode;this.current.activeSMask&&!t?this.beginSMaskMode():!this.current.activeSMask&&t&&this.endSMaskMode()}beginSMaskMode(){if(this.inSMaskMode)throw new Error("beginSMaskMode called while already in smask mode");const t=this.ctx.canvas.width,e=this.ctx.canvas.height,i="smaskGroupAt"+this.groupLevel,n=this.cachedCanvases.getCanvas(i,t,e);this.suspendedCtx=this.ctx;const s=this.ctx=n.context;s.setTransform(this.suspendedCtx.getTransform());copyCtxState(this.suspendedCtx,s);!function mirrorContextOperations(t,e){if(t._removeMirroring)throw new Error("Context is already forwarding operations.");t.__originalSave=t.save;t.__originalRestore=t.restore;t.__originalRotate=t.rotate;t.__originalScale=t.scale;t.__originalTranslate=t.translate;t.__originalTransform=t.transform;t.__originalSetTransform=t.setTransform;t.__originalResetTransform=t.resetTransform;t.__originalClip=t.clip;t.__originalMoveTo=t.moveTo;t.__originalLineTo=t.lineTo;t.__originalBezierCurveTo=t.bezierCurveTo;t.__originalRect=t.rect;t.__originalClosePath=t.closePath;t.__originalBeginPath=t.beginPath;t._removeMirroring=()=>{t.save=t.__originalSave;t.restore=t.__originalRestore;t.rotate=t.__originalRotate;t.scale=t.__originalScale;t.translate=t.__originalTranslate;t.transform=t.__originalTransform;t.setTransform=t.__originalSetTransform;t.resetTransform=t.__originalResetTransform;t.clip=t.__originalClip;t.moveTo=t.__originalMoveTo;t.lineTo=t.__originalLineTo;t.bezierCurveTo=t.__originalBezierCurveTo;t.rect=t.__originalRect;t.closePath=t.__originalClosePath;t.beginPath=t.__originalBeginPath;delete t._removeMirroring};t.save=function(){e.save();this.__originalSave()};t.restore=function(){e.restore();this.__originalRestore()};t.translate=function(t,i){e.translate(t,i);this.__originalTranslate(t,i)};t.scale=function(t,i){e.scale(t,i);this.__originalScale(t,i)};t.transform=function(t,i,n,s,r,a){e.transform(t,i,n,s,r,a);this.__originalTransform(t,i,n,s,r,a)};t.setTransform=function(t,i,n,s,r,a){e.setTransform(t,i,n,s,r,a);this.__originalSetTransform(t,i,n,s,r,a)};t.resetTransform=function(){e.resetTransform();this.__originalResetTransform()};t.rotate=function(t){e.rotate(t);this.__originalRotate(t)};t.clip=function(t){e.clip(t);this.__originalClip(t)};t.moveTo=function(t,i){e.moveTo(t,i);this.__originalMoveTo(t,i)};t.lineTo=function(t,i){e.lineTo(t,i);this.__originalLineTo(t,i)};t.bezierCurveTo=function(t,i,n,s,r,a){e.bezierCurveTo(t,i,n,s,r,a);this.__originalBezierCurveTo(t,i,n,s,r,a)};t.rect=function(t,i,n,s){e.rect(t,i,n,s);this.__originalRect(t,i,n,s)};t.closePath=function(){e.closePath();this.__originalClosePath()};t.beginPath=function(){e.beginPath();this.__originalBeginPath()}}(s,this.suspendedCtx);this.setGState([["BM","source-over"]])}endSMaskMode(){if(!this.inSMaskMode)throw new Error("endSMaskMode called while not in smask mode");this.ctx._removeMirroring();copyCtxState(this.ctx,this.suspendedCtx);this.ctx=this.suspendedCtx;this.suspendedCtx=null}compose(t){if(!this.current.activeSMask)return;if(t){t[0]=Math.floor(t[0]);t[1]=Math.floor(t[1]);t[2]=Math.ceil(t[2]);t[3]=Math.ceil(t[3])}else t=[0,0,this.ctx.canvas.width,this.ctx.canvas.height];const e=this.current.activeSMask,i=this.suspendedCtx;this.composeSMask(i,e,this.ctx,t);this.ctx.save();this.ctx.setTransform(1,0,0,1,0,0);this.ctx.clearRect(0,0,this.ctx.canvas.width,this.ctx.canvas.height);this.ctx.restore()}composeSMask(t,e,i,n){const s=n[0],r=n[1],a=n[2]-s,o=n[3]-r;if(0!==a&&0!==o){this.genericComposeSMask(e.context,i,a,o,e.subtype,e.backdrop,e.transferMap,s,r,e.offsetX,e.offsetY);t.save();t.globalAlpha=1;t.globalCompositeOperation="source-over";t.setTransform(1,0,0,1,0,0);t.drawImage(i.canvas,0,0);t.restore()}}genericComposeSMask(t,e,i,n,s,r,a,o,l,h,c){let d=t.canvas,u=o-h,p=l-c;if(r)if(u<0||p<0||u+i>d.width||p+n>d.height){const t=this.cachedCanvases.getCanvas("maskExtension",i,n),e=t.context;e.drawImage(d,-u,-p);e.globalCompositeOperation="destination-atop";e.fillStyle=r;e.fillRect(0,0,i,n);e.globalCompositeOperation="source-over";d=t.canvas;u=p=0}else{t.save();t.globalAlpha=1;t.setTransform(1,0,0,1,0,0);const e=new Path2D;e.rect(u,p,i,n);t.clip(e);t.globalCompositeOperation="destination-atop";t.fillStyle=r;t.fillRect(u,p,i,n);t.restore()}e.save();e.globalAlpha=1;e.setTransform(1,0,0,1,0,0);"Alpha"===s&&a?e.filter=this.filterFactory.addAlphaFilter(a):"Luminosity"===s&&(e.filter=this.filterFactory.addLuminosityFilter(a));const g=new Path2D;g.rect(o,l,i,n);e.clip(g);e.globalCompositeOperation="destination-in";e.drawImage(d,u,p,i,n,o,l,i,n);e.restore()}save(){this.inSMaskMode&&copyCtxState(this.ctx,this.suspendedCtx);this.ctx.save();const t=this.current;this.stateStack.push(t);this.current=t.clone()}restore(){if(0!==this.stateStack.length){this.current=this.stateStack.pop();this.ctx.restore();this.inSMaskMode&&copyCtxState(this.suspendedCtx,this.ctx);this.checkSMaskState();this.pendingClip=null;this._cachedScaleForStroking[0]=-1;this._cachedGetSinglePixelWidth=null}else this.inSMaskMode&&this.endSMaskMode()}transform(t,e,i,n,s,r){this.ctx.transform(t,e,i,n,s,r);this._cachedScaleForStroking[0]=-1;this._cachedGetSinglePixelWidth=null}constructPath(t,e,i){let[n]=e;if(i){if(!(n instanceof Path2D)){const t=e[0]=new Path2D;for(let e=0,i=n.length;e<i;)switch(n[e++]){case R:t.moveTo(n[e++],n[e++]);break;case L:t.lineTo(n[e++],n[e++]);break;case O:t.bezierCurveTo(n[e++],n[e++],n[e++],n[e++],n[e++],n[e++]);break;case N:t.closePath();break;default:warn(`Unrecognized drawing path operator: ${n[e-1]}`)}n=t}Util.axialAlignedBoundingBox(i,getCurrentTransform(this.ctx),this.current.minMax);this[t](n)}else{n||=e[0]=new Path2D;this[t](n)}}closePath(){this.ctx.closePath()}stroke(t,e=!0){const i=this.ctx,n=this.current.strokeColor;i.globalAlpha=this.current.strokeAlpha;if(this.contentVisible)if("object"==typeof n&&n?.getPattern){const e=n.isModifyingCurrentTransform()?i.getTransform():null;i.save();i.strokeStyle=n.getPattern(i,this,getCurrentTransformInverse(i),ct);if(e){const n=new Path2D;n.addPath(t,i.getTransform().invertSelf().multiplySelf(e));t=n}this.rescaleAndStroke(t,!1);i.restore()}else this.rescaleAndStroke(t,!0);e&&this.consumePath(t,this.current.getClippedPathBoundingBox(ct,getCurrentTransform(this.ctx)));i.globalAlpha=this.current.fillAlpha}closeStroke(t){this.stroke(t)}fill(t,e=!0){const i=this.ctx,n=this.current.fillColor;let s=!1;if(this.current.patternFill){const e=n.isModifyingCurrentTransform()?i.getTransform():null;i.save();i.fillStyle=n.getPattern(i,this,getCurrentTransformInverse(i),ht);if(e){const n=new Path2D;n.addPath(t,i.getTransform().invertSelf().multiplySelf(e));t=n}s=!0}const r=this.current.getClippedPathBoundingBox();if(this.contentVisible&&null!==r)if(this.pendingEOFill){i.fill(t,"evenodd");this.pendingEOFill=!1}else i.fill(t);s&&i.restore();e&&this.consumePath(t,r)}eoFill(t){this.pendingEOFill=!0;this.fill(t)}fillStroke(t){this.fill(t,!1);this.stroke(t,!1);this.consumePath(t)}eoFillStroke(t){this.pendingEOFill=!0;this.fillStroke(t)}closeFillStroke(t){this.fillStroke(t)}closeEOFillStroke(t){this.pendingEOFill=!0;this.fillStroke(t)}endPath(t){this.consumePath(t)}rawFillPath(t){this.ctx.fill(t)}clip(){this.pendingClip=yt}eoClip(){this.pendingClip=At}beginText(){this.current.textMatrix=null;this.current.textMatrixScale=1;this.current.x=this.current.lineX=0;this.current.y=this.current.lineY=0}endText(){const t=this.pendingTextPaths,e=this.ctx;if(void 0===t)return;const i=new Path2D,n=e.getTransform().invertSelf();for(const{transform:e,x:s,y:r,fontSize:a,path:o}of t)o&&i.addPath(o,new DOMMatrix(e).preMultiplySelf(n).translate(s,r).scale(a,-a));e.clip(i);delete this.pendingTextPaths}setCharSpacing(t){this.current.charSpacing=t}setWordSpacing(t){this.current.wordSpacing=t}setHScale(t){this.current.textHScale=t/100}setLeading(t){this.current.leading=-t}setFont(t,e){const i=this.commonObjs.get(t),s=this.current;if(!i)throw new Error(`Can't find font for ${t}`);s.fontMatrix=i.fontMatrix||n;0!==s.fontMatrix[0]&&0!==s.fontMatrix[3]||warn("Invalid font matrix for font "+t);if(e<0){e=-e;s.fontDirection=-1}else s.fontDirection=1;this.current.font=i;this.current.fontSize=e;if(i.isType3Font)return;const r=i.loadedName||"sans-serif",a=i.systemFontInfo?.css||`"${r}", ${i.fallbackName}`;let o="normal";i.black?o="900":i.bold&&(o="bold");const l=i.italic?"italic":"normal";let h=e;e<16?h=16:e>100&&(h=100);this.current.fontSizeScale=e/h;this.ctx.font=`${l} ${o} ${h}px ${a}`}setTextRenderingMode(t){this.current.textRenderingMode=t}setTextRise(t){this.current.textRise=t}moveText(t,e){this.current.x=this.current.lineX+=t;this.current.y=this.current.lineY+=e}setLeadingMoveText(t,e){this.setLeading(-e);this.moveText(t,e)}setTextMatrix(t){const{current:e}=this;e.textMatrix=t;e.textMatrixScale=Math.hypot(t[0],t[1]);e.x=e.lineX=0;e.y=e.lineY=0}nextLine(){this.moveText(0,this.current.leading)}#ji(t,e,i){const n=new Path2D;n.addPath(t,new DOMMatrix(i).invertSelf().multiplySelf(e));return n}paintChar(t,e,i,n,s){const r=this.ctx,a=this.current,o=a.font,l=a.textRenderingMode,h=a.fontSize/a.fontSizeScale,c=l&x,d=!!(l&_),u=a.patternFill&&!o.missingFile,p=a.patternStroke&&!o.missingFile;let g;(o.disableFontFace||d||u||p)&&!o.missingFile&&(g=o.getPathGenerator(this.commonObjs,t));if(g&&(o.disableFontFace||u||p)){r.save();r.translate(e,i);r.scale(h,-h);let t;if(c===v||c===y)if(n){t=r.getTransform();r.setTransform(...n);r.fill(this.#ji(g,t,n))}else r.fill(g);if(c===w||c===y)if(s){t||=r.getTransform();r.setTransform(...s);const{a:e,b:i,c:n,d:a}=t,o=Util.inverseTransform(s),l=Util.transform([e,i,n,a,0,0],o);Util.singularValueDecompose2dScale(l,mt);r.lineWidth*=Math.max(mt[0],mt[1])/h;r.stroke(this.#ji(g,t,s))}else{r.lineWidth/=h;r.stroke(g)}r.restore()}else{c!==v&&c!==y||r.fillText(t,e,i);c!==w&&c!==y||r.strokeText(t,e,i)}if(d){(this.pendingTextPaths||=[]).push({transform:getCurrentTransform(r),x:e,y:i,fontSize:h,path:g})}}get isFontSubpixelAAEnabled(){const{context:t}=this.cachedCanvases.getCanvas("isFontSubpixelAAEnabled",10,10);t.scale(1.5,1);t.fillText("I",0,10);const e=t.getImageData(0,0,10,10).data;let i=!1;for(let t=3;t<e.length;t+=4)if(e[t]>0&&e[t]<255){i=!0;break}return shadow(this,"isFontSubpixelAAEnabled",i)}showText(t){const e=this.current,i=e.font;if(i.isType3Font)return this.showType3Text(t);const n=e.fontSize;if(0===n)return;const s=this.ctx,r=e.fontSizeScale,a=e.charSpacing,o=e.wordSpacing,l=e.fontDirection,h=e.textHScale*l,c=t.length,d=i.vertical,u=d?1:-1,p=i.defaultVMetrics,g=n*e.fontMatrix[0],f=e.textRenderingMode===v&&!i.disableFontFace&&!e.patternFill;s.save();e.textMatrix&&s.transform(...e.textMatrix);s.translate(e.x,e.y+e.textRise);l>0?s.scale(h,-1):s.scale(h,1);let m,b;if(e.patternFill){s.save();const t=e.fillColor.getPattern(s,this,getCurrentTransformInverse(s),ht);m=getCurrentTransform(s);s.restore();s.fillStyle=t}if(e.patternStroke){s.save();const t=e.strokeColor.getPattern(s,this,getCurrentTransformInverse(s),ct);b=getCurrentTransform(s);s.restore();s.strokeStyle=t}let A=e.lineWidth;const _=e.textMatrixScale;if(0===_||0===A){const t=e.textRenderingMode&x;t!==w&&t!==y||(A=this.getSinglePixelWidth())}else A/=_;if(1!==r){s.scale(r,r);A/=r}s.lineWidth=A;if(i.isInvalidPDFjsFont){const i=[];let n=0;for(const e of t){i.push(e.unicode);n+=e.width}s.fillText(i.join(""),0,0);e.x+=n*g*h;s.restore();this.compose();return}let E,S=0;for(E=0;E<c;++E){const e=t[E];if("number"==typeof e){S+=u*e*n/1e3;continue}let h=!1;const c=(e.isSpace?o:0)+a,v=e.fontChar,w=e.accent;let y,A,x=e.width;if(d){const t=e.vmetric||p,i=-(e.vmetric?t[1]:.5*x)*g,n=t[2]*g;x=t?-t[0]:x;y=i/r;A=(S+n)/r}else{y=S/r;A=0}if(i.remeasure&&x>0){const t=1e3*s.measureText(v).width/n*r;if(x<t&&this.isFontSubpixelAAEnabled){const e=x/t;h=!0;s.save();s.scale(e,1);y/=e}else x!==t&&(y+=(x-t)/2e3*n/r)}if(this.contentVisible&&(e.isInFont||i.missingFile))if(f&&!w)s.fillText(v,y,A);else{this.paintChar(v,y,A,m,b);if(w){const t=y+n*w.offset.x/r,e=A-n*w.offset.y/r;this.paintChar(w.fontChar,t,e,m,b)}}S+=d?x*g-c*l:x*g+c*l;h&&s.restore()}d?e.y-=S:e.x+=S*h;s.restore();this.compose()}showType3Text(t){const e=this.ctx,i=this.current,s=i.font,r=i.fontSize,a=i.fontDirection,o=s.vertical?1:-1,l=i.charSpacing,h=i.wordSpacing,c=i.textHScale*a,d=i.fontMatrix||n,u=t.length;let p,g,f,m;if(!(i.textRenderingMode===A)&&0!==r){this._cachedScaleForStroking[0]=-1;this._cachedGetSinglePixelWidth=null;e.save();i.textMatrix&&e.transform(...i.textMatrix);e.translate(i.x,i.y+i.textRise);e.scale(c,a);for(p=0;p<u;++p){g=t[p];if("number"==typeof g){m=o*g*r/1e3;this.ctx.translate(m,0);i.x+=m*c;continue}const n=(g.isSpace?h:0)+l,a=s.charProcOperatorList[g.operatorListId];if(a){if(this.contentVisible){this.save();e.scale(r,r);e.transform(...d);this.executeOperatorList(a);this.restore()}}else warn(`Type3 character "${g.operatorListId}" is not available.`);const u=[g.width,0];Util.applyTransform(u,d);f=u[0]*r+n;e.translate(f,0);i.x+=f*c}e.restore()}}setCharWidth(t,e){}setCharWidthAndBounds(t,e,i,n,s,r){const a=new Path2D;a.rect(i,n,s-i,r-n);this.ctx.clip(a);this.endPath()}getColorN_Pattern(t){let e;if("TilingPattern"===t[0]){const i=this.baseTransform||getCurrentTransform(this.ctx),n={createCanvasGraphics:t=>new CanvasGraphics(t,this.commonObjs,this.objs,this.canvasFactory,this.filterFactory,{optionalContentConfig:this.optionalContentConfig,markedContentStack:this.markedContentStack})};e=new TilingPattern(t,this.ctx,n,i)}else e=this._getPattern(t[1],t[2]);return e}setStrokeColorN(){this.current.strokeColor=this.getColorN_Pattern(arguments);this.current.patternStroke=!0}setFillColorN(){this.current.fillColor=this.getColorN_Pattern(arguments);this.current.patternFill=!0}setStrokeRGBColor(t){this.ctx.strokeStyle=this.current.strokeColor=t;this.current.patternStroke=!1}setStrokeTransparent(){this.ctx.strokeStyle=this.current.strokeColor="transparent";this.current.patternStroke=!1}setFillRGBColor(t){this.ctx.fillStyle=this.current.fillColor=t;this.current.patternFill=!1}setFillTransparent(){this.ctx.fillStyle=this.current.fillColor="transparent";this.current.patternFill=!1}_getPattern(t,e=null){let i;if(this.cachedPatterns.has(t))i=this.cachedPatterns.get(t);else{i=function getShadingPattern(t){switch(t[0]){case"RadialAxial":return new RadialAxialShadingPattern(t);case"Mesh":return new MeshShadingPattern(t);case"Dummy":return new DummyShadingPattern}throw new Error(`Unknown IR type: ${t[0]}`)}(this.getObject(t));this.cachedPatterns.set(t,i)}e&&(i.matrix=e);return i}shadingFill(t){if(!this.contentVisible)return;const e=this.ctx;this.save();const i=this._getPattern(t);e.fillStyle=i.getPattern(e,this,getCurrentTransformInverse(e),dt);const n=getCurrentTransformInverse(e);if(n){const{width:t,height:i}=e.canvas,s=bt.slice();Util.axialAlignedBoundingBox([0,0,t,i],n,s);const[r,a,o,l]=s;this.ctx.fillRect(r,a,o-r,l-a)}else this.ctx.fillRect(-1e10,-1e10,2e10,2e10);this.compose(this.current.getClippedPathBoundingBox());this.restore()}beginInlineImage(){unreachable("Should not call beginInlineImage")}beginImageData(){unreachable("Should not call beginImageData")}paintFormXObjectBegin(t,e){if(this.contentVisible){this.save();this.baseTransformStack.push(this.baseTransform);t&&this.transform(...t);this.baseTransform=getCurrentTransform(this.ctx);if(e){Util.axialAlignedBoundingBox(e,this.baseTransform,this.current.minMax);const[t,i,n,s]=e,r=new Path2D;r.rect(t,i,n-t,s-i);this.ctx.clip(r);this.endPath()}}}paintFormXObjectEnd(){if(this.contentVisible){this.restore();this.baseTransform=this.baseTransformStack.pop()}}beginGroup(t){if(!this.contentVisible)return;this.save();if(this.inSMaskMode){this.endSMaskMode();this.current.activeSMask=null}const e=this.ctx;t.isolated||info("TODO: Support non-isolated groups.");t.knockout&&warn("Knockout groups not supported.");const i=getCurrentTransform(e);t.matrix&&e.transform(...t.matrix);if(!t.bbox)throw new Error("Bounding box is required.");let n=bt.slice();Util.axialAlignedBoundingBox(t.bbox,getCurrentTransform(e),n);const s=[0,0,e.canvas.width,e.canvas.height];n=Util.intersect(n,s)||[0,0,0,0];const r=Math.floor(n[0]),a=Math.floor(n[1]),o=Math.max(Math.ceil(n[2])-r,1),l=Math.max(Math.ceil(n[3])-a,1);this.current.startNewPathAndClipBox([0,0,o,l]);let h="groupAt"+this.groupLevel;t.smask&&(h+="_smask_"+this.smaskCounter++%2);const c=this.cachedCanvases.getCanvas(h,o,l),d=c.context;d.translate(-r,-a);d.transform(...i);let u=new Path2D;const[p,g,f,m]=t.bbox;u.rect(p,g,f-p,m-g);if(t.matrix){const e=new Path2D;e.addPath(u,new DOMMatrix(t.matrix));u=e}d.clip(u);if(t.smask)this.smaskStack.push({canvas:c.canvas,context:d,offsetX:r,offsetY:a,subtype:t.smask.subtype,backdrop:t.smask.backdrop,transferMap:t.smask.transferMap||null,startTransformInverse:null});else{e.setTransform(1,0,0,1,0,0);e.translate(r,a);e.save()}copyCtxState(e,d);this.ctx=d;this.setGState([["BM","source-over"],["ca",1],["CA",1]]);this.groupStack.push(e);this.groupLevel++}endGroup(t){if(!this.contentVisible)return;this.groupLevel--;const e=this.ctx,i=this.groupStack.pop();this.ctx=i;this.ctx.imageSmoothingEnabled=!1;if(t.smask){this.tempSMask=this.smaskStack.pop();this.restore()}else{this.ctx.restore();const t=getCurrentTransform(this.ctx);this.restore();this.ctx.save();this.ctx.setTransform(...t);const i=bt.slice();Util.axialAlignedBoundingBox([0,0,e.canvas.width,e.canvas.height],t,i);this.ctx.drawImage(e.canvas,0,0);this.ctx.restore();this.compose(i)}}beginAnnotation(t,e,i,n,s){this.#Hi();resetCtxToDefault(this.ctx);this.ctx.save();this.save();this.baseTransform&&this.ctx.setTransform(...this.baseTransform);if(e){const n=e[2]-e[0],r=e[3]-e[1];if(s&&this.annotationCanvasMap){(i=i.slice())[4]-=e[0];i[5]-=e[1];(e=e.slice())[0]=e[1]=0;e[2]=n;e[3]=r;Util.singularValueDecompose2dScale(getCurrentTransform(this.ctx),mt);const{viewportScale:s}=this,a=Math.ceil(n*this.outputScaleX*s),o=Math.ceil(r*this.outputScaleY*s);this.annotationCanvas=this.canvasFactory.create(a,o);const{canvas:l,context:h}=this.annotationCanvas;this.annotationCanvasMap.set(t,l);this.annotationCanvas.savedCtx=this.ctx;this.ctx=h;this.ctx.save();this.ctx.setTransform(mt[0],0,0,-mt[1],0,r*mt[1]);resetCtxToDefault(this.ctx)}else{resetCtxToDefault(this.ctx);this.endPath();const t=new Path2D;t.rect(e[0],e[1],n,r);this.ctx.clip(t)}}this.current=new CanvasExtraState(this.ctx.canvas.width,this.ctx.canvas.height);this.transform(...i);this.transform(...n)}endAnnotation(){if(this.annotationCanvas){this.ctx.restore();this.#zi();this.ctx=this.annotationCanvas.savedCtx;delete this.annotationCanvas.savedCtx;delete this.annotationCanvas}}paintImageMaskXObject(t){if(!this.contentVisible)return;const e=t.count;(t=this.getObject(t.data,t)).count=e;const i=this.ctx,n=this._createMaskCanvas(t),s=n.canvas;i.save();i.setTransform(1,0,0,1,0,0);i.drawImage(s,n.offsetX,n.offsetY);i.restore();this.compose()}paintImageMaskXObjectRepeat(t,e,i=0,n=0,s,r){if(!this.contentVisible)return;t=this.getObject(t.data,t);const a=this.ctx;a.save();const o=getCurrentTransform(a);a.transform(e,i,n,s,0,0);const l=this._createMaskCanvas(t);a.setTransform(1,0,0,1,l.offsetX-o[4],l.offsetY-o[5]);for(let t=0,h=r.length;t<h;t+=2){const h=Util.transform(o,[e,i,n,s,r[t],r[t+1]]);a.drawImage(l.canvas,h[4],h[5])}a.restore();this.compose()}paintImageMaskXObjectGroup(t){if(!this.contentVisible)return;const e=this.ctx,i=this.current.fillColor,n=this.current.patternFill;for(const s of t){const{data:t,width:r,height:a,transform:o}=s,l=this.cachedCanvases.getCanvas("maskCanvas",r,a),h=l.context;h.save();putBinaryImageMask(h,this.getObject(t,s));h.globalCompositeOperation="source-in";h.fillStyle=n?i.getPattern(h,this,getCurrentTransformInverse(e),ht):i;h.fillRect(0,0,r,a);h.restore();e.save();e.transform(...o);e.scale(1,-1);drawImageAtIntegerCoords(e,l.canvas,0,0,r,a,0,-1,1,1);e.restore()}this.compose()}paintImageXObject(t){if(!this.contentVisible)return;const e=this.getObject(t);e?this.paintInlineImageXObject(e):warn("Dependent image isn't ready yet")}paintImageXObjectRepeat(t,e,i,n){if(!this.contentVisible)return;const s=this.getObject(t);if(!s){warn("Dependent image isn't ready yet");return}const r=s.width,a=s.height,o=[];for(let t=0,s=n.length;t<s;t+=2)o.push({transform:[e,0,0,i,n[t],n[t+1]],x:0,y:0,w:r,h:a});this.paintInlineImageXObjectGroup(s,o)}applyTransferMapsToCanvas(t){if("none"!==this.current.transferMaps){t.filter=this.current.transferMaps;t.drawImage(t.canvas,0,0);t.filter="none"}return t.canvas}applyTransferMapsToBitmap(t){if("none"===this.current.transferMaps)return t.bitmap;const{bitmap:e,width:i,height:n}=t,s=this.cachedCanvases.getCanvas("inlineImage",i,n),r=s.context;r.filter=this.current.transferMaps;r.drawImage(e,0,0);r.filter="none";return s.canvas}paintInlineImageXObject(t){if(!this.contentVisible)return;const e=t.width,i=t.height,n=this.ctx;this.save();const{filter:s}=n;"none"!==s&&""!==s&&(n.filter="none");n.scale(1/e,-1/i);let r;if(t.bitmap)r=this.applyTransferMapsToBitmap(t);else if("function"==typeof HTMLElement&&t instanceof HTMLElement||!t.data)r=t;else{const n=this.cachedCanvases.getCanvas("inlineImage",e,i).context;putBinaryImageData(n,t);r=this.applyTransferMapsToCanvas(n)}const a=this._scaleImage(r,getCurrentTransformInverse(n));n.imageSmoothingEnabled=getImageSmoothingEnabled(getCurrentTransform(n),t.interpolate);drawImageAtIntegerCoords(n,a.img,0,0,a.paintWidth,a.paintHeight,0,-i,e,i);this.compose();this.restore()}paintInlineImageXObjectGroup(t,e){if(!this.contentVisible)return;const i=this.ctx;let n;if(t.bitmap)n=t.bitmap;else{const e=t.width,i=t.height,s=this.cachedCanvases.getCanvas("inlineImage",e,i).context;putBinaryImageData(s,t);n=this.applyTransferMapsToCanvas(s)}for(const t of e){i.save();i.transform(...t.transform);i.scale(1,-1);drawImageAtIntegerCoords(i,n,t.x,t.y,t.w,t.h,0,-1,1,1);i.restore()}this.compose()}paintSolidColorImageMask(){if(this.contentVisible){this.ctx.fillRect(0,0,1,1);this.compose()}}markPoint(t){}markPointProps(t,e){}beginMarkedContent(t){this.markedContentStack.push({visible:!0})}beginMarkedContentProps(t,e){"OC"===t?this.markedContentStack.push({visible:this.optionalContentConfig.isVisible(e)}):this.markedContentStack.push({visible:!0});this.contentVisible=this.isContentVisible()}endMarkedContent(){this.markedContentStack.pop();this.contentVisible=this.isContentVisible()}beginCompat(){}endCompat(){}consumePath(t,e){const i=this.current.isEmptyClip();this.pendingClip&&this.current.updateClipFromPath();this.pendingClip||this.compose(e);const n=this.ctx;if(this.pendingClip){i||(this.pendingClip===At?n.clip(t,"evenodd"):n.clip(t));this.pendingClip=null}this.current.startNewPathAndClipBox(this.current.clipBox)}getSinglePixelWidth(){if(!this._cachedGetSinglePixelWidth){const t=getCurrentTransform(this.ctx);if(0===t[1]&&0===t[2])this._cachedGetSinglePixelWidth=1/Math.min(Math.abs(t[0]),Math.abs(t[3]));else{const e=Math.abs(t[0]*t[3]-t[2]*t[1]),i=Math.hypot(t[0],t[2]),n=Math.hypot(t[1],t[3]);this._cachedGetSinglePixelWidth=Math.max(i,n)/e}}return this._cachedGetSinglePixelWidth}getScaleForStroking(){if(-1===this._cachedScaleForStroking[0]){const{lineWidth:t}=this.current,{a:e,b:i,c:n,d:s}=this.ctx.getTransform();let r,a;if(0===i&&0===n){const i=Math.abs(e),n=Math.abs(s);if(i===n)if(0===t)r=a=1/i;else{const e=i*t;r=a=e<1?1/e:1}else if(0===t){r=1/i;a=1/n}else{const e=i*t,s=n*t;r=e<1?1/e:1;a=s<1?1/s:1}}else{const o=Math.abs(e*s-i*n),l=Math.hypot(e,i),h=Math.hypot(n,s);if(0===t){r=h/o;a=l/o}else{const e=t*o;r=h>e?h/e:1;a=l>e?l/e:1}}this._cachedScaleForStroking[0]=r;this._cachedScaleForStroking[1]=a}return this._cachedScaleForStroking}rescaleAndStroke(t,e){const{ctx:i,current:{lineWidth:n}}=this,[s,r]=this.getScaleForStroking();if(s===r){i.lineWidth=(n||1)*s;i.stroke(t);return}const a=i.getLineDash();e&&i.save();i.scale(s,r);ft.a=1/s;ft.d=1/r;const o=new Path2D;o.addPath(t,ft);if(a.length>0){const t=Math.max(s,r);i.setLineDash(a.map((e=>e/t)));i.lineDashOffset/=t}i.lineWidth=n||1;i.stroke(o);e&&i.restore()}isContentVisible(){for(let t=this.markedContentStack.length-1;t>=0;t--)if(!this.markedContentStack[t].visible)return!1;return!0}}for(const t in I)void 0!==CanvasGraphics.prototype[t]&&(CanvasGraphics.prototype[I[t]]=CanvasGraphics.prototype[t]);class GlobalWorkerOptions{static#Gi=null;static#Wi="";static get workerPort(){return this.#Gi}static set workerPort(t){if(!("undefined"!=typeof Worker&&t instanceof Worker)&&null!==t)throw new Error("Invalid `workerPort` type.");this.#Gi=t}static get workerSrc(){return this.#Wi}static set workerSrc(t){if("string"!=typeof t)throw new Error("Invalid `workerSrc` type.");this.#Wi=t}}class Metadata{#Vi;#$i;constructor({parsedData:t,rawData:e}){this.#Vi=t;this.#$i=e}getRaw(){return this.#$i}get(t){return this.#Vi.get(t)??null}[Symbol.iterator](){return this.#Vi.entries()}}const xt=Symbol("INTERNAL");class OptionalContentGroup{#qi=!1;#Xi=!1;#Ki=!1;#Yi=!0;constructor(t,{name:e,intent:i,usage:n,rbGroups:s}){this.#qi=!!(t&a);this.#Xi=!!(t&o);this.name=e;this.intent=i;this.usage=n;this.rbGroups=s}get visible(){if(this.#Ki)return this.#Yi;if(!this.#Yi)return!1;const{print:t,view:e}=this.usage;return this.#qi?"OFF"!==e?.viewState:!this.#Xi||"OFF"!==t?.printState}_setVisible(t,e,i=!1){t!==xt&&unreachable("Internal method `_setVisible` called.");this.#Ki=i;this.#Yi=e}}class OptionalContentConfig{#Qi=null;#Ji=new Map;#Zi=null;#tn=null;constructor(t,e=a){this.renderingIntent=e;this.name=null;this.creator=null;if(null!==t){this.name=t.name;this.creator=t.creator;this.#tn=t.order;for(const i of t.groups)this.#Ji.set(i.id,new OptionalContentGroup(e,i));if("OFF"===t.baseState)for(const t of this.#Ji.values())t._setVisible(xt,!1);for(const e of t.on)this.#Ji.get(e)._setVisible(xt,!0);for(const e of t.off)this.#Ji.get(e)._setVisible(xt,!1);this.#Zi=this.getHash()}}#en(t){const e=t.length;if(e<2)return!0;const i=t[0];for(let n=1;n<e;n++){const e=t[n];let s;if(Array.isArray(e))s=this.#en(e);else{if(!this.#Ji.has(e)){warn(`Optional content group not found: ${e}`);return!0}s=this.#Ji.get(e).visible}switch(i){case"And":if(!s)return!1;break;case"Or":if(s)return!0;break;case"Not":return!s;default:return!0}}return"And"===i}isVisible(t){if(0===this.#Ji.size)return!0;if(!t){info("Optional content group not defined.");return!0}if("OCG"===t.type){if(!this.#Ji.has(t.id)){warn(`Optional content group not found: ${t.id}`);return!0}return this.#Ji.get(t.id).visible}if("OCMD"===t.type){if(t.expression)return this.#en(t.expression);if(!t.policy||"AnyOn"===t.policy){for(const e of t.ids){if(!this.#Ji.has(e)){warn(`Optional content group not found: ${e}`);return!0}if(this.#Ji.get(e).visible)return!0}return!1}if("AllOn"===t.policy){for(const e of t.ids){if(!this.#Ji.has(e)){warn(`Optional content group not found: ${e}`);return!0}if(!this.#Ji.get(e).visible)return!1}return!0}if("AnyOff"===t.policy){for(const e of t.ids){if(!this.#Ji.has(e)){warn(`Optional content group not found: ${e}`);return!0}if(!this.#Ji.get(e).visible)return!0}return!1}if("AllOff"===t.policy){for(const e of t.ids){if(!this.#Ji.has(e)){warn(`Optional content group not found: ${e}`);return!0}if(this.#Ji.get(e).visible)return!1}return!0}warn(`Unknown optional content policy ${t.policy}.`);return!0}warn(`Unknown group type ${t.type}.`);return!0}setVisibility(t,e=!0,i=!0){const n=this.#Ji.get(t);if(n){if(i&&e&&n.rbGroups.length)for(const e of n.rbGroups)for(const i of e)i!==t&&this.#Ji.get(i)?._setVisible(xt,!1,!0);n._setVisible(xt,!!e,!0);this.#Qi=null}else warn(`Optional content group not found: ${t}`)}setOCGState({state:t,preserveRB:e}){let i;for(const n of t){switch(n){case"ON":case"OFF":case"Toggle":i=n;continue}const t=this.#Ji.get(n);if(t)switch(i){case"ON":this.setVisibility(n,!0,e);break;case"OFF":this.setVisibility(n,!1,e);break;case"Toggle":this.setVisibility(n,!t.visible,e)}}this.#Qi=null}get hasInitialVisibility(){return null===this.#Zi||this.getHash()===this.#Zi}getOrder(){return this.#Ji.size?this.#tn?this.#tn.slice():[...this.#Ji.keys()]:null}getGroup(t){return this.#Ji.get(t)||null}getHash(){if(null!==this.#Qi)return this.#Qi;const t=new MurmurHash3_64;for(const[e,i]of this.#Ji)t.update(`${e}:${i.visible}`);return this.#Qi=t.hexdigest()}[Symbol.iterator](){return this.#Ji.entries()}}class PDFDataTransportStream{constructor(t,{disableRange:e=!1,disableStream:i=!1}){assert(t,'PDFDataTransportStream - missing required "pdfDataRangeTransport" argument.');const{length:n,initialData:s,progressiveDone:r,contentDispositionFilename:a}=t;this._queuedChunks=[];this._progressiveDone=r;this._contentDispositionFilename=a;if(s?.length>0){const t=s instanceof Uint8Array&&s.byteLength===s.buffer.byteLength?s.buffer:new Uint8Array(s).buffer;this._queuedChunks.push(t)}this._pdfDataRangeTransport=t;this._isStreamingSupported=!i;this._isRangeSupported=!e;this._contentLength=n;this._fullRequestReader=null;this._rangeReaders=[];t.addRangeListener(((t,e)=>{this._onReceiveData({begin:t,chunk:e})}));t.addProgressListener(((t,e)=>{this._onProgress({loaded:t,total:e})}));t.addProgressiveReadListener((t=>{this._onReceiveData({chunk:t})}));t.addProgressiveDoneListener((()=>{this._onProgressiveDone()}));t.transportReady()}_onReceiveData({begin:t,chunk:e}){const i=e instanceof Uint8Array&&e.byteLength===e.buffer.byteLength?e.buffer:new Uint8Array(e).buffer;if(void 0===t)this._fullRequestReader?this._fullRequestReader._enqueue(i):this._queuedChunks.push(i);else{assert(this._rangeReaders.some((function(e){if(e._begin!==t)return!1;e._enqueue(i);return!0})),"_onReceiveData - no `PDFDataTransportStreamRangeReader` instance found.")}}get _progressiveDataLength(){return this._fullRequestReader?._loaded??0}_onProgress(t){void 0===t.total?this._rangeReaders[0]?.onProgress?.({loaded:t.loaded}):this._fullRequestReader?.onProgress?.({loaded:t.loaded,total:t.total})}_onProgressiveDone(){this._fullRequestReader?.progressiveDone();this._progressiveDone=!0}_removeRangeReader(t){const e=this._rangeReaders.indexOf(t);e>=0&&this._rangeReaders.splice(e,1)}getFullReader(){assert(!this._fullRequestReader,"PDFDataTransportStream.getFullReader can only be called once.");const t=this._queuedChunks;this._queuedChunks=null;return new PDFDataTransportStreamReader(this,t,this._progressiveDone,this._contentDispositionFilename)}getRangeReader(t,e){if(e<=this._progressiveDataLength)return null;const i=new PDFDataTransportStreamRangeReader(this,t,e);this._pdfDataRangeTransport.requestDataRange(t,e);this._rangeReaders.push(i);return i}cancelAllRequests(t){this._fullRequestReader?.cancel(t);for(const e of this._rangeReaders.slice(0))e.cancel(t);this._pdfDataRangeTransport.abort()}}class PDFDataTransportStreamReader{constructor(t,e,i=!1,n=null){this._stream=t;this._done=i||!1;this._filename=isPdfFile(n)?n:null;this._queuedChunks=e||[];this._loaded=0;for(const t of this._queuedChunks)this._loaded+=t.byteLength;this._requests=[];this._headersReady=Promise.resolve();t._fullRequestReader=this;this.onProgress=null}_enqueue(t){if(!this._done){if(this._requests.length>0){this._requests.shift().resolve({value:t,done:!1})}else this._queuedChunks.push(t);this._loaded+=t.byteLength}}get headersReady(){return this._headersReady}get filename(){return this._filename}get isRangeSupported(){return this._stream._isRangeSupported}get isStreamingSupported(){return this._stream._isStreamingSupported}get contentLength(){return this._stream._contentLength}async read(){if(this._queuedChunks.length>0){return{value:this._queuedChunks.shift(),done:!1}}if(this._done)return{value:void 0,done:!0};const t=Promise.withResolvers();this._requests.push(t);return t.promise}cancel(t){this._done=!0;for(const t of this._requests)t.resolve({value:void 0,done:!0});this._requests.length=0}progressiveDone(){this._done||(this._done=!0)}}class PDFDataTransportStreamRangeReader{constructor(t,e,i){this._stream=t;this._begin=e;this._end=i;this._queuedChunk=null;this._requests=[];this._done=!1;this.onProgress=null}_enqueue(t){if(!this._done){if(0===this._requests.length)this._queuedChunk=t;else{this._requests.shift().resolve({value:t,done:!1});for(const t of this._requests)t.resolve({value:void 0,done:!0});this._requests.length=0}this._done=!0;this._stream._removeRangeReader(this)}}get isStreamingSupported(){return!1}async read(){if(this._queuedChunk){const t=this._queuedChunk;this._queuedChunk=null;return{value:t,done:!1}}if(this._done)return{value:void 0,done:!0};const t=Promise.withResolvers();this._requests.push(t);return t.promise}cancel(t){this._done=!0;for(const t of this._requests)t.resolve({value:void 0,done:!0});this._requests.length=0;this._stream._removeRangeReader(this)}}function createHeaders(t,e){const i=new Headers;if(!t||!e||"object"!=typeof e)return i;for(const t in e){const n=e[t];void 0!==n&&i.append(t,n)}return i}function getResponseOrigin(t){return URL.parse(t)?.origin??null}function validateRangeRequestCapabilities({responseHeaders:t,isHttp:e,rangeChunkSize:i,disableRange:n}){const s={allowRangeRequests:!1,suggestedLength:void 0},r=parseInt(t.get("Content-Length"),10);if(!Number.isInteger(r))return s;s.suggestedLength=r;if(r<=2*i)return s;if(n||!e)return s;if("bytes"!==t.get("Accept-Ranges"))return s;if("identity"!==(t.get("Content-Encoding")||"identity"))return s;s.allowRangeRequests=!0;return s}function extractFilenameFromHeader(t){const e=t.get("Content-Disposition");if(e){let t=function getFilenameFromContentDispositionHeader(t){let e=!0,i=toParamRegExp("filename\\*","i").exec(t);if(i){i=i[1];let t=rfc2616unquote(i);t=unescape(t);t=rfc5987decode(t);t=rfc2047decode(t);return fixupEncoding(t)}i=function rfc2231getparam(t){const e=[];let i;const n=toParamRegExp("filename\\*((?!0\\d)\\d+)(\\*?)","ig");for(;null!==(i=n.exec(t));){let[,t,n,s]=i;t=parseInt(t,10);if(t in e){if(0===t)break}else e[t]=[n,s]}const s=[];for(let t=0;t<e.length&&t in e;++t){let[i,n]=e[t];n=rfc2616unquote(n);if(i){n=unescape(n);0===t&&(n=rfc5987decode(n))}s.push(n)}return s.join("")}(t);if(i)return fixupEncoding(rfc2047decode(i));i=toParamRegExp("filename","i").exec(t);if(i){i=i[1];let t=rfc2616unquote(i);t=rfc2047decode(t);return fixupEncoding(t)}function toParamRegExp(t,e){return new RegExp("(?:^|;)\\s*"+t+'\\s*=\\s*([^";\\s][^;\\s]*|"(?:[^"\\\\]|\\\\"?)+"?)',e)}function textdecode(t,i){if(t){if(!/^[\x00-\xFF]+$/.test(i))return i;try{const n=new TextDecoder(t,{fatal:!0}),s=stringToBytes(i);i=n.decode(s);e=!1}catch{}}return i}function fixupEncoding(t){if(e&&/[\x80-\xff]/.test(t)){t=textdecode("utf-8",t);e&&(t=textdecode("iso-8859-1",t))}return t}function rfc2616unquote(t){if(t.startsWith('"')){const e=t.slice(1).split('\\"');for(let t=0;t<e.length;++t){const i=e[t].indexOf('"');if(-1!==i){e[t]=e[t].slice(0,i);e.length=t+1}e[t]=e[t].replaceAll(/\\(.)/g,"$1")}t=e.join('"')}return t}function rfc5987decode(t){const e=t.indexOf("'");return-1===e?t:textdecode(t.slice(0,e),t.slice(e+1).replace(/^[^']*'/,""))}function rfc2047decode(t){return!t.startsWith("=?")||/[\x00-\x19\x80-\xff]/.test(t)?t:t.replaceAll(/=\?([\w-]*)\?([QqBb])\?((?:[^?]|\?(?!=))*)\?=/g,(function(t,e,i,n){if("q"===i||"Q"===i)return textdecode(e,n=(n=n.replaceAll("_"," ")).replaceAll(/=([0-9a-fA-F]{2})/g,(function(t,e){return String.fromCharCode(parseInt(e,16))})));try{n=atob(n)}catch{}return textdecode(e,n)}))}return""}(e);if(t.includes("%"))try{t=decodeURIComponent(t)}catch{}if(isPdfFile(t))return t}return null}function createResponseError(t,e){return new ResponseException(`Unexpected server response (${t}) while retrieving PDF "${e}".`,t,404===t||0===t&&e.startsWith("file:"))}function validateResponseStatus(t){return 200===t||206===t}function createFetchOptions(t,e,i){return{method:"GET",headers:t,signal:i.signal,mode:"cors",credentials:e?"include":"same-origin",redirect:"follow"}}function getArrayBuffer(t){if(t instanceof Uint8Array)return t.buffer;if(t instanceof ArrayBuffer)return t;warn(`getArrayBuffer - unexpected data format: ${t}`);return new Uint8Array(t).buffer}class PDFFetchStream{_responseOrigin=null;constructor(t){this.source=t;this.isHttp=/^https?:/i.test(t.url);this.headers=createHeaders(this.isHttp,t.httpHeaders);this._fullRequestReader=null;this._rangeRequestReaders=[]}get _progressiveDataLength(){return this._fullRequestReader?._loaded??0}getFullReader(){assert(!this._fullRequestReader,"PDFFetchStream.getFullReader can only be called once.");this._fullRequestReader=new PDFFetchStreamReader(this);return this._fullRequestReader}getRangeReader(t,e){if(e<=this._progressiveDataLength)return null;const i=new PDFFetchStreamRangeReader(this,t,e);this._rangeRequestReaders.push(i);return i}cancelAllRequests(t){this._fullRequestReader?.cancel(t);for(const e of this._rangeRequestReaders.slice(0))e.cancel(t)}}class PDFFetchStreamReader{constructor(t){this._stream=t;this._reader=null;this._loaded=0;this._filename=null;const e=t.source;this._withCredentials=e.withCredentials||!1;this._contentLength=e.length;this._headersCapability=Promise.withResolvers();this._disableRange=e.disableRange||!1;this._rangeChunkSize=e.rangeChunkSize;this._rangeChunkSize||this._disableRange||(this._disableRange=!0);this._abortController=new AbortController;this._isStreamingSupported=!e.disableStream;this._isRangeSupported=!e.disableRange;const i=new Headers(t.headers),n=e.url;fetch(n,createFetchOptions(i,this._withCredentials,this._abortController)).then((e=>{t._responseOrigin=getResponseOrigin(e.url);if(!validateResponseStatus(e.status))throw createResponseError(e.status,n);this._reader=e.body.getReader();this._headersCapability.resolve();const i=e.headers,{allowRangeRequests:s,suggestedLength:r}=validateRangeRequestCapabilities({responseHeaders:i,isHttp:t.isHttp,rangeChunkSize:this._rangeChunkSize,disableRange:this._disableRange});this._isRangeSupported=s;this._contentLength=r||this._contentLength;this._filename=extractFilenameFromHeader(i);!this._isStreamingSupported&&this._isRangeSupported&&this.cancel(new AbortException("Streaming is disabled."))})).catch(this._headersCapability.reject);this.onProgress=null}get headersReady(){return this._headersCapability.promise}get filename(){return this._filename}get contentLength(){return this._contentLength}get isRangeSupported(){return this._isRangeSupported}get isStreamingSupported(){return this._isStreamingSupported}async read(){await this._headersCapability.promise;const{value:t,done:e}=await this._reader.read();if(e)return{value:t,done:e};this._loaded+=t.byteLength;this.onProgress?.({loaded:this._loaded,total:this._contentLength});return{value:getArrayBuffer(t),done:!1}}cancel(t){this._reader?.cancel(t);this._abortController.abort()}}class PDFFetchStreamRangeReader{constructor(t,e,i){this._stream=t;this._reader=null;this._loaded=0;const n=t.source;this._withCredentials=n.withCredentials||!1;this._readCapability=Promise.withResolvers();this._isStreamingSupported=!n.disableStream;this._abortController=new AbortController;const s=new Headers(t.headers);s.append("Range",`bytes=${e}-${i-1}`);const r=n.url;fetch(r,createFetchOptions(s,this._withCredentials,this._abortController)).then((e=>{const i=getResponseOrigin(e.url);if(i!==t._responseOrigin)throw new Error(`Expected range response-origin "${i}" to match "${t._responseOrigin}".`);if(!validateResponseStatus(e.status))throw createResponseError(e.status,r);this._readCapability.resolve();this._reader=e.body.getReader()})).catch(this._readCapability.reject);this.onProgress=null}get isStreamingSupported(){return this._isStreamingSupported}async read(){await this._readCapability.promise;const{value:t,done:e}=await this._reader.read();if(e)return{value:t,done:e};this._loaded+=t.byteLength;this.onProgress?.({loaded:this._loaded});return{value:getArrayBuffer(t),done:!1}}cancel(t){this._reader?.cancel(t);this._abortController.abort()}}class NetworkManager{_responseOrigin=null;constructor({url:t,httpHeaders:e,withCredentials:i}){this.url=t;this.isHttp=/^https?:/i.test(t);this.headers=createHeaders(this.isHttp,e);this.withCredentials=i||!1;this.currXhrId=0;this.pendingRequests=Object.create(null)}request(t){const e=new XMLHttpRequest,i=this.currXhrId++,n=this.pendingRequests[i]={xhr:e};e.open("GET",this.url);e.withCredentials=this.withCredentials;for(const[t,i]of this.headers)e.setRequestHeader(t,i);if(this.isHttp&&"begin"in t&&"end"in t){e.setRequestHeader("Range",`bytes=${t.begin}-${t.end-1}`);n.expectedStatus=206}else n.expectedStatus=200;e.responseType="arraybuffer";assert(t.onError,"Expected `onError` callback to be provided.");e.onerror=()=>{t.onError(e.status)};e.onreadystatechange=this.onStateChange.bind(this,i);e.onprogress=this.onProgress.bind(this,i);n.onHeadersReceived=t.onHeadersReceived;n.onDone=t.onDone;n.onError=t.onError;n.onProgress=t.onProgress;e.send(null);return i}onProgress(t,e){const i=this.pendingRequests[t];i&&i.onProgress?.(e)}onStateChange(t,e){const i=this.pendingRequests[t];if(!i)return;const n=i.xhr;if(n.readyState>=2&&i.onHeadersReceived){i.onHeadersReceived();delete i.onHeadersReceived}if(4!==n.readyState)return;if(!(t in this.pendingRequests))return;delete this.pendingRequests[t];if(0===n.status&&this.isHttp){i.onError(n.status);return}const s=n.status||200;if(!(200===s&&206===i.expectedStatus)&&s!==i.expectedStatus){i.onError(n.status);return}const r=function network_getArrayBuffer(t){const e=t.response;return"string"!=typeof e?e:stringToBytes(e).buffer}(n);if(206===s){const t=n.getResponseHeader("Content-Range"),e=/bytes (\d+)-(\d+)\/(\d+)/.exec(t);if(e)i.onDone({begin:parseInt(e[1],10),chunk:r});else{warn('Missing or invalid "Content-Range" header.');i.onError(0)}}else r?i.onDone({begin:0,chunk:r}):i.onError(n.status)}getRequestXhr(t){return this.pendingRequests[t].xhr}isPendingRequest(t){return t in this.pendingRequests}abortRequest(t){const e=this.pendingRequests[t].xhr;delete this.pendingRequests[t];e.abort()}}class PDFNetworkStream{constructor(t){this._source=t;this._manager=new NetworkManager(t);this._rangeChunkSize=t.rangeChunkSize;this._fullRequestReader=null;this._rangeRequestReaders=[]}_onRangeRequestReaderClosed(t){const e=this._rangeRequestReaders.indexOf(t);e>=0&&this._rangeRequestReaders.splice(e,1)}getFullReader(){assert(!this._fullRequestReader,"PDFNetworkStream.getFullReader can only be called once.");this._fullRequestReader=new PDFNetworkStreamFullRequestReader(this._manager,this._source);return this._fullRequestReader}getRangeReader(t,e){const i=new PDFNetworkStreamRangeRequestReader(this._manager,t,e);i.onClosed=this._onRangeRequestReaderClosed.bind(this);this._rangeRequestReaders.push(i);return i}cancelAllRequests(t){this._fullRequestReader?.cancel(t);for(const e of this._rangeRequestReaders.slice(0))e.cancel(t)}}class PDFNetworkStreamFullRequestReader{constructor(t,e){this._manager=t;this._url=e.url;this._fullRequestId=t.request({onHeadersReceived:this._onHeadersReceived.bind(this),onDone:this._onDone.bind(this),onError:this._onError.bind(this),onProgress:this._onProgress.bind(this)});this._headersCapability=Promise.withResolvers();this._disableRange=e.disableRange||!1;this._contentLength=e.length;this._rangeChunkSize=e.rangeChunkSize;this._rangeChunkSize||this._disableRange||(this._disableRange=!0);this._isStreamingSupported=!1;this._isRangeSupported=!1;this._cachedChunks=[];this._requests=[];this._done=!1;this._storedError=void 0;this._filename=null;this.onProgress=null}_onHeadersReceived(){const t=this._fullRequestId,e=this._manager.getRequestXhr(t);this._manager._responseOrigin=getResponseOrigin(e.responseURL);const i=e.getAllResponseHeaders(),n=new Headers(i?i.trimStart().replace(/[^\S ]+$/,"").split(/[\r\n]+/).map((t=>{const[e,...i]=t.split(": ");return[e,i.join(": ")]})):[]),{allowRangeRequests:s,suggestedLength:r}=validateRangeRequestCapabilities({responseHeaders:n,isHttp:this._manager.isHttp,rangeChunkSize:this._rangeChunkSize,disableRange:this._disableRange});s&&(this._isRangeSupported=!0);this._contentLength=r||this._contentLength;this._filename=extractFilenameFromHeader(n);this._isRangeSupported&&this._manager.abortRequest(t);this._headersCapability.resolve()}_onDone(t){if(t)if(this._requests.length>0){this._requests.shift().resolve({value:t.chunk,done:!1})}else this._cachedChunks.push(t.chunk);this._done=!0;if(!(this._cachedChunks.length>0)){for(const t of this._requests)t.resolve({value:void 0,done:!0});this._requests.length=0}}_onError(t){this._storedError=createResponseError(t,this._url);this._headersCapability.reject(this._storedError);for(const t of this._requests)t.reject(this._storedError);this._requests.length=0;this._cachedChunks.length=0}_onProgress(t){this.onProgress?.({loaded:t.loaded,total:t.lengthComputable?t.total:this._contentLength})}get filename(){return this._filename}get isRangeSupported(){return this._isRangeSupported}get isStreamingSupported(){return this._isStreamingSupported}get contentLength(){return this._contentLength}get headersReady(){return this._headersCapability.promise}async read(){await this._headersCapability.promise;if(this._storedError)throw this._storedError;if(this._cachedChunks.length>0){return{value:this._cachedChunks.shift(),done:!1}}if(this._done)return{value:void 0,done:!0};const t=Promise.withResolvers();this._requests.push(t);return t.promise}cancel(t){this._done=!0;this._headersCapability.reject(t);for(const t of this._requests)t.resolve({value:void 0,done:!0});this._requests.length=0;this._manager.isPendingRequest(this._fullRequestId)&&this._manager.abortRequest(this._fullRequestId);this._fullRequestReader=null}}class PDFNetworkStreamRangeRequestReader{constructor(t,e,i){this._manager=t;this._url=t.url;this._requestId=t.request({begin:e,end:i,onHeadersReceived:this._onHeadersReceived.bind(this),onDone:this._onDone.bind(this),onError:this._onError.bind(this),onProgress:this._onProgress.bind(this)});this._requests=[];this._queuedChunk=null;this._done=!1;this._storedError=void 0;this.onProgress=null;this.onClosed=null}_onHeadersReceived(){const t=getResponseOrigin(this._manager.getRequestXhr(this._requestId)?.responseURL);if(t!==this._manager._responseOrigin){this._storedError=new Error(`Expected range response-origin "${t}" to match "${this._manager._responseOrigin}".`);this._onError(0)}}_close(){this.onClosed?.(this)}_onDone(t){const e=t.chunk;if(this._requests.length>0){this._requests.shift().resolve({value:e,done:!1})}else this._queuedChunk=e;this._done=!0;for(const t of this._requests)t.resolve({value:void 0,done:!0});this._requests.length=0;this._close()}_onError(t){this._storedError??=createResponseError(t,this._url);for(const t of this._requests)t.reject(this._storedError);this._requests.length=0;this._queuedChunk=null}_onProgress(t){this.isStreamingSupported||this.onProgress?.({loaded:t.loaded})}get isStreamingSupported(){return!1}async read(){if(this._storedError)throw this._storedError;if(null!==this._queuedChunk){const t=this._queuedChunk;this._queuedChunk=null;return{value:t,done:!1}}if(this._done)return{value:void 0,done:!0};const t=Promise.withResolvers();this._requests.push(t);return t.promise}cancel(t){this._done=!0;for(const t of this._requests)t.resolve({value:void 0,done:!0});this._requests.length=0;this._manager.isPendingRequest(this._requestId)&&this._manager.abortRequest(this._requestId);this._close()}}const _t=/^[a-z][a-z0-9\-+.]+:/i;class PDFNodeStream{constructor(t){this.source=t;this.url=function parseUrlOrPath(t){if(_t.test(t))return new URL(t);const e=process.getBuiltinModule("url");return new URL(e.pathToFileURL(t))}(t.url);assert("file:"===this.url.protocol,"PDFNodeStream only supports file:// URLs.");this._fullRequestReader=null;this._rangeRequestReaders=[]}get _progressiveDataLength(){return this._fullRequestReader?._loaded??0}getFullReader(){assert(!this._fullRequestReader,"PDFNodeStream.getFullReader can only be called once.");this._fullRequestReader=new PDFNodeStreamFsFullReader(this);return this._fullRequestReader}getRangeReader(t,e){if(e<=this._progressiveDataLength)return null;const i=new PDFNodeStreamFsRangeReader(this,t,e);this._rangeRequestReaders.push(i);return i}cancelAllRequests(t){this._fullRequestReader?.cancel(t);for(const e of this._rangeRequestReaders.slice(0))e.cancel(t)}}class PDFNodeStreamFsFullReader{constructor(t){this._url=t.url;this._done=!1;this._storedError=null;this.onProgress=null;const e=t.source;this._contentLength=e.length;this._loaded=0;this._filename=null;this._disableRange=e.disableRange||!1;this._rangeChunkSize=e.rangeChunkSize;this._rangeChunkSize||this._disableRange||(this._disableRange=!0);this._isStreamingSupported=!e.disableStream;this._isRangeSupported=!e.disableRange;this._readableStream=null;this._readCapability=Promise.withResolvers();this._headersCapability=Promise.withResolvers();const i=process.getBuiltinModule("fs");i.promises.lstat(this._url).then((t=>{this._contentLength=t.size;this._setReadableStream(i.createReadStream(this._url));this._headersCapability.resolve()}),(t=>{"ENOENT"===t.code&&(t=createResponseError(0,this._url.href));this._storedError=t;this._headersCapability.reject(t)}))}get headersReady(){return this._headersCapability.promise}get filename(){return this._filename}get contentLength(){return this._contentLength}get isRangeSupported(){return this._isRangeSupported}get isStreamingSupported(){return this._isStreamingSupported}async read(){await this._readCapability.promise;if(this._done)return{value:void 0,done:!0};if(this._storedError)throw this._storedError;const t=this._readableStream.read();if(null===t){this._readCapability=Promise.withResolvers();return this.read()}this._loaded+=t.length;this.onProgress?.({loaded:this._loaded,total:this._contentLength});return{value:new Uint8Array(t).buffer,done:!1}}cancel(t){this._readableStream?this._readableStream.destroy(t):this._error(t)}_error(t){this._storedError=t;this._readCapability.resolve()}_setReadableStream(t){this._readableStream=t;t.on("readable",(()=>{this._readCapability.resolve()}));t.on("end",(()=>{t.destroy();this._done=!0;this._readCapability.resolve()}));t.on("error",(t=>{this._error(t)}));!this._isStreamingSupported&&this._isRangeSupported&&this._error(new AbortException("streaming is disabled"));this._storedError&&this._readableStream.destroy(this._storedError)}}class PDFNodeStreamFsRangeReader{constructor(t,e,i){this._url=t.url;this._done=!1;this._storedError=null;this.onProgress=null;this._loaded=0;this._readableStream=null;this._readCapability=Promise.withResolvers();const n=t.source;this._isStreamingSupported=!n.disableStream;const s=process.getBuiltinModule("fs");this._setReadableStream(s.createReadStream(this._url,{start:e,end:i-1}))}get isStreamingSupported(){return this._isStreamingSupported}async read(){await this._readCapability.promise;if(this._done)return{value:void 0,done:!0};if(this._storedError)throw this._storedError;const t=this._readableStream.read();if(null===t){this._readCapability=Promise.withResolvers();return this.read()}this._loaded+=t.length;this.onProgress?.({loaded:this._loaded});return{value:new Uint8Array(t).buffer,done:!1}}cancel(t){this._readableStream?this._readableStream.destroy(t):this._error(t)}_error(t){this._storedError=t;this._readCapability.resolve()}_setReadableStream(t){this._readableStream=t;t.on("readable",(()=>{this._readCapability.resolve()}));t.on("end",(()=>{t.destroy();this._done=!0;this._readCapability.resolve()}));t.on("error",(t=>{this._error(t)}));this._storedError&&this._readableStream.destroy(this._storedError)}}const Et=Symbol("INITIAL_DATA");class PDFObjects{#in=Object.create(null);#nn(t){return this.#in[t]||={...Promise.withResolvers(),data:Et}}get(t,e=null){if(e){const i=this.#nn(t);i.promise.then((()=>e(i.data)));return null}const i=this.#in[t];if(!i||i.data===Et)throw new Error(`Requesting object that isn't resolved yet ${t}.`);return i.data}has(t){const e=this.#in[t];return!!e&&e.data!==Et}delete(t){const e=this.#in[t];if(!e||e.data===Et)return!1;delete this.#in[t];return!0}resolve(t,e=null){const i=this.#nn(t);i.data=e;i.resolve()}clear(){for(const t in this.#in){const{data:e}=this.#in[t];e?.bitmap?.close()}this.#in=Object.create(null)}*[Symbol.iterator](){for(const t in this.#in){const{data:e}=this.#in[t];e!==Et&&(yield[t,e])}}}class TextLayer{#sn=Promise.withResolvers();#bt=null;#rn=!1;#an=!!globalThis.FontInspector?.enabled;#on=null;#ln=null;#hn=0;#cn=0;#dn=null;#un=null;#pn=0;#gn=0;#fn=Object.create(null);#mn=[];#bn=null;#vn=[];#wn=new WeakMap;#yn=null;static#An=new Map;static#xn=new Map;static#_n=new WeakMap;static#En=null;static#Sn=new Set;constructor({textContentSource:t,container:e,viewport:i}){if(t instanceof ReadableStream)this.#bn=t;else{if("object"!=typeof t)throw new Error('No "textContentSource" parameter specified.');this.#bn=new ReadableStream({start(e){e.enqueue(t);e.close()}})}this.#bt=this.#un=e;this.#gn=i.scale*OutputScale.pixelRatio;this.#pn=i.rotation;this.#ln={div:null,properties:null,ctx:null};const{pageWidth:n,pageHeight:s,pageX:r,pageY:a}=i.rawDims;this.#yn=[1,0,0,-1,-r,a+s];this.#cn=n;this.#hn=s;TextLayer.#Cn();setLayerDimensions(e,i);this.#sn.promise.finally((()=>{TextLayer.#Sn.delete(this);this.#ln=null;this.#fn=null})).catch((()=>{}))}static get fontFamilyMap(){const{isWindows:t,isFirefox:e}=util_FeatureTest.platform;return shadow(this,"fontFamilyMap",new Map([["sans-serif",(t&&e?"Calibri, ":"")+"sans-serif"],["monospace",(t&&e?"Lucida Console, ":"")+"monospace"]]))}render(){const pump=()=>{this.#dn.read().then((({value:t,done:e})=>{if(e)this.#sn.resolve();else{this.#on??=t.lang;Object.assign(this.#fn,t.styles);this.#Tn(t.items);pump()}}),this.#sn.reject)};this.#dn=this.#bn.getReader();TextLayer.#Sn.add(this);pump();return this.#sn.promise}update({viewport:t,onBefore:e=null}){const i=t.scale*OutputScale.pixelRatio,n=t.rotation;if(n!==this.#pn){e?.();this.#pn=n;setLayerDimensions(this.#un,{rotation:n})}if(i!==this.#gn){e?.();this.#gn=i;const t={div:null,properties:null,ctx:TextLayer.#Mn(this.#on)};for(const e of this.#vn){t.properties=this.#wn.get(e);t.div=e;this.#Dn(t)}}}cancel(){const t=new AbortException("TextLayer task cancelled.");this.#dn?.cancel(t).catch((()=>{}));this.#dn=null;this.#sn.reject(t)}get textDivs(){return this.#vn}get textContentItemsStr(){return this.#mn}#Tn(t){if(this.#rn)return;this.#ln.ctx??=TextLayer.#Mn(this.#on);const e=this.#vn,i=this.#mn;for(const n of t){if(e.length>1e5){warn("Ignoring additional textDivs for performance reasons.");this.#rn=!0;return}if(void 0!==n.str){i.push(n.str);this.#Pn(n)}else if("beginMarkedContentProps"===n.type||"beginMarkedContent"===n.type){const t=this.#bt;this.#bt=document.createElement("span");this.#bt.classList.add("markedContent");n.id&&this.#bt.setAttribute("id",`${n.id}`);t.append(this.#bt)}else"endMarkedContent"===n.type&&(this.#bt=this.#bt.parentNode)}}#Pn(t){const e=document.createElement("span"),i={angle:0,canvasWidth:0,hasText:""!==t.str,hasEOL:t.hasEOL,fontSize:0};this.#vn.push(e);const n=Util.transform(this.#yn,t.transform);let s=Math.atan2(n[1],n[0]);const r=this.#fn[t.fontName];r.vertical&&(s+=Math.PI/2);let a=this.#an&&r.fontSubstitution||r.fontFamily;a=TextLayer.fontFamilyMap.get(a)||a;const o=Math.hypot(n[2],n[3]),l=o*TextLayer.#kn(a,r,this.#on);let h,c;if(0===s){h=n[4];c=n[5]-l}else{h=n[4]+l*Math.sin(s);c=n[5]-l*Math.cos(s)}const d="calc(var(--total-scale-factor) *",u=e.style;if(this.#bt===this.#un){u.left=`${(100*h/this.#cn).toFixed(2)}%`;u.top=`${(100*c/this.#hn).toFixed(2)}%`}else{u.left=`${d}${h.toFixed(2)}px)`;u.top=`${d}${c.toFixed(2)}px)`}u.fontSize=`${d}${(TextLayer.#En*o).toFixed(2)}px)`;u.fontFamily=a;i.fontSize=o;e.setAttribute("role","presentation");e.textContent=t.str;e.dir=t.dir;this.#an&&(e.dataset.fontName=r.fontSubstitutionLoadedName||t.fontName);0!==s&&(i.angle=s*(180/Math.PI));let p=!1;if(t.str.length>1)p=!0;else if(" "!==t.str&&t.transform[0]!==t.transform[3]){const e=Math.abs(t.transform[0]),i=Math.abs(t.transform[3]);e!==i&&Math.max(e,i)/Math.min(e,i)>1.5&&(p=!0)}p&&(i.canvasWidth=r.vertical?t.height:t.width);this.#wn.set(e,i);this.#ln.div=e;this.#ln.properties=i;this.#Dn(this.#ln);i.hasText&&this.#bt.append(e);if(i.hasEOL){const t=document.createElement("br");t.setAttribute("role","presentation");this.#bt.append(t)}}#Dn(t){const{div:e,properties:i,ctx:n}=t,{style:s}=e;let r="";TextLayer.#En>1&&(r=`scale(${1/TextLayer.#En})`);if(0!==i.canvasWidth&&i.hasText){const{fontFamily:t}=s,{canvasWidth:a,fontSize:o}=i;TextLayer.#In(n,o*this.#gn,t);const{width:l}=n.measureText(e.textContent);l>0&&(r=`scaleX(${a*this.#gn/l}) ${r}`)}0!==i.angle&&(r=`rotate(${i.angle}deg) ${r}`);r.length>0&&(s.transform=r)}static cleanup(){if(!(this.#Sn.size>0)){this.#An.clear();for(const{canvas:t}of this.#xn.values())t.remove();this.#xn.clear()}}static#Mn(t=null){let e=this.#xn.get(t||="");if(!e){const i=document.createElement("canvas");i.className="hiddenCanvasElement";i.lang=t;document.body.append(i);e=i.getContext("2d",{alpha:!1,willReadFrequently:!0});this.#xn.set(t,e);this.#_n.set(e,{size:0,family:""})}return e}static#In(t,e,i){const n=this.#_n.get(t);if(e!==n.size||i!==n.family){t.font=`${e}px ${i}`;n.size=e;n.family=i}}static#Cn(){if(null!==this.#En)return;const t=document.createElement("div");t.style.opacity=0;t.style.lineHeight=1;t.style.fontSize="1px";t.style.position="absolute";t.textContent="X";document.body.append(t);this.#En=t.getBoundingClientRect().height;t.remove()}static#kn(t,e,i){const n=this.#An.get(t);if(n)return n;const s=this.#Mn(i);s.canvas.width=s.canvas.height=30;this.#In(s,30,t);const r=s.measureText(""),a=r.fontBoundingBoxAscent,o=Math.abs(r.fontBoundingBoxDescent);s.canvas.width=s.canvas.height=0;let l=.8;if(a)l=a/(a+o);else{util_FeatureTest.platform.isFirefox&&warn("Enable the `dom.textMetrics.fontBoundingBox.enabled` preference in `about:config` to improve TextLayer rendering.");e.ascent?l=e.ascent:e.descent&&(l=1+e.descent)}this.#An.set(t,l);return l}}class XfaText{static textContent(t){const e=[],i={items:e,styles:Object.create(null)};!function walk(t){if(!t)return;let i=null;const n=t.name;if("#text"===n)i=t.value;else{if(!XfaText.shouldBuildText(n))return;t?.attributes?.textContent?i=t.attributes.textContent:t.value&&(i=t.value)}null!==i&&e.push({str:i});if(t.children)for(const e of t.children)walk(e)}(t);return i}static shouldBuildText(t){return!("textarea"===t||"input"===t||"option"===t||"select"===t)}}function getDocument(t={}){"string"==typeof t||t instanceof URL?t={url:t}:(t instanceof ArrayBuffer||ArrayBuffer.isView(t))&&(t={data:t});const e=new PDFDocumentLoadingTask,{docId:n}=e,s=t.url?function getUrlProp(t){if(t instanceof URL)return t.href;if("string"==typeof t){if(i)return t;const e=URL.parse(t,window.location);if(e)return e.href}throw new Error("Invalid PDF url data: either string or URL-object is expected in the url property.")}(t.url):null,r=t.data?function getDataProp(t){if(i&&"undefined"!=typeof Buffer&&t instanceof Buffer)throw new Error("Please provide binary data as `Uint8Array`, rather than `Buffer`.");if(t instanceof Uint8Array&&t.byteLength===t.buffer.byteLength)return t;if("string"==typeof t)return stringToBytes(t);if(t instanceof ArrayBuffer||ArrayBuffer.isView(t)||"object"==typeof t&&!isNaN(t?.length))return new Uint8Array(t);throw new Error("Invalid PDF binary data: either TypedArray, string, or array-like object is expected in the data property.")}(t.data):null,a=t.httpHeaders||null,o=!0===t.withCredentials,l=t.password??null,h=t.range instanceof PDFDataRangeTransport?t.range:null,c=Number.isInteger(t.rangeChunkSize)&&t.rangeChunkSize>0?t.rangeChunkSize:65536;let d=t.worker instanceof PDFWorker?t.worker:null;const u=t.verbosity,p="string"!=typeof t.docBaseUrl||isDataScheme(t.docBaseUrl)?null:t.docBaseUrl,g=getFactoryUrlProp(t.cMapUrl),f=!1!==t.cMapPacked,m=t.CMapReaderFactory||(i?NodeCMapReaderFactory:DOMCMapReaderFactory),b=getFactoryUrlProp(t.iccUrl),v=getFactoryUrlProp(t.standardFontDataUrl),w=t.StandardFontDataFactory||(i?NodeStandardFontDataFactory:DOMStandardFontDataFactory),y=getFactoryUrlProp(t.wasmUrl),A=t.WasmFactory||(i?NodeWasmFactory:DOMWasmFactory),x=!0!==t.stopAtErrors,_=Number.isInteger(t.maxImageSize)&&t.maxImageSize>-1?t.maxImageSize:-1,E=!1!==t.isEvalSupported,S="boolean"==typeof t.isOffscreenCanvasSupported?t.isOffscreenCanvasSupported:!i,C="boolean"==typeof t.isImageDecoderSupported?t.isImageDecoderSupported:!i&&(util_FeatureTest.platform.isFirefox||!globalThis.chrome),T=Number.isInteger(t.canvasMaxAreaInBytes)?t.canvasMaxAreaInBytes:-1,M="boolean"==typeof t.disableFontFace?t.disableFontFace:i,D=!0===t.fontExtraProperties,P=!0===t.enableXfa,k=t.ownerDocument||globalThis.document,I=!0===t.disableRange,R=!0===t.disableStream,L=!0===t.disableAutoFetch,O=!0===t.pdfBug,N=t.CanvasFactory||(i?NodeCanvasFactory:DOMCanvasFactory),B=t.FilterFactory||(i?NodeFilterFactory:DOMFilterFactory),U=!0===t.enableHWA,H=!1!==t.useWasm,z=h?h.length:t.length??NaN,j="boolean"==typeof t.useSystemFonts?t.useSystemFonts:!i&&!M,G="boolean"==typeof t.useWorkerFetch?t.useWorkerFetch:!!(m===DOMCMapReaderFactory&&w===DOMStandardFontDataFactory&&A===DOMWasmFactory&&g&&v&&y&&isValidFetchUrl(g,document.baseURI)&&isValidFetchUrl(v,document.baseURI)&&isValidFetchUrl(y,document.baseURI));setVerbosityLevel(u);const W={canvasFactory:new N({ownerDocument:k,enableHWA:U}),filterFactory:new B({docId:n,ownerDocument:k}),cMapReaderFactory:G?null:new m({baseUrl:g,isCompressed:f}),standardFontDataFactory:G?null:new w({baseUrl:v}),wasmFactory:G?null:new A({baseUrl:y})};if(!d){d=PDFWorker.create({verbosity:u,port:GlobalWorkerOptions.workerPort});e._worker=d}const V={docId:n,apiVersion:"5.4.54",data:r,password:l,disableAutoFetch:L,rangeChunkSize:c,length:z,docBaseUrl:p,enableXfa:P,evaluatorOptions:{maxImageSize:_,disableFontFace:M,ignoreErrors:x,isEvalSupported:E,isOffscreenCanvasSupported:S,isImageDecoderSupported:C,canvasMaxAreaInBytes:T,fontExtraProperties:D,useSystemFonts:j,useWasm:H,useWorkerFetch:G,cMapUrl:g,iccUrl:b,standardFontDataUrl:v,wasmUrl:y}},$={ownerDocument:k,pdfBug:O,styleElement:null,loadingParams:{disableAutoFetch:L,enableXfa:P}};d.promise.then((function(){if(e.destroyed)throw new Error("Loading aborted");if(d.destroyed)throw new Error("Worker was destroyed");const t=d.messageHandler.sendWithPromise("GetDocRequest",V,r?[r.buffer]:null);let l;if(h)l=new PDFDataTransportStream(h,{disableRange:I,disableStream:R});else if(!r){if(!s)throw new Error("getDocument - no `url` parameter provided.");const t=isValidFetchUrl(s)?PDFFetchStream:i?PDFNodeStream:PDFNetworkStream;l=new t({url:s,length:z,httpHeaders:a,withCredentials:o,rangeChunkSize:c,disableRange:I,disableStream:R})}return t.then((t=>{if(e.destroyed)throw new Error("Loading aborted");if(d.destroyed)throw new Error("Worker was destroyed");const i=new MessageHandler(n,t,d.port),s=new WorkerTransport(i,e,l,$,W,U);e._transport=s;i.send("Ready",null)}))})).catch(e._capability.reject);return e}class PDFDocumentLoadingTask{static#Ci=0;_capability=Promise.withResolvers();_transport=null;_worker=null;docId="d"+PDFDocumentLoadingTask.#Ci++;destroyed=!1;onPassword=null;onProgress=null;get promise(){return this._capability.promise}async destroy(){this.destroyed=!0;try{this._worker?.port&&(this._worker._pendingDestroy=!0);await(this._transport?.destroy())}catch(t){this._worker?.port&&delete this._worker._pendingDestroy;throw t}this._transport=null;this._worker?.destroy();this._worker=null}async getData(){return this._transport.getData()}}class PDFDataRangeTransport{#sn=Promise.withResolvers();#Rn=[];#Fn=[];#Ln=[];#On=[];constructor(t,e,i=!1,n=null){this.length=t;this.initialData=e;this.progressiveDone=i;this.contentDispositionFilename=n}addRangeListener(t){this.#On.push(t)}addProgressListener(t){this.#Ln.push(t)}addProgressiveReadListener(t){this.#Fn.push(t)}addProgressiveDoneListener(t){this.#Rn.push(t)}onDataRange(t,e){for(const i of this.#On)i(t,e)}onDataProgress(t,e){this.#sn.promise.then((()=>{for(const i of this.#Ln)i(t,e)}))}onDataProgressiveRead(t){this.#sn.promise.then((()=>{for(const e of this.#Fn)e(t)}))}onDataProgressiveDone(){this.#sn.promise.then((()=>{for(const t of this.#Rn)t()}))}transportReady(){this.#sn.resolve()}requestDataRange(t,e){unreachable("Abstract method PDFDataRangeTransport.requestDataRange")}abort(){}}class PDFDocumentProxy{constructor(t,e){this._pdfInfo=t;this._transport=e}get annotationStorage(){return this._transport.annotationStorage}get canvasFactory(){return this._transport.canvasFactory}get filterFactory(){return this._transport.filterFactory}get numPages(){return this._pdfInfo.numPages}get fingerprints(){return this._pdfInfo.fingerprints}get isPureXfa(){return shadow(this,"isPureXfa",!!this._transport._htmlForXfa)}get allXfaHtml(){return this._transport._htmlForXfa}getPage(t){return this._transport.getPage(t)}getPageIndex(t){return this._transport.getPageIndex(t)}getDestinations(){return this._transport.getDestinations()}getDestination(t){return this._transport.getDestination(t)}getPageLabels(){return this._transport.getPageLabels()}getPageLayout(){return this._transport.getPageLayout()}getPageMode(){return this._transport.getPageMode()}getViewerPreferences(){return this._transport.getViewerPreferences()}getOpenAction(){return this._transport.getOpenAction()}getAttachments(){return this._transport.getAttachments()}getJSActions(){return this._transport.getDocJSActions()}getOutline(){return this._transport.getOutline()}getOptionalContentConfig({intent:t="display"}={}){const{renderingIntent:e}=this._transport.getRenderingIntent(t);return this._transport.getOptionalContentConfig(e)}getPermissions(){return this._transport.getPermissions()}getMetadata(){return this._transport.getMetadata()}getMarkInfo(){return this._transport.getMarkInfo()}getData(){return this._transport.getData()}saveDocument(){return this._transport.saveDocument()}getDownloadInfo(){return this._transport.downloadInfoCapability.promise}cleanup(t=!1){return this._transport.startCleanup(t||this.isPureXfa)}destroy(){return this.loadingTask.destroy()}cachedPageNumber(t){return this._transport.cachedPageNumber(t)}get loadingParams(){return this._transport.loadingParams}get loadingTask(){return this._transport.loadingTask}getFieldObjects(){return this._transport.getFieldObjects()}hasJSActions(){return this._transport.hasJSActions()}getCalculationOrderIds(){return this._transport.getCalculationOrderIds()}}class PDFPageProxy{#Nn=!1;constructor(t,e,i,n=!1){this._pageIndex=t;this._pageInfo=e;this._transport=i;this._stats=n?new StatTimer:null;this._pdfBug=n;this.commonObjs=i.commonObjs;this.objs=new PDFObjects;this._intentStates=new Map;this.destroyed=!1}get pageNumber(){return this._pageIndex+1}get rotate(){return this._pageInfo.rotate}get ref(){return this._pageInfo.ref}get userUnit(){return this._pageInfo.userUnit}get view(){return this._pageInfo.view}getViewport({scale:t,rotation:e=this.rotate,offsetX:i=0,offsetY:n=0,dontFlip:s=!1}={}){return new PageViewport({viewBox:this.view,userUnit:this.userUnit,scale:t,rotation:e,offsetX:i,offsetY:n,dontFlip:s})}getAnnotations({intent:t="display"}={}){const{renderingIntent:e}=this._transport.getRenderingIntent(t);return this._transport.getAnnotations(this._pageIndex,e)}getJSActions(){return this._transport.getPageJSActions(this._pageIndex)}get filterFactory(){return this._transport.filterFactory}get isPureXfa(){return shadow(this,"isPureXfa",!!this._transport._htmlForXfa)}async getXfa(){return this._transport._htmlForXfa?.children[this._pageIndex]||null}render({canvasContext:t,canvas:e=t.canvas,viewport:i,intent:n="display",annotationMode:s=p.ENABLE,transform:r=null,background:a=null,optionalContentConfigPromise:l=null,annotationCanvasMap:h=null,pageColors:c=null,printAnnotationStorage:d=null,isEditing:u=!1}){this._stats?.time("Overall");const g=this._transport.getRenderingIntent(n,s,d,u),{renderingIntent:f,cacheKey:m}=g;this.#Nn=!1;l||=this._transport.getOptionalContentConfig(f);let b=this._intentStates.get(m);if(!b){b=Object.create(null);this._intentStates.set(m,b)}if(b.streamReaderCancelTimeout){clearTimeout(b.streamReaderCancelTimeout);b.streamReaderCancelTimeout=null}const v=!!(f&o);if(!b.displayReadyCapability){b.displayReadyCapability=Promise.withResolvers();b.operatorList={fnArray:[],argsArray:[],lastChunk:!1,separateAnnots:null};this._stats?.time("Page Request");this._pumpOperatorList(g)}const complete=t=>{b.renderTasks.delete(w);v&&(this.#Nn=!0);this.#Bn();if(t){w.capability.reject(t);this._abortOperatorList({intentState:b,reason:t instanceof Error?t:new Error(t)})}else w.capability.resolve();if(this._stats){this._stats.timeEnd("Rendering");this._stats.timeEnd("Overall");globalThis.Stats?.enabled&&globalThis.Stats.add(this.pageNumber,this._stats)}},w=new InternalRenderTask({callback:complete,params:{canvas:e,canvasContext:t,viewport:i,transform:r,background:a},objs:this.objs,commonObjs:this.commonObjs,annotationCanvasMap:h,operatorList:b.operatorList,pageIndex:this._pageIndex,canvasFactory:this._transport.canvasFactory,filterFactory:this._transport.filterFactory,useRequestAnimationFrame:!v,pdfBug:this._pdfBug,pageColors:c,enableHWA:this._transport.enableHWA});(b.renderTasks||=new Set).add(w);const y=w.task;Promise.all([b.displayReadyCapability.promise,l]).then((([t,e])=>{if(this.destroyed)complete();else{this._stats?.time("Rendering");if(!(e.renderingIntent&f))throw new Error("Must use the same `intent`-argument when calling the `PDFPageProxy.render` and `PDFDocumentProxy.getOptionalContentConfig` methods.");w.initializeGraphics({transparency:t,optionalContentConfig:e});w.operatorListChanged()}})).catch(complete);return y}getOperatorList({intent:t="display",annotationMode:e=p.ENABLE,printAnnotationStorage:i=null,isEditing:n=!1}={}){const s=this._transport.getRenderingIntent(t,e,i,n,!0);let r,a=this._intentStates.get(s.cacheKey);if(!a){a=Object.create(null);this._intentStates.set(s.cacheKey,a)}if(!a.opListReadCapability){r=Object.create(null);r.operatorListChanged=function operatorListChanged(){if(a.operatorList.lastChunk){a.opListReadCapability.resolve(a.operatorList);a.renderTasks.delete(r)}};a.opListReadCapability=Promise.withResolvers();(a.renderTasks||=new Set).add(r);a.operatorList={fnArray:[],argsArray:[],lastChunk:!1,separateAnnots:null};this._stats?.time("Page Request");this._pumpOperatorList(s)}return a.opListReadCapability.promise}streamTextContent({includeMarkedContent:t=!1,disableNormalization:e=!1}={}){return this._transport.messageHandler.sendWithStream("GetTextContent",{pageIndex:this._pageIndex,includeMarkedContent:!0===t,disableNormalization:!0===e},{highWaterMark:100,size:t=>t.items.length})}getTextContent(t={}){if(this._transport._htmlForXfa)return this.getXfa().then((t=>XfaText.textContent(t)));const e=this.streamTextContent(t);return new Promise((function(t,i){const n=e.getReader(),s={items:[],styles:Object.create(null),lang:null};!function pump(){n.read().then((function({value:e,done:i}){if(i)t(s);else{s.lang??=e.lang;Object.assign(s.styles,e.styles);s.items.push(...e.items);pump()}}),i)}()}))}getStructTree(){return this._transport.getStructTree(this._pageIndex)}_destroy(){this.destroyed=!0;const t=[];for(const e of this._intentStates.values()){this._abortOperatorList({intentState:e,reason:new Error("Page was destroyed."),force:!0});if(!e.opListReadCapability)for(const i of e.renderTasks){t.push(i.completed);i.cancel()}}this.objs.clear();this.#Nn=!1;return Promise.all(t)}cleanup(t=!1){this.#Nn=!0;const e=this.#Bn();t&&e&&(this._stats&&=new StatTimer);return e}#Bn(){if(!this.#Nn||this.destroyed)return!1;for(const{renderTasks:t,operatorList:e}of this._intentStates.values())if(t.size>0||!e.lastChunk)return!1;this._intentStates.clear();this.objs.clear();this.#Nn=!1;return!0}_startRenderPage(t,e){const i=this._intentStates.get(e);if(i){this._stats?.timeEnd("Page Request");i.displayReadyCapability?.resolve(t)}}_renderPageChunk(t,e){for(let i=0,n=t.length;i<n;i++){e.operatorList.fnArray.push(t.fnArray[i]);e.operatorList.argsArray.push(t.argsArray[i])}e.operatorList.lastChunk=t.lastChunk;e.operatorList.separateAnnots=t.separateAnnots;for(const t of e.renderTasks)t.operatorListChanged();t.lastChunk&&this.#Bn()}_pumpOperatorList({renderingIntent:t,cacheKey:e,annotationStorageSerializable:i,modifiedIds:n}){const{map:s,transfer:r}=i,a=this._transport.messageHandler.sendWithStream("GetOperatorList",{pageIndex:this._pageIndex,intent:t,cacheKey:e,annotationStorage:s,modifiedIds:n},r).getReader(),o=this._intentStates.get(e);o.streamReader=a;const pump=()=>{a.read().then((({value:t,done:e})=>{if(e)o.streamReader=null;else if(!this._transport.destroyed){this._renderPageChunk(t,o);pump()}}),(t=>{o.streamReader=null;if(!this._transport.destroyed){if(o.operatorList){o.operatorList.lastChunk=!0;for(const t of o.renderTasks)t.operatorListChanged();this.#Bn()}if(o.displayReadyCapability)o.displayReadyCapability.reject(t);else{if(!o.opListReadCapability)throw t;o.opListReadCapability.reject(t)}}}))};pump()}_abortOperatorList({intentState:t,reason:e,force:i=!1}){if(t.streamReader){if(t.streamReaderCancelTimeout){clearTimeout(t.streamReaderCancelTimeout);t.streamReaderCancelTimeout=null}if(!i){if(t.renderTasks.size>0)return;if(e instanceof RenderingCancelledException){let i=100;e.extraDelay>0&&e.extraDelay<1e3&&(i+=e.extraDelay);t.streamReaderCancelTimeout=setTimeout((()=>{t.streamReaderCancelTimeout=null;this._abortOperatorList({intentState:t,reason:e,force:!0})}),i);return}}t.streamReader.cancel(new AbortException(e.message)).catch((()=>{}));t.streamReader=null;if(!this._transport.destroyed){for(const[e,i]of this._intentStates)if(i===t){this._intentStates.delete(e);break}this.cleanup()}}}get stats(){return this._stats}}class PDFWorker{#sn=Promise.withResolvers();#Un=null;#Gi=null;#Hn=null;static#zn=0;static#jn=!1;static#Gn=new WeakMap;static{if(i){this.#jn=!0;GlobalWorkerOptions.workerSrc||="./pdf.worker.mjs"}this._isSameOrigin=(t,e)=>{const i=URL.parse(t);if(!i?.origin||"null"===i.origin)return!1;const n=new URL(e,i);return i.origin===n.origin};this._createCDNWrapper=t=>{const e=`await import("${t}");`;return URL.createObjectURL(new Blob([e],{type:"text/javascript"}))};this.fromPort=t=>{!function deprecated(t){console.log("Deprecated API usage: "+t)}("`PDFWorker.fromPort` - please use `PDFWorker.create` instead.");if(!t?.port)throw new Error("PDFWorker.fromPort - invalid method signature.");return this.create(t)}}constructor({name:t=null,port:e=null,verbosity:i=getVerbosityLevel()}={}){this.name=t;this.destroyed=!1;this.verbosity=i;if(e){if(PDFWorker.#Gn.has(e))throw new Error("Cannot use more than one PDFWorker per port.");PDFWorker.#Gn.set(e,this);this.#Wn(e)}else this.#Vn()}get promise(){return this.#sn.promise}#$n(){this.#sn.resolve();this.#Un.send("configure",{verbosity:this.verbosity})}get port(){return this.#Gi}get messageHandler(){return this.#Un}#Wn(t){this.#Gi=t;this.#Un=new MessageHandler("main","worker",t);this.#Un.on("ready",(()=>{}));this.#$n()}#Vn(){if(PDFWorker.#jn||PDFWorker.#qn){this.#Xn();return}let{workerSrc:t}=PDFWorker;try{PDFWorker._isSameOrigin(window.location,t)||(t=PDFWorker._createCDNWrapper(new URL(t,window.location).href));const e=new Worker(t,{type:"module"}),i=new MessageHandler("main","worker",e),terminateEarly=()=>{n.abort();i.destroy();e.terminate();this.destroyed?this.#sn.reject(new Error("Worker was destroyed")):this.#Xn()},n=new AbortController;e.addEventListener("error",(()=>{this.#Hn||terminateEarly()}),{signal:n.signal});i.on("test",(t=>{n.abort();if(!this.destroyed&&t){this.#Un=i;this.#Gi=e;this.#Hn=e;this.#$n()}else terminateEarly()}));i.on("ready",(t=>{n.abort();if(this.destroyed)terminateEarly();else try{sendTest()}catch{this.#Xn()}}));const sendTest=()=>{const t=new Uint8Array;i.send("test",t,[t.buffer])};sendTest();return}catch{info("The worker has been disabled.")}this.#Xn()}#Xn(){if(!PDFWorker.#jn){warn("Setting up fake worker.");PDFWorker.#jn=!0}PDFWorker._setupFakeWorkerGlobal.then((t=>{if(this.destroyed){this.#sn.reject(new Error("Worker was destroyed"));return}const e=new LoopbackPort;this.#Gi=e;const i="fake"+PDFWorker.#zn++,n=new MessageHandler(i+"_worker",i,e);t.setup(n,e);this.#Un=new MessageHandler(i,i+"_worker",e);this.#$n()})).catch((t=>{this.#sn.reject(new Error(`Setting up fake worker failed: "${t.message}".`))}))}destroy(){this.destroyed=!0;this.#Hn?.terminate();this.#Hn=null;PDFWorker.#Gn.delete(this.#Gi);this.#Gi=null;this.#Un?.destroy();this.#Un=null}static create(t){const e=this.#Gn.get(t?.port);if(e){if(e._pendingDestroy)throw new Error("PDFWorker.create - the worker is being destroyed.\nPlease remember to await `PDFDocumentLoadingTask.destroy()`-calls.");return e}return new PDFWorker(t)}static get workerSrc(){if(GlobalWorkerOptions.workerSrc)return GlobalWorkerOptions.workerSrc;throw new Error('No "GlobalWorkerOptions.workerSrc" specified.')}static get#qn(){try{return globalThis.pdfjsWorker?.WorkerMessageHandler||null}catch{return null}}static get _setupFakeWorkerGlobal(){return shadow(this,"_setupFakeWorkerGlobal",(async()=>{if(this.#qn)return this.#qn;return(await import(
/*webpackIgnore: true*/
/*@vite-ignore*/
this.workerSrc)).WorkerMessageHandler})())}}class WorkerTransport{#Kn=new Map;#Yn=new Map;#Qn=new Map;#Jn=new Map;#Zn=null;constructor(t,e,i,n,s,r){this.messageHandler=t;this.loadingTask=e;this.commonObjs=new PDFObjects;this.fontLoader=new FontLoader({ownerDocument:n.ownerDocument,styleElement:n.styleElement});this.loadingParams=n.loadingParams;this._params=n;this.canvasFactory=s.canvasFactory;this.filterFactory=s.filterFactory;this.cMapReaderFactory=s.cMapReaderFactory;this.standardFontDataFactory=s.standardFontDataFactory;this.wasmFactory=s.wasmFactory;this.destroyed=!1;this.destroyCapability=null;this._networkStream=i;this._fullReader=null;this._lastProgress=null;this.downloadInfoCapability=Promise.withResolvers();this.enableHWA=r;this.setupMessageHandler()}#ts(t,e=null){const i=this.#Kn.get(t);if(i)return i;const n=this.messageHandler.sendWithPromise(t,e);this.#Kn.set(t,n);return n}get annotationStorage(){return shadow(this,"annotationStorage",new AnnotationStorage)}getRenderingIntent(t,e=p.ENABLE,i=null,n=!1,s=!1){let g=a,f=Y;switch(t){case"any":g=r;break;case"display":break;case"print":g=o;break;default:warn(`getRenderingIntent - invalid intent: ${t}`)}const m=g&o&&i instanceof PrintAnnotationStorage?i:this.annotationStorage;switch(e){case p.DISABLE:g+=c;break;case p.ENABLE:break;case p.ENABLE_FORMS:g+=l;break;case p.ENABLE_STORAGE:g+=h;f=m.serializable;break;default:warn(`getRenderingIntent - invalid annotationMode: ${e}`)}n&&(g+=d);s&&(g+=u);const{ids:b,hash:v}=m.modifiedIds;return{renderingIntent:g,cacheKey:[g,f.hash,v].join("_"),annotationStorageSerializable:f,modifiedIds:b}}destroy(){if(this.destroyCapability)return this.destroyCapability.promise;this.destroyed=!0;this.destroyCapability=Promise.withResolvers();this.#Zn?.reject(new Error("Worker was destroyed during onPassword callback"));const t=[];for(const e of this.#Yn.values())t.push(e._destroy());this.#Yn.clear();this.#Qn.clear();this.#Jn.clear();this.hasOwnProperty("annotationStorage")&&this.annotationStorage.resetModified();const e=this.messageHandler.sendWithPromise("Terminate",null);t.push(e);Promise.all(t).then((()=>{this.commonObjs.clear();this.fontLoader.clear();this.#Kn.clear();this.filterFactory.destroy();TextLayer.cleanup();this._networkStream?.cancelAllRequests(new AbortException("Worker was terminated."));this.messageHandler?.destroy();this.messageHandler=null;this.destroyCapability.resolve()}),this.destroyCapability.reject);return this.destroyCapability.promise}setupMessageHandler(){const{messageHandler:t,loadingTask:e}=this;t.on("GetReader",((t,e)=>{assert(this._networkStream,"GetReader - no `IPDFStream` instance available.");this._fullReader=this._networkStream.getFullReader();this._fullReader.onProgress=t=>{this._lastProgress={loaded:t.loaded,total:t.total}};e.onPull=()=>{this._fullReader.read().then((function({value:t,done:i}){if(i)e.close();else{assert(t instanceof ArrayBuffer,"GetReader - expected an ArrayBuffer.");e.enqueue(new Uint8Array(t),1,[t])}})).catch((t=>{e.error(t)}))};e.onCancel=t=>{this._fullReader.cancel(t);e.ready.catch((t=>{if(!this.destroyed)throw t}))}}));t.on("ReaderHeadersReady",(async t=>{await this._fullReader.headersReady;const{isStreamingSupported:i,isRangeSupported:n,contentLength:s}=this._fullReader;if(!i||!n){this._lastProgress&&e.onProgress?.(this._lastProgress);this._fullReader.onProgress=t=>{e.onProgress?.({loaded:t.loaded,total:t.total})}}return{isStreamingSupported:i,isRangeSupported:n,contentLength:s}}));t.on("GetRangeReader",((t,e)=>{assert(this._networkStream,"GetRangeReader - no `IPDFStream` instance available.");const i=this._networkStream.getRangeReader(t.begin,t.end);if(i){e.onPull=()=>{i.read().then((function({value:t,done:i}){if(i)e.close();else{assert(t instanceof ArrayBuffer,"GetRangeReader - expected an ArrayBuffer.");e.enqueue(new Uint8Array(t),1,[t])}})).catch((t=>{e.error(t)}))};e.onCancel=t=>{i.cancel(t);e.ready.catch((t=>{if(!this.destroyed)throw t}))}}else e.close()}));t.on("GetDoc",(({pdfInfo:t})=>{this._numPages=t.numPages;this._htmlForXfa=t.htmlForXfa;delete t.htmlForXfa;e._capability.resolve(new PDFDocumentProxy(t,this))}));t.on("DocException",(t=>{e._capability.reject(wrapReason(t))}));t.on("PasswordRequest",(t=>{this.#Zn=Promise.withResolvers();try{if(!e.onPassword)throw wrapReason(t);const updatePassword=t=>{t instanceof Error?this.#Zn.reject(t):this.#Zn.resolve({password:t})};e.onPassword(updatePassword,t.code)}catch(t){this.#Zn.reject(t)}return this.#Zn.promise}));t.on("DataLoaded",(t=>{e.onProgress?.({loaded:t.length,total:t.length});this.downloadInfoCapability.resolve(t)}));t.on("StartRenderPage",(t=>{if(this.destroyed)return;this.#Yn.get(t.pageIndex)._startRenderPage(t.transparency,t.cacheKey)}));t.on("commonobj",(([e,i,n])=>{if(this.destroyed)return null;if(this.commonObjs.has(e))return null;switch(i){case"Font":if("error"in n){const t=n.error;warn(`Error during font loading: ${t}`);this.commonObjs.resolve(e,t);break}const s=this._params.pdfBug&&globalThis.FontInspector?.enabled?(t,e)=>globalThis.FontInspector.fontAdded(t,e):null,r=new FontFaceObject(n,s);this.fontLoader.bind(r).catch((()=>t.sendWithPromise("FontFallback",{id:e}))).finally((()=>{!r.fontExtraProperties&&r.data&&(r.data=null);this.commonObjs.resolve(e,r)}));break;case"CopyLocalImage":const{imageRef:a}=n;assert(a,"The imageRef must be defined.");for(const t of this.#Yn.values())for(const[,i]of t.objs)if(i?.ref===a){if(!i.dataLen)return null;this.commonObjs.resolve(e,structuredClone(i));return i.dataLen}break;case"FontPath":case"Image":case"Pattern":this.commonObjs.resolve(e,n);break;default:throw new Error(`Got unknown common object type ${i}`)}return null}));t.on("obj",(([t,e,i,n])=>{if(this.destroyed)return;const s=this.#Yn.get(e);if(!s.objs.has(t))if(0!==s._intentStates.size)switch(i){case"Image":case"Pattern":s.objs.resolve(t,n);break;default:throw new Error(`Got unknown object type ${i}`)}else n?.bitmap?.close()}));t.on("DocProgress",(t=>{this.destroyed||e.onProgress?.({loaded:t.loaded,total:t.total})}));t.on("FetchBinaryData",(async t=>{if(this.destroyed)throw new Error("Worker was destroyed.");const e=this[t.type];if(!e)throw new Error(`${t.type} not initialized, see the \`useWorkerFetch\` parameter.`);return e.fetch(t)}))}getData(){return this.messageHandler.sendWithPromise("GetData",null)}saveDocument(){this.annotationStorage.size<=0&&warn("saveDocument called while `annotationStorage` is empty, please use the getData-method instead.");const{map:t,transfer:e}=this.annotationStorage.serializable;return this.messageHandler.sendWithPromise("SaveDocument",{isPureXfa:!!this._htmlForXfa,numPages:this._numPages,annotationStorage:t,filename:this._fullReader?.filename??null},e).finally((()=>{this.annotationStorage.resetModified()}))}getPage(t){if(!Number.isInteger(t)||t<=0||t>this._numPages)return Promise.reject(new Error("Invalid page request."));const e=t-1,i=this.#Qn.get(e);if(i)return i;const n=this.messageHandler.sendWithPromise("GetPage",{pageIndex:e}).then((i=>{if(this.destroyed)throw new Error("Transport destroyed");i.refStr&&this.#Jn.set(i.refStr,t);const n=new PDFPageProxy(e,i,this,this._params.pdfBug);this.#Yn.set(e,n);return n}));this.#Qn.set(e,n);return n}getPageIndex(t){return isRefProxy(t)?this.messageHandler.sendWithPromise("GetPageIndex",{num:t.num,gen:t.gen}):Promise.reject(new Error("Invalid pageIndex request."))}getAnnotations(t,e){return this.messageHandler.sendWithPromise("GetAnnotations",{pageIndex:t,intent:e})}getFieldObjects(){return this.#ts("GetFieldObjects")}hasJSActions(){return this.#ts("HasJSActions")}getCalculationOrderIds(){return this.messageHandler.sendWithPromise("GetCalculationOrderIds",null)}getDestinations(){return this.messageHandler.sendWithPromise("GetDestinations",null)}getDestination(t){return"string"!=typeof t?Promise.reject(new Error("Invalid destination request.")):this.messageHandler.sendWithPromise("GetDestination",{id:t})}getPageLabels(){return this.messageHandler.sendWithPromise("GetPageLabels",null)}getPageLayout(){return this.messageHandler.sendWithPromise("GetPageLayout",null)}getPageMode(){return this.messageHandler.sendWithPromise("GetPageMode",null)}getViewerPreferences(){return this.messageHandler.sendWithPromise("GetViewerPreferences",null)}getOpenAction(){return this.messageHandler.sendWithPromise("GetOpenAction",null)}getAttachments(){return this.messageHandler.sendWithPromise("GetAttachments",null)}getDocJSActions(){return this.#ts("GetDocJSActions")}getPageJSActions(t){return this.messageHandler.sendWithPromise("GetPageJSActions",{pageIndex:t})}getStructTree(t){return this.messageHandler.sendWithPromise("GetStructTree",{pageIndex:t})}getOutline(){return this.messageHandler.sendWithPromise("GetOutline",null)}getOptionalContentConfig(t){return this.#ts("GetOptionalContentConfig").then((e=>new OptionalContentConfig(e,t)))}getPermissions(){return this.messageHandler.sendWithPromise("GetPermissions",null)}getMetadata(){const t="GetMetadata",e=this.#Kn.get(t);if(e)return e;const i=this.messageHandler.sendWithPromise(t,null).then((t=>({info:t[0],metadata:t[1]?new Metadata(t[1]):null,contentDispositionFilename:this._fullReader?.filename??null,contentLength:this._fullReader?.contentLength??null})));this.#Kn.set(t,i);return i}getMarkInfo(){return this.messageHandler.sendWithPromise("GetMarkInfo",null)}async startCleanup(t=!1){if(!this.destroyed){await this.messageHandler.sendWithPromise("Cleanup",null);for(const t of this.#Yn.values()){if(!t.cleanup())throw new Error(`startCleanup: Page ${t.pageNumber} is currently rendering.`)}this.commonObjs.clear();t||this.fontLoader.clear();this.#Kn.clear();this.filterFactory.destroy(!0);TextLayer.cleanup()}}cachedPageNumber(t){if(!isRefProxy(t))return null;const e=0===t.gen?`${t.num}R`:`${t.num}R${t.gen}`;return this.#Jn.get(e)??null}}class RenderTask{#es=null;onContinue=null;onError=null;constructor(t){this.#es=t}get promise(){return this.#es.capability.promise}cancel(t=0){this.#es.cancel(null,t)}get separateAnnots(){const{separateAnnots:t}=this.#es.operatorList;if(!t)return!1;const{annotationCanvasMap:e}=this.#es;return t.form||t.canvas&&e?.size>0}}class InternalRenderTask{#is=null;static#ns=new WeakSet;constructor({callback:t,params:e,objs:i,commonObjs:n,annotationCanvasMap:s,operatorList:r,pageIndex:a,canvasFactory:o,filterFactory:l,useRequestAnimationFrame:h=!1,pdfBug:c=!1,pageColors:d=null,enableHWA:u=!1}){this.callback=t;this.params=e;this.objs=i;this.commonObjs=n;this.annotationCanvasMap=s;this.operatorListIdx=null;this.operatorList=r;this._pageIndex=a;this.canvasFactory=o;this.filterFactory=l;this._pdfBug=c;this.pageColors=d;this.running=!1;this.graphicsReadyCallback=null;this.graphicsReady=!1;this._useRequestAnimationFrame=!0===h&&"undefined"!=typeof window;this.cancelled=!1;this.capability=Promise.withResolvers();this.task=new RenderTask(this);this._cancelBound=this.cancel.bind(this);this._continueBound=this._continue.bind(this);this._scheduleNextBound=this._scheduleNext.bind(this);this._nextBound=this._next.bind(this);this._canvas=e.canvas;this._canvasContext=e.canvas?null:e.canvasContext;this._enableHWA=u}get completed(){return this.capability.promise.catch((function(){}))}initializeGraphics({transparency:t=!1,optionalContentConfig:e}){if(this.cancelled)return;if(this._canvas){if(InternalRenderTask.#ns.has(this._canvas))throw new Error("Cannot use the same canvas during multiple render() operations. Use different canvas or ensure previous operations were cancelled or completed.");InternalRenderTask.#ns.add(this._canvas)}if(this._pdfBug&&globalThis.StepperManager?.enabled){this.stepper=globalThis.StepperManager.create(this._pageIndex);this.stepper.init(this.operatorList);this.stepper.nextBreakPoint=this.stepper.getNextBreakPoint()}const{viewport:i,transform:n,background:s}=this.params,r=this._canvasContext||this._canvas.getContext("2d",{alpha:!1,willReadFrequently:!this._enableHWA});this.gfx=new CanvasGraphics(r,this.commonObjs,this.objs,this.canvasFactory,this.filterFactory,{optionalContentConfig:e},this.annotationCanvasMap,this.pageColors);this.gfx.beginDrawing({transform:n,viewport:i,transparency:t,background:s});this.operatorListIdx=0;this.graphicsReady=!0;this.graphicsReadyCallback?.()}cancel(t=null,e=0){this.running=!1;this.cancelled=!0;this.gfx?.endDrawing();if(this.#is){window.cancelAnimationFrame(this.#is);this.#is=null}InternalRenderTask.#ns.delete(this._canvas);t||=new RenderingCancelledException(`Rendering cancelled, page ${this._pageIndex+1}`,e);this.callback(t);this.task.onError?.(t)}operatorListChanged(){if(this.graphicsReady){this.stepper?.updateOperatorList(this.operatorList);this.running||this._continue()}else this.graphicsReadyCallback||=this._continueBound}_continue(){this.running=!0;this.cancelled||(this.task.onContinue?this.task.onContinue(this._scheduleNextBound):this._scheduleNext())}_scheduleNext(){this._useRequestAnimationFrame?this.#is=window.requestAnimationFrame((()=>{this.#is=null;this._nextBound().catch(this._cancelBound)})):Promise.resolve().then(this._nextBound).catch(this._cancelBound)}async _next(){if(!this.cancelled){this.operatorListIdx=this.gfx.executeOperatorList(this.operatorList,this.operatorListIdx,this._continueBound,this.stepper);if(this.operatorListIdx===this.operatorList.argsArray.length){this.running=!1;if(this.operatorList.lastChunk){this.gfx.endDrawing();InternalRenderTask.#ns.delete(this._canvas);this.callback()}}}}}const St="5.4.54",Ct="295fb3ec4";class ColorPicker{#ss=null;#rs=null;#as;#os=null;#ls=!1;#hs=!1;#r=null;#cs;#ds=null;#m=null;static#us=null;static get _keyboardManager(){return shadow(this,"_keyboardManager",new KeyboardManager([[["Escape","mac+Escape"],ColorPicker.prototype._hideDropdownFromKeyboard],[[" ","mac+ "],ColorPicker.prototype._colorSelectFromKeyboard],[["ArrowDown","ArrowRight","mac+ArrowDown","mac+ArrowRight"],ColorPicker.prototype._moveToNext],[["ArrowUp","ArrowLeft","mac+ArrowUp","mac+ArrowLeft"],ColorPicker.prototype._moveToPrevious],[["Home","mac+Home"],ColorPicker.prototype._moveToBeginning],[["End","mac+End"],ColorPicker.prototype._moveToEnd]]))}constructor({editor:t=null,uiManager:e=null}){if(t){this.#hs=!1;this.#r=t}else this.#hs=!0;this.#m=t?._uiManager||e;this.#cs=this.#m._eventBus;this.#as=t?.color?.toUpperCase()||this.#m?.highlightColors.values().next().value||"#FFFF98";ColorPicker.#us||=Object.freeze({blue:"pdfjs-editor-colorpicker-blue",green:"pdfjs-editor-colorpicker-green",pink:"pdfjs-editor-colorpicker-pink",red:"pdfjs-editor-colorpicker-red",yellow:"pdfjs-editor-colorpicker-yellow"})}renderButton(){const t=this.#ss=document.createElement("button");t.className="colorPicker";t.tabIndex="0";t.setAttribute("data-l10n-id","pdfjs-editor-colorpicker-button");t.ariaHasPopup="true";this.#r&&(t.ariaControls=`${this.#r.id}_colorpicker_dropdown`);const e=this.#m._signal;t.addEventListener("click",this.#ps.bind(this),{signal:e});t.addEventListener("keydown",this.#gs.bind(this),{signal:e});const i=this.#rs=document.createElement("span");i.className="swatch";i.ariaHidden="true";i.style.backgroundColor=this.#as;t.append(i);return t}renderMainDropdown(){const t=this.#os=this.#fs();t.ariaOrientation="horizontal";t.ariaLabelledBy="highlightColorPickerLabel";return t}#fs(){const t=document.createElement("div"),e=this.#m._signal;t.addEventListener("contextmenu",noContextMenu,{signal:e});t.className="dropdown";t.role="listbox";t.ariaMultiSelectable="false";t.ariaOrientation="vertical";t.setAttribute("data-l10n-id","pdfjs-editor-colorpicker-dropdown");this.#r&&(t.id=`${this.#r.id}_colorpicker_dropdown`);for(const[i,n]of this.#m.highlightColors){const s=document.createElement("button");s.tabIndex="0";s.role="option";s.setAttribute("data-color",n);s.title=i;s.setAttribute("data-l10n-id",ColorPicker.#us[i]);const r=document.createElement("span");s.append(r);r.className="swatch";r.style.backgroundColor=n;s.ariaSelected=n===this.#as;s.addEventListener("click",this.#ms.bind(this,n),{signal:e});t.append(s)}t.addEventListener("keydown",this.#gs.bind(this),{signal:e});return t}#ms(t,e){e.stopPropagation();this.#cs.dispatch("switchannotationeditorparams",{source:this,type:m.HIGHLIGHT_COLOR,value:t});this.updateColor(t)}_colorSelectFromKeyboard(t){if(t.target===this.#ss){this.#ps(t);return}const e=t.target.getAttribute("data-color");e&&this.#ms(e,t)}_moveToNext(t){this.#bs?t.target!==this.#ss?t.target.nextSibling?.focus():this.#os.firstChild?.focus():this.#ps(t)}_moveToPrevious(t){if(t.target!==this.#os?.firstChild&&t.target!==this.#ss){this.#bs||this.#ps(t);t.target.previousSibling?.focus()}else this.#bs&&this._hideDropdownFromKeyboard()}_moveToBeginning(t){this.#bs?this.#os.firstChild?.focus():this.#ps(t)}_moveToEnd(t){this.#bs?this.#os.lastChild?.focus():this.#ps(t)}#gs(t){ColorPicker._keyboardManager.exec(this,t)}#ps(t){if(this.#bs){this.hideDropdown();return}this.#ls=0===t.detail;if(!this.#ds){this.#ds=new AbortController;window.addEventListener("pointerdown",this.#d.bind(this),{signal:this.#m.combinedSignal(this.#ds)})}this.#ss.ariaExpanded="true";if(this.#os){this.#os.classList.remove("hidden");return}const e=this.#os=this.#fs();this.#ss.append(e)}#d(t){this.#os?.contains(t.target)||this.hideDropdown()}hideDropdown(){this.#os?.classList.add("hidden");this.#ss.ariaExpanded="false";this.#ds?.abort();this.#ds=null}get#bs(){return this.#os&&!this.#os.classList.contains("hidden")}_hideDropdownFromKeyboard(){if(!this.#hs)if(this.#bs){this.hideDropdown();this.#ss.focus({preventScroll:!0,focusVisible:this.#ls})}else this.#r?.unselect()}updateColor(t){this.#rs&&(this.#rs.style.backgroundColor=t);if(!this.#os)return;const e=this.#m.highlightColors.values();for(const i of this.#os.children)i.ariaSelected=e.next().value===t.toUpperCase()}destroy(){this.#ss?.remove();this.#ss=null;this.#rs=null;this.#os?.remove();this.#os=null}}class BasicColorPicker{#vs=null;#r=null;#m=null;static#us=null;constructor(t){this.#r=t;this.#m=t._uiManager;BasicColorPicker.#us||=Object.freeze({freetext:"pdfjs-editor-color-picker-free-text-input",ink:"pdfjs-editor-color-picker-ink-input"})}renderButton(){if(this.#vs)return this.#vs;const{editorType:t,colorType:e,colorValue:i}=this.#r,n=this.#vs=document.createElement("input");n.type="color";n.value=i||"#000000";n.className="basicColorPicker";n.tabIndex=0;n.setAttribute("data-l10n-id",BasicColorPicker.#us[t]);n.addEventListener("input",(()=>{this.#m.updateParams(e,n.value)}),{signal:this.#m._signal});return n}update(t){this.#vs&&(this.#vs.value=t)}destroy(){this.#vs?.remove();this.#vs=null}hideDropdown(){}}__webpack_require__(531);function makeColorComp(t){return Math.floor(255*Math.max(0,Math.min(1,t))).toString(16).padStart(2,"0")}function scaleAndClamp(t){return Math.max(0,Math.min(255,255*t))}class ColorConverters{static CMYK_G([t,e,i,n]){return["G",1-Math.min(1,.3*t+.59*i+.11*e+n)]}static G_CMYK([t]){return["CMYK",0,0,0,1-t]}static G_RGB([t]){return["RGB",t,t,t]}static G_rgb([t]){return[t=scaleAndClamp(t),t,t]}static G_HTML([t]){const e=makeColorComp(t);return`#${e}${e}${e}`}static RGB_G([t,e,i]){return["G",.3*t+.59*e+.11*i]}static RGB_rgb(t){return t.map(scaleAndClamp)}static RGB_HTML(t){return`#${t.map(makeColorComp).join("")}`}static T_HTML(){return"#00000000"}static T_rgb(){return[null]}static CMYK_RGB([t,e,i,n]){return["RGB",1-Math.min(1,t+n),1-Math.min(1,i+n),1-Math.min(1,e+n)]}static CMYK_rgb([t,e,i,n]){return[scaleAndClamp(1-Math.min(1,t+n)),scaleAndClamp(1-Math.min(1,i+n)),scaleAndClamp(1-Math.min(1,e+n))]}static CMYK_HTML(t){const e=this.CMYK_RGB(t).slice(1);return this.RGB_HTML(e)}static RGB_CMYK([t,e,i]){const n=1-t,s=1-e,r=1-i;return["CMYK",n,s,r,Math.min(n,s,r)]}}class BaseSVGFactory{create(t,e,i=!1){if(t<=0||e<=0)throw new Error("Invalid SVG dimensions");const n=this._createSVG("svg:svg");n.setAttribute("version","1.1");if(!i){n.setAttribute("width",`${t}px`);n.setAttribute("height",`${e}px`)}n.setAttribute("preserveAspectRatio","none");n.setAttribute("viewBox",`0 0 ${t} ${e}`);return n}createElement(t){if("string"!=typeof t)throw new Error("Invalid SVG element type");return this._createSVG(t)}_createSVG(t){unreachable("Abstract method `_createSVG` called.")}}class DOMSVGFactory extends BaseSVGFactory{_createSVG(t){return document.createElementNS(V,t)}}class XfaLayer{static setupStorage(t,e,i,n,s){const r=n.getValue(e,{value:null});switch(i.name){case"textarea":null!==r.value&&(t.textContent=r.value);if("print"===s)break;t.addEventListener("input",(t=>{n.setValue(e,{value:t.target.value})}));break;case"input":if("radio"===i.attributes.type||"checkbox"===i.attributes.type){r.value===i.attributes.xfaOn?t.setAttribute("checked",!0):r.value===i.attributes.xfaOff&&t.removeAttribute("checked");if("print"===s)break;t.addEventListener("change",(t=>{n.setValue(e,{value:t.target.checked?t.target.getAttribute("xfaOn"):t.target.getAttribute("xfaOff")})}))}else{null!==r.value&&t.setAttribute("value",r.value);if("print"===s)break;t.addEventListener("input",(t=>{n.setValue(e,{value:t.target.value})}))}break;case"select":if(null!==r.value){t.setAttribute("value",r.value);for(const t of i.children)t.attributes.value===r.value?t.attributes.selected=!0:t.attributes.hasOwnProperty("selected")&&delete t.attributes.selected}t.addEventListener("input",(t=>{const i=t.target.options,s=-1===i.selectedIndex?"":i[i.selectedIndex].value;n.setValue(e,{value:s})}))}}static setAttributes({html:t,element:e,storage:i=null,intent:n,linkService:s}){const{attributes:r}=e,a=t instanceof HTMLAnchorElement;"radio"===r.type&&(r.name=`${r.name}-${n}`);for(const[e,i]of Object.entries(r))if(null!=i)switch(e){case"class":i.length&&t.setAttribute(e,i.join(" "));break;case"dataId":break;case"id":t.setAttribute("data-element-id",i);break;case"style":Object.assign(t.style,i);break;case"textContent":t.textContent=i;break;default:(!a||"href"!==e&&"newWindow"!==e)&&t.setAttribute(e,i)}a&&s.addLinkAttributes(t,r.href,r.newWindow);i&&r.dataId&&this.setupStorage(t,r.dataId,e,i)}static render(t){const e=t.annotationStorage,i=t.linkService,n=t.xfaHtml,s=t.intent||"display",r=document.createElement(n.name);n.attributes&&this.setAttributes({html:r,element:n,intent:s,linkService:i});const a="richText"!==s,o=t.div;o.append(r);if(t.viewport){const e=`matrix(${t.viewport.transform.join(",")})`;o.style.transform=e}a&&o.setAttribute("class","xfaLayer xfaFont");const l=[];if(0===n.children.length){if(n.value){const t=document.createTextNode(n.value);r.append(t);a&&XfaText.shouldBuildText(n.name)&&l.push(t)}return{textDivs:l}}const h=[[n,-1,r]];for(;h.length>0;){const[t,n,r]=h.at(-1);if(n+1===t.children.length){h.pop();continue}const o=t.children[++h.at(-1)[1]];if(null===o)continue;const{name:c}=o;if("#text"===c){const t=document.createTextNode(o.value);l.push(t);r.append(t);continue}const d=o?.attributes?.xmlns?document.createElementNS(o.attributes.xmlns,c):document.createElement(c);r.append(d);o.attributes&&this.setAttributes({html:d,element:o,storage:e,intent:s,linkService:i});if(o.children?.length>0)h.push([o,-1,d]);else if(o.value){const t=document.createTextNode(o.value);a&&XfaText.shouldBuildText(c)&&l.push(t);d.append(t)}}for(const t of o.querySelectorAll(".xfaNonInteractive input, .xfaNonInteractive textarea"))t.setAttribute("readOnly",!0);return{textDivs:l}}static update(t){const e=`matrix(${t.viewport.transform.join(",")})`;t.div.style.transform=e;t.div.hidden=!1}}const Tt=new WeakSet,Mt=60*(new Date).getTimezoneOffset()*1e3;class AnnotationElementFactory{static create(t){switch(t.data.annotationType){case S.LINK:return new LinkAnnotationElement(t);case S.TEXT:return new TextAnnotationElement(t);case S.WIDGET:switch(t.data.fieldType){case"Tx":return new TextWidgetAnnotationElement(t);case"Btn":return t.data.radioButton?new RadioButtonWidgetAnnotationElement(t):t.data.checkBox?new CheckboxWidgetAnnotationElement(t):new PushButtonWidgetAnnotationElement(t);case"Ch":return new ChoiceWidgetAnnotationElement(t);case"Sig":return new SignatureWidgetAnnotationElement(t)}return new WidgetAnnotationElement(t);case S.POPUP:return new PopupAnnotationElement(t);case S.FREETEXT:return new FreeTextAnnotationElement(t);case S.LINE:return new LineAnnotationElement(t);case S.SQUARE:return new SquareAnnotationElement(t);case S.CIRCLE:return new CircleAnnotationElement(t);case S.POLYLINE:return new PolylineAnnotationElement(t);case S.CARET:return new CaretAnnotationElement(t);case S.INK:return new InkAnnotationElement(t);case S.POLYGON:return new PolygonAnnotationElement(t);case S.HIGHLIGHT:return new HighlightAnnotationElement(t);case S.UNDERLINE:return new UnderlineAnnotationElement(t);case S.SQUIGGLY:return new SquigglyAnnotationElement(t);case S.STRIKEOUT:return new StrikeOutAnnotationElement(t);case S.STAMP:return new StampAnnotationElement(t);case S.FILEATTACHMENT:return new FileAttachmentAnnotationElement(t);default:return new AnnotationElement(t)}}}class AnnotationElement{#ws=null;#ys=!1;#As=null;constructor(t,{isRenderable:e=!1,ignoreBorder:i=!1,createQuadrilaterals:n=!1}={}){this.isRenderable=e;this.data=t.data;this.layer=t.layer;this.linkService=t.linkService;this.downloadManager=t.downloadManager;this.imageResourcesPath=t.imageResourcesPath;this.renderForms=t.renderForms;this.svgFactory=t.svgFactory;this.annotationStorage=t.annotationStorage;this.enableScripting=t.enableScripting;this.hasJSActions=t.hasJSActions;this._fieldObjects=t.fieldObjects;this.parent=t.parent;e&&(this.container=this._createContainer(i));n&&this._createQuadrilaterals()}static _hasPopupData({contentsObj:t,richText:e}){return!(!t?.str&&!e?.str)}get _isEditable(){return this.data.isEditable}get hasPopupData(){return AnnotationElement._hasPopupData(this.data)}updateEdited(t){if(!this.container)return;t.rect&&(this.#ws||={rect:this.data.rect.slice(0)});const{rect:e,popup:i}=t;e&&this.#xs(e);let n=this.#As?.popup||this.popup;if(!n&&i?.text){this._createPopup(i);n=this.#As.popup}if(n){n.updateEdited(t);if(i?.deleted){n.remove();this.#As=null;this.popup=null}}}resetEdited(){if(this.#ws){this.#xs(this.#ws.rect);this.#As?.popup.resetEdited();this.#ws=null}}#xs(t){const{container:{style:e},data:{rect:i,rotation:n},parent:{viewport:{rawDims:{pageWidth:s,pageHeight:r,pageX:a,pageY:o}}}}=this;i?.splice(0,4,...t);e.left=100*(t[0]-a)/s+"%";e.top=100*(r-t[3]+o)/r+"%";if(0===n){e.width=100*(t[2]-t[0])/s+"%";e.height=100*(t[3]-t[1])/r+"%"}else this.setRotation(n)}_createContainer(t){const{data:e,parent:{page:i,viewport:n}}=this,s=document.createElement("section");s.setAttribute("data-annotation-id",e.id);this instanceof WidgetAnnotationElement||this instanceof LinkAnnotationElement||(s.tabIndex=0);const{style:r}=s;r.zIndex=this.parent.zIndex++;e.alternativeText&&(s.title=e.alternativeText);e.noRotate&&s.classList.add("norotate");if(!e.rect||this instanceof PopupAnnotationElement){const{rotation:t}=e;e.hasOwnCanvas||0===t||this.setRotation(t,s);return s}const{width:a,height:o}=this;if(!t&&e.borderStyle.width>0){r.borderWidth=`${e.borderStyle.width}px`;const t=e.borderStyle.horizontalCornerRadius,i=e.borderStyle.verticalCornerRadius;if(t>0||i>0){const e=`calc(${t}px * var(--total-scale-factor)) / calc(${i}px * var(--total-scale-factor))`;r.borderRadius=e}else if(this instanceof RadioButtonWidgetAnnotationElement){const t=`calc(${a}px * var(--total-scale-factor)) / calc(${o}px * var(--total-scale-factor))`;r.borderRadius=t}switch(e.borderStyle.style){case C:r.borderStyle="solid";break;case T:r.borderStyle="dashed";break;case M:warn("Unimplemented border style: beveled");break;case D:warn("Unimplemented border style: inset");break;case P:r.borderBottomStyle="solid"}const n=e.borderColor||null;if(n){this.#ys=!0;r.borderColor=Util.makeHexColor(0|n[0],0|n[1],0|n[2])}else r.borderWidth=0}const l=Util.normalizeRect([e.rect[0],i.view[3]-e.rect[1]+i.view[1],e.rect[2],i.view[3]-e.rect[3]+i.view[1]]),{pageWidth:h,pageHeight:c,pageX:d,pageY:u}=n.rawDims;r.left=100*(l[0]-d)/h+"%";r.top=100*(l[1]-u)/c+"%";const{rotation:p}=e;if(e.hasOwnCanvas||0===p){r.width=100*a/h+"%";r.height=100*o/c+"%"}else this.setRotation(p,s);return s}setRotation(t,e=this.container){if(!this.data.rect)return;const{pageWidth:i,pageHeight:n}=this.parent.viewport.rawDims;let{width:s,height:r}=this;t%180!=0&&([s,r]=[r,s]);e.style.width=100*s/i+"%";e.style.height=100*r/n+"%";e.setAttribute("data-main-rotation",(360-t)%360)}get _commonActions(){const setColor=(t,e,i)=>{const n=i.detail[t],s=n[0],r=n.slice(1);i.target.style[e]=ColorConverters[`${s}_HTML`](r);this.annotationStorage.setValue(this.data.id,{[e]:ColorConverters[`${s}_rgb`](r)})};return shadow(this,"_commonActions",{display:t=>{const{display:e}=t.detail,i=e%2==1;this.container.style.visibility=i?"hidden":"visible";this.annotationStorage.setValue(this.data.id,{noView:i,noPrint:1===e||2===e})},print:t=>{this.annotationStorage.setValue(this.data.id,{noPrint:!t.detail.print})},hidden:t=>{const{hidden:e}=t.detail;this.container.style.visibility=e?"hidden":"visible";this.annotationStorage.setValue(this.data.id,{noPrint:e,noView:e})},focus:t=>{setTimeout((()=>t.target.focus({preventScroll:!1})),0)},userName:t=>{t.target.title=t.detail.userName},readonly:t=>{t.target.disabled=t.detail.readonly},required:t=>{this._setRequired(t.target,t.detail.required)},bgColor:t=>{setColor("bgColor","backgroundColor",t)},fillColor:t=>{setColor("fillColor","backgroundColor",t)},fgColor:t=>{setColor("fgColor","color",t)},textColor:t=>{setColor("textColor","color",t)},borderColor:t=>{setColor("borderColor","borderColor",t)},strokeColor:t=>{setColor("strokeColor","borderColor",t)},rotation:t=>{const e=t.detail.rotation;this.setRotation(e);this.annotationStorage.setValue(this.data.id,{rotation:e})}})}_dispatchEventFromSandbox(t,e){const i=this._commonActions;for(const n of Object.keys(e.detail)){const s=t[n]||i[n];s?.(e)}}_setDefaultPropertiesFromJS(t){if(!this.enableScripting)return;const e=this.annotationStorage.getRawValue(this.data.id);if(!e)return;const i=this._commonActions;for(const[n,s]of Object.entries(e)){const r=i[n];if(r){r({detail:{[n]:s},target:t});delete e[n]}}}_createQuadrilaterals(){if(!this.container)return;const{quadPoints:t}=this.data;if(!t)return;const[e,i,n,s]=this.data.rect.map((t=>Math.fround(t)));if(8===t.length){const[r,a,o,l]=t.subarray(2,6);if(n===r&&s===a&&e===o&&i===l)return}const{style:r}=this.container;let a;if(this.#ys){const{borderColor:t,borderWidth:e}=r;r.borderWidth=0;a=["url('data:image/svg+xml;utf8,",'<svg xmlns="http://www.w3.org/2000/svg"',' preserveAspectRatio="none" viewBox="0 0 1 1">',`<g fill="transparent" stroke="${t}" stroke-width="${e}">`];this.container.classList.add("hasBorder")}const o=n-e,l=s-i,{svgFactory:h}=this,c=h.createElement("svg");c.classList.add("quadrilateralsContainer");c.setAttribute("width",0);c.setAttribute("height",0);c.role="none";const d=h.createElement("defs");c.append(d);const u=h.createElement("clipPath"),p=`clippath_${this.data.id}`;u.setAttribute("id",p);u.setAttribute("clipPathUnits","objectBoundingBox");d.append(u);for(let i=2,n=t.length;i<n;i+=8){const n=t[i],r=t[i+1],c=t[i+2],d=t[i+3],p=h.createElement("rect"),g=(c-e)/o,f=(s-r)/l,m=(n-c)/o,b=(r-d)/l;p.setAttribute("x",g);p.setAttribute("y",f);p.setAttribute("width",m);p.setAttribute("height",b);u.append(p);a?.push(`<rect vector-effect="non-scaling-stroke" x="${g}" y="${f}" width="${m}" height="${b}"/>`)}if(this.#ys){a.push("</g></svg>')");r.backgroundImage=a.join("")}this.container.append(c);this.container.style.clipPath=`url(#${p})`}_createPopup(t=null){const{data:e}=this;let i,n;if(t){i={str:t.text};n=t.date}else{i=e.contentsObj;n=e.modificationDate}const s=this.#As=new PopupAnnotationElement({data:{color:e.color,titleObj:e.titleObj,modificationDate:n,contentsObj:i,richText:e.richText,parentRect:e.rect,borderStyle:0,id:`popup_${e.id}`,rotation:e.rotation,noRotate:!0},linkService:this.linkService,parent:this.parent,elements:[this]});this.parent.div.append(s.render())}get hasPopupElement(){return!!(this.#As||this.popup||this.data.popupRef)}render(){unreachable("Abstract method `AnnotationElement.render` called")}_getElementsByName(t,e=null){const i=[];if(this._fieldObjects){const n=this._fieldObjects[t];if(n)for(const{page:t,id:s,exportValues:r}of n){if(-1===t)continue;if(s===e)continue;const n="string"==typeof r?r:null,a=document.querySelector(`[data-element-id="${s}"]`);!a||Tt.has(a)?i.push({id:s,exportValue:n,domElement:a}):warn(`_getElementsByName - element not allowed: ${s}`)}return i}for(const n of document.getElementsByName(t)){const{exportValue:t}=n,s=n.getAttribute("data-element-id");s!==e&&(Tt.has(n)&&i.push({id:s,exportValue:t,domElement:n}))}return i}show(){this.container&&(this.container.hidden=!1);this.popup?.maybeShow()}hide(){this.container&&(this.container.hidden=!0);this.popup?.forceHide()}getElementsToTriggerPopup(){return this.container}addHighlightArea(){const t=this.getElementsToTriggerPopup();if(Array.isArray(t))for(const e of t)e.classList.add("highlightArea");else t.classList.add("highlightArea")}_editOnDoubleClick(){if(!this._isEditable)return;const{annotationEditorType:t,data:{id:e}}=this;this.container.addEventListener("dblclick",(()=>{this.linkService.eventBus?.dispatch("switchannotationeditormode",{source:this,mode:t,editId:e,mustEnterInEditMode:!0})}))}get width(){return this.data.rect[2]-this.data.rect[0]}get height(){return this.data.rect[3]-this.data.rect[1]}}class LinkAnnotationElement extends AnnotationElement{constructor(t,e=null){super(t,{isRenderable:!0,ignoreBorder:!!e?.ignoreBorder,createQuadrilaterals:!0});this.isTooltipOnly=t.data.isTooltipOnly}render(){const{data:t,linkService:e}=this,i=document.createElement("a");i.setAttribute("data-element-id",t.id);let n=!1;if(t.url){e.addLinkAttributes(i,t.url,t.newWindow);n=!0}else if(t.action){this._bindNamedAction(i,t.action,t.overlaidText);n=!0}else if(t.attachment){this.#_s(i,t.attachment,t.overlaidText,t.attachmentDest);n=!0}else if(t.setOCGState){this.#Es(i,t.setOCGState,t.overlaidText);n=!0}else if(t.dest){this._bindLink(i,t.dest,t.overlaidText);n=!0}else{if(t.actions&&(t.actions.Action||t.actions["Mouse Up"]||t.actions["Mouse Down"])&&this.enableScripting&&this.hasJSActions){this._bindJSAction(i,t);n=!0}if(t.resetForm){this._bindResetFormAction(i,t.resetForm);n=!0}else if(this.isTooltipOnly&&!n){this._bindLink(i,"");n=!0}}this.container.classList.add("linkAnnotation");n&&this.container.append(i);return this.container}#Ss(){this.container.setAttribute("data-internal-link","")}_bindLink(t,e,i=""){t.href=this.linkService.getDestinationHash(e);t.onclick=()=>{e&&this.linkService.goToDestination(e);return!1};(e||""===e)&&this.#Ss();i&&(t.title=i)}_bindNamedAction(t,e,i=""){t.href=this.linkService.getAnchorUrl("");t.onclick=()=>{this.linkService.executeNamedAction(e);return!1};i&&(t.title=i);this.#Ss()}#_s(t,e,i="",n=null){t.href=this.linkService.getAnchorUrl("");e.description?t.title=e.description:i&&(t.title=i);t.onclick=()=>{this.downloadManager?.openOrDownloadData(e.content,e.filename,n);return!1};this.#Ss()}#Es(t,e,i=""){t.href=this.linkService.getAnchorUrl("");t.onclick=()=>{this.linkService.executeSetOCGState(e);return!1};i&&(t.title=i);this.#Ss()}_bindJSAction(t,e){t.href=this.linkService.getAnchorUrl("");const i=new Map([["Action","onclick"],["Mouse Up","onmouseup"],["Mouse Down","onmousedown"]]);for(const n of Object.keys(e.actions)){const s=i.get(n);s&&(t[s]=()=>{this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e.id,name:n}});return!1})}e.overlaidText&&(t.title=e.overlaidText);t.onclick||(t.onclick=()=>!1);this.#Ss()}_bindResetFormAction(t,e){const i=t.onclick;i||(t.href=this.linkService.getAnchorUrl(""));this.#Ss();if(this._fieldObjects)t.onclick=()=>{i?.();const{fields:t,refs:n,include:s}=e,r=[];if(0!==t.length||0!==n.length){const e=new Set(n);for(const i of t){const t=this._fieldObjects[i]||[];for(const{id:i}of t)e.add(i)}for(const t of Object.values(this._fieldObjects))for(const i of t)e.has(i.id)===s&&r.push(i)}else for(const t of Object.values(this._fieldObjects))r.push(...t);const a=this.annotationStorage,o=[];for(const t of r){const{id:e}=t;o.push(e);switch(t.type){case"text":{const i=t.defaultValue||"";a.setValue(e,{value:i});break}case"checkbox":case"radiobutton":{const i=t.defaultValue===t.exportValues;a.setValue(e,{value:i});break}case"combobox":case"listbox":{const i=t.defaultValue||"";a.setValue(e,{value:i});break}default:continue}const i=document.querySelector(`[data-element-id="${e}"]`);i&&(Tt.has(i)?i.dispatchEvent(new Event("resetform")):warn(`_bindResetFormAction - element not allowed: ${e}`))}this.enableScripting&&this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:"app",ids:o,name:"ResetForm"}});return!1};else{warn('_bindResetFormAction - "resetForm" action not supported, ensure that the `fieldObjects` parameter is provided.');i||(t.onclick=()=>!1)}}}class TextAnnotationElement extends AnnotationElement{constructor(t){super(t,{isRenderable:!0})}render(){this.container.classList.add("textAnnotation");const t=document.createElement("img");t.src=this.imageResourcesPath+"annotation-"+this.data.name.toLowerCase()+".svg";t.setAttribute("data-l10n-id","pdfjs-text-annotation-type");t.setAttribute("data-l10n-args",JSON.stringify({type:this.data.name}));!this.data.popupRef&&this.hasPopupData&&this._createPopup();this.container.append(t);return this.container}}class WidgetAnnotationElement extends AnnotationElement{render(){return this.container}showElementAndHideCanvas(t){if(this.data.hasOwnCanvas){"CANVAS"===t.previousSibling?.nodeName&&(t.previousSibling.hidden=!0);t.hidden=!1}}_getKeyModifier(t){return util_FeatureTest.platform.isMac?t.metaKey:t.ctrlKey}_setEventListener(t,e,i,n,s){i.includes("mouse")?t.addEventListener(i,(t=>{this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:this.data.id,name:n,value:s(t),shift:t.shiftKey,modifier:this._getKeyModifier(t)}})})):t.addEventListener(i,(t=>{if("blur"===i){if(!e.focused||!t.relatedTarget)return;e.focused=!1}else if("focus"===i){if(e.focused)return;e.focused=!0}s&&this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:this.data.id,name:n,value:s(t)}})}))}_setEventListeners(t,e,i,n){for(const[s,r]of i)if("Action"===r||this.data.actions?.[r]){"Focus"!==r&&"Blur"!==r||(e||={focused:!1});this._setEventListener(t,e,s,r,n);"Focus"!==r||this.data.actions?.Blur?"Blur"!==r||this.data.actions?.Focus||this._setEventListener(t,e,"focus","Focus",null):this._setEventListener(t,e,"blur","Blur",null)}}_setBackgroundColor(t){const e=this.data.backgroundColor||null;t.style.backgroundColor=null===e?"transparent":Util.makeHexColor(e[0],e[1],e[2])}_setTextStyle(t){const e=["left","center","right"],{fontColor:i}=this.data.defaultAppearanceData,n=this.data.defaultAppearanceData.fontSize||9,r=t.style;let a;const roundToOneDecimal=t=>Math.round(10*t)/10;if(this.data.multiLine){const t=Math.abs(this.data.rect[3]-this.data.rect[1]-2),e=t/(Math.round(t/(s*n))||1);a=Math.min(n,roundToOneDecimal(e/s))}else{const t=Math.abs(this.data.rect[3]-this.data.rect[1]-2);a=Math.min(n,roundToOneDecimal(t/s))}r.fontSize=`calc(${a}px * var(--total-scale-factor))`;r.color=Util.makeHexColor(i[0],i[1],i[2]);null!==this.data.textAlignment&&(r.textAlign=e[this.data.textAlignment])}_setRequired(t,e){e?t.setAttribute("required",!0):t.removeAttribute("required");t.setAttribute("aria-required",e)}}class TextWidgetAnnotationElement extends WidgetAnnotationElement{constructor(t){super(t,{isRenderable:t.renderForms||t.data.hasOwnCanvas||!t.data.hasAppearance&&!!t.data.fieldValue})}setPropertyOnSiblings(t,e,i,n){const s=this.annotationStorage;for(const r of this._getElementsByName(t.name,t.id)){r.domElement&&(r.domElement[e]=i);s.setValue(r.id,{[n]:i})}}render(){const t=this.annotationStorage,e=this.data.id;this.container.classList.add("textWidgetAnnotation");let i=null;if(this.renderForms){const n=t.getValue(e,{value:this.data.fieldValue});let s=n.value||"";const r=t.getValue(e,{charLimit:this.data.maxLen}).charLimit;r&&s.length>r&&(s=s.slice(0,r));let a=n.formattedValue||this.data.textContent?.join("\n")||null;a&&this.data.comb&&(a=a.replaceAll(/\s+/g,""));const o={userValue:s,formattedValue:a,lastCommittedValue:null,commitKey:1,focused:!1};if(this.data.multiLine){i=document.createElement("textarea");i.textContent=a??s;this.data.doNotScroll&&(i.style.overflowY="hidden")}else{i=document.createElement("input");i.type=this.data.password?"password":"text";i.setAttribute("value",a??s);this.data.doNotScroll&&(i.style.overflowX="hidden")}this.data.hasOwnCanvas&&(i.hidden=!0);Tt.add(i);i.setAttribute("data-element-id",e);i.disabled=this.data.readOnly;i.name=this.data.fieldName;i.tabIndex=0;const{datetimeFormat:l,datetimeType:h,timeStep:c}=this.data,d=!!h&&this.enableScripting;l&&(i.title=l);this._setRequired(i,this.data.required);r&&(i.maxLength=r);i.addEventListener("input",(n=>{t.setValue(e,{value:n.target.value});this.setPropertyOnSiblings(i,"value",n.target.value,"value");o.formattedValue=null}));i.addEventListener("resetform",(t=>{const e=this.data.defaultFieldValue??"";i.value=o.userValue=e;o.formattedValue=null}));let blurListener=t=>{const{formattedValue:e}=o;null!=e&&(t.target.value=e);t.target.scrollLeft=0};if(this.enableScripting&&this.hasJSActions){i.addEventListener("focus",(t=>{if(o.focused)return;const{target:e}=t;if(d){e.type=h;c&&(e.step=c)}if(o.userValue){const t=o.userValue;if(d)if("time"===h){const i=new Date(t),n=[i.getHours(),i.getMinutes(),i.getSeconds()];e.value=n.map((t=>t.toString().padStart(2,"0"))).join(":")}else e.value=new Date(t-Mt).toISOString().split("date"===h?"T":".",1)[0];else e.value=t}o.lastCommittedValue=e.value;o.commitKey=1;this.data.actions?.Focus||(o.focused=!0)}));i.addEventListener("updatefromsandbox",(i=>{this.showElementAndHideCanvas(i.target);const n={value(i){o.userValue=i.detail.value??"";d||t.setValue(e,{value:o.userValue.toString()});i.target.value=o.userValue},formattedValue(i){const{formattedValue:n}=i.detail;o.formattedValue=n;null!=n&&i.target!==document.activeElement&&(i.target.value=n);const s={formattedValue:n};d&&(s.value=n);t.setValue(e,s)},selRange(t){t.target.setSelectionRange(...t.detail.selRange)},charLimit:i=>{const{charLimit:n}=i.detail,{target:s}=i;if(0===n){s.removeAttribute("maxLength");return}s.setAttribute("maxLength",n);let r=o.userValue;if(r&&!(r.length<=n)){r=r.slice(0,n);s.value=o.userValue=r;t.setValue(e,{value:r});this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:r,willCommit:!0,commitKey:1,selStart:s.selectionStart,selEnd:s.selectionEnd}})}}};this._dispatchEventFromSandbox(n,i)}));i.addEventListener("keydown",(t=>{o.commitKey=1;let i=-1;"Escape"===t.key?i=0:"Enter"!==t.key||this.data.multiLine?"Tab"===t.key&&(o.commitKey=3):i=2;if(-1===i)return;const{value:n}=t.target;if(o.lastCommittedValue!==n){o.lastCommittedValue=n;o.userValue=n;this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:n,willCommit:!0,commitKey:i,selStart:t.target.selectionStart,selEnd:t.target.selectionEnd}})}}));const n=blurListener;blurListener=null;i.addEventListener("blur",(t=>{if(!o.focused||!t.relatedTarget)return;this.data.actions?.Blur||(o.focused=!1);const{target:i}=t;let{value:s}=i;if(d){if(s&&"time"===h){const t=s.split(":").map((t=>parseInt(t,10)));s=new Date(2e3,0,1,t[0],t[1],t[2]||0).valueOf();i.step=""}else s=new Date(s).valueOf();i.type="text"}o.userValue=s;o.lastCommittedValue!==s&&this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:s,willCommit:!0,commitKey:o.commitKey,selStart:t.target.selectionStart,selEnd:t.target.selectionEnd}});n(t)}));this.data.actions?.Keystroke&&i.addEventListener("beforeinput",(t=>{o.lastCommittedValue=null;const{data:i,target:n}=t,{value:s,selectionStart:r,selectionEnd:a}=n;let l=r,h=a;switch(t.inputType){case"deleteWordBackward":{const t=s.substring(0,r).match(/\w*[^\w]*$/);t&&(l-=t[0].length);break}case"deleteWordForward":{const t=s.substring(r).match(/^[^\w]*\w*/);t&&(h+=t[0].length);break}case"deleteContentBackward":r===a&&(l-=1);break;case"deleteContentForward":r===a&&(h+=1)}t.preventDefault();this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:s,change:i||"",willCommit:!1,selStart:l,selEnd:h}})}));this._setEventListeners(i,o,[["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"]],(t=>t.target.value))}blurListener&&i.addEventListener("blur",blurListener);if(this.data.comb){const t=(this.data.rect[2]-this.data.rect[0])/r;i.classList.add("comb");i.style.letterSpacing=`calc(${t}px * var(--total-scale-factor) - 1ch)`}}else{i=document.createElement("div");i.textContent=this.data.fieldValue;i.style.verticalAlign="middle";i.style.display="table-cell";this.data.hasOwnCanvas&&(i.hidden=!0)}this._setTextStyle(i);this._setBackgroundColor(i);this._setDefaultPropertiesFromJS(i);this.container.append(i);return this.container}}class SignatureWidgetAnnotationElement extends WidgetAnnotationElement{constructor(t){super(t,{isRenderable:!!t.data.hasOwnCanvas})}}class CheckboxWidgetAnnotationElement extends WidgetAnnotationElement{constructor(t){super(t,{isRenderable:t.renderForms})}render(){const t=this.annotationStorage,e=this.data,i=e.id;let n=t.getValue(i,{value:e.exportValue===e.fieldValue}).value;if("string"==typeof n){n="Off"!==n;t.setValue(i,{value:n})}this.container.classList.add("buttonWidgetAnnotation","checkBox");const s=document.createElement("input");Tt.add(s);s.setAttribute("data-element-id",i);s.disabled=e.readOnly;this._setRequired(s,this.data.required);s.type="checkbox";s.name=e.fieldName;n&&s.setAttribute("checked",!0);s.setAttribute("exportValue",e.exportValue);s.tabIndex=0;s.addEventListener("change",(n=>{const{name:s,checked:r}=n.target;for(const n of this._getElementsByName(s,i)){const i=r&&n.exportValue===e.exportValue;n.domElement&&(n.domElement.checked=i);t.setValue(n.id,{value:i})}t.setValue(i,{value:r})}));s.addEventListener("resetform",(t=>{const i=e.defaultFieldValue||"Off";t.target.checked=i===e.exportValue}));if(this.enableScripting&&this.hasJSActions){s.addEventListener("updatefromsandbox",(e=>{const n={value(e){e.target.checked="Off"!==e.detail.value;t.setValue(i,{value:e.target.checked})}};this._dispatchEventFromSandbox(n,e)}));this._setEventListeners(s,null,[["change","Validate"],["change","Action"],["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"]],(t=>t.target.checked))}this._setBackgroundColor(s);this._setDefaultPropertiesFromJS(s);this.container.append(s);return this.container}}class RadioButtonWidgetAnnotationElement extends WidgetAnnotationElement{constructor(t){super(t,{isRenderable:t.renderForms})}render(){this.container.classList.add("buttonWidgetAnnotation","radioButton");const t=this.annotationStorage,e=this.data,i=e.id;let n=t.getValue(i,{value:e.fieldValue===e.buttonValue}).value;if("string"==typeof n){n=n!==e.buttonValue;t.setValue(i,{value:n})}if(n)for(const n of this._getElementsByName(e.fieldName,i))t.setValue(n.id,{value:!1});const s=document.createElement("input");Tt.add(s);s.setAttribute("data-element-id",i);s.disabled=e.readOnly;this._setRequired(s,this.data.required);s.type="radio";s.name=e.fieldName;n&&s.setAttribute("checked",!0);s.tabIndex=0;s.addEventListener("change",(e=>{const{name:n,checked:s}=e.target;for(const e of this._getElementsByName(n,i))t.setValue(e.id,{value:!1});t.setValue(i,{value:s})}));s.addEventListener("resetform",(t=>{const i=e.defaultFieldValue;t.target.checked=null!=i&&i===e.buttonValue}));if(this.enableScripting&&this.hasJSActions){const n=e.buttonValue;s.addEventListener("updatefromsandbox",(e=>{const s={value:e=>{const s=n===e.detail.value;for(const n of this._getElementsByName(e.target.name)){const e=s&&n.id===i;n.domElement&&(n.domElement.checked=e);t.setValue(n.id,{value:e})}}};this._dispatchEventFromSandbox(s,e)}));this._setEventListeners(s,null,[["change","Validate"],["change","Action"],["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"]],(t=>t.target.checked))}this._setBackgroundColor(s);this._setDefaultPropertiesFromJS(s);this.container.append(s);return this.container}}class PushButtonWidgetAnnotationElement extends LinkAnnotationElement{constructor(t){super(t,{ignoreBorder:t.data.hasAppearance})}render(){const t=super.render();t.classList.add("buttonWidgetAnnotation","pushButton");const e=t.lastChild;if(this.enableScripting&&this.hasJSActions&&e){this._setDefaultPropertiesFromJS(e);e.addEventListener("updatefromsandbox",(t=>{this._dispatchEventFromSandbox({},t)}))}return t}}class ChoiceWidgetAnnotationElement extends WidgetAnnotationElement{constructor(t){super(t,{isRenderable:t.renderForms})}render(){this.container.classList.add("choiceWidgetAnnotation");const t=this.annotationStorage,e=this.data.id,i=t.getValue(e,{value:this.data.fieldValue}),n=document.createElement("select");Tt.add(n);n.setAttribute("data-element-id",e);n.disabled=this.data.readOnly;this._setRequired(n,this.data.required);n.name=this.data.fieldName;n.tabIndex=0;let s=this.data.combo&&this.data.options.length>0;if(!this.data.combo){n.size=this.data.options.length;this.data.multiSelect&&(n.multiple=!0)}n.addEventListener("resetform",(t=>{const e=this.data.defaultFieldValue;for(const t of n.options)t.selected=t.value===e}));for(const t of this.data.options){const e=document.createElement("option");e.textContent=t.displayValue;e.value=t.exportValue;if(i.value.includes(t.exportValue)){e.setAttribute("selected",!0);s=!1}n.append(e)}let r=null;if(s){const t=document.createElement("option");t.value=" ";t.setAttribute("hidden",!0);t.setAttribute("selected",!0);n.prepend(t);r=()=>{t.remove();n.removeEventListener("input",r);r=null};n.addEventListener("input",r)}const getValue=t=>{const e=t?"value":"textContent",{options:i,multiple:s}=n;return s?Array.prototype.filter.call(i,(t=>t.selected)).map((t=>t[e])):-1===i.selectedIndex?null:i[i.selectedIndex][e]};let a=getValue(!1);const getItems=t=>{const e=t.target.options;return Array.prototype.map.call(e,(t=>({displayValue:t.textContent,exportValue:t.value})))};if(this.enableScripting&&this.hasJSActions){n.addEventListener("updatefromsandbox",(i=>{const s={value(i){r?.();const s=i.detail.value,o=new Set(Array.isArray(s)?s:[s]);for(const t of n.options)t.selected=o.has(t.value);t.setValue(e,{value:getValue(!0)});a=getValue(!1)},multipleSelection(t){n.multiple=!0},remove(i){const s=n.options,r=i.detail.remove;s[r].selected=!1;n.remove(r);if(s.length>0){-1===Array.prototype.findIndex.call(s,(t=>t.selected))&&(s[0].selected=!0)}t.setValue(e,{value:getValue(!0),items:getItems(i)});a=getValue(!1)},clear(i){for(;0!==n.length;)n.remove(0);t.setValue(e,{value:null,items:[]});a=getValue(!1)},insert(i){const{index:s,displayValue:r,exportValue:o}=i.detail.insert,l=n.children[s],h=document.createElement("option");h.textContent=r;h.value=o;l?l.before(h):n.append(h);t.setValue(e,{value:getValue(!0),items:getItems(i)});a=getValue(!1)},items(i){const{items:s}=i.detail;for(;0!==n.length;)n.remove(0);for(const t of s){const{displayValue:e,exportValue:i}=t,s=document.createElement("option");s.textContent=e;s.value=i;n.append(s)}n.options.length>0&&(n.options[0].selected=!0);t.setValue(e,{value:getValue(!0),items:getItems(i)});a=getValue(!1)},indices(i){const n=new Set(i.detail.indices);for(const t of i.target.options)t.selected=n.has(t.index);t.setValue(e,{value:getValue(!0)});a=getValue(!1)},editable(t){t.target.disabled=!t.detail.editable}};this._dispatchEventFromSandbox(s,i)}));n.addEventListener("input",(i=>{const n=getValue(!0),s=getValue(!1);t.setValue(e,{value:n});i.preventDefault();this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:a,change:s,changeEx:n,willCommit:!1,commitKey:1,keyDown:!1}})}));this._setEventListeners(n,null,[["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"],["input","Action"],["input","Validate"]],(t=>t.target.value))}else n.addEventListener("input",(function(i){t.setValue(e,{value:getValue(!0)})}));this.data.combo&&this._setTextStyle(n);this._setBackgroundColor(n);this._setDefaultPropertiesFromJS(n);this.container.append(n);return this.container}}class PopupAnnotationElement extends AnnotationElement{constructor(t){const{data:e,elements:i}=t;super(t,{isRenderable:AnnotationElement._hasPopupData(e)});this.elements=i;this.popup=null}render(){const{container:t}=this;t.classList.add("popupAnnotation");t.role="comment";const e=this.popup=new PopupElement({container:this.container,color:this.data.color,titleObj:this.data.titleObj,modificationDate:this.data.modificationDate||this.data.creationDate,contentsObj:this.data.contentsObj,richText:this.data.richText,rect:this.data.rect,parentRect:this.data.parentRect||null,parent:this.parent,elements:this.elements,open:this.data.open}),i=[];for(const t of this.elements){t.popup=e;t.container.ariaHasPopup="dialog";i.push(t.data.id);t.addHighlightArea()}this.container.setAttribute("aria-controls",i.map((t=>`${W}${t}`)).join(","));return this.container}}class PopupElement{#Cs=this.#gs.bind(this);#Ts=this.#Ms.bind(this);#Ds=this.#Ps.bind(this);#ks=this.#Is.bind(this);#Rs=null;#bt=null;#Fs=null;#Ls=null;#Os=null;#Ns=null;#Bs=null;#Us=!1;#Hs=null;#zs=null;#T=null;#js=null;#Gs=null;#Ws=null;#ws=null;#Vs=!1;constructor({container:t,color:e,elements:i,titleObj:n,modificationDate:s,contentsObj:r,richText:a,parent:o,rect:l,parentRect:h,open:c}){this.#bt=t;this.#Ws=n;this.#Fs=r;this.#Gs=a;this.#Ns=o;this.#Rs=e;this.#js=l;this.#Bs=h;this.#Os=i;this.#Ls=PDFDateString.toDateObject(s);this.trigger=i.flatMap((t=>t.getElementsToTriggerPopup()));this.#$s();this.#bt.hidden=!0;c&&this.#Is()}#$s(){if(this.#zs)return;this.#zs=new AbortController;const{signal:t}=this.#zs;for(const e of this.trigger){e.addEventListener("click",this.#ks,{signal:t});e.addEventListener("mouseenter",this.#Ds,{signal:t});e.addEventListener("mouseleave",this.#Ts,{signal:t});e.classList.add("popupTriggerArea")}for(const e of this.#Os)e.container?.addEventListener("keydown",this.#Cs,{signal:t})}render(){if(this.#Hs)return;const t=this.#Hs=document.createElement("div");t.className="popup";if(this.#Rs){const e=t.style.outlineColor=Util.makeHexColor(...this.#Rs);t.style.backgroundColor=`color-mix(in srgb, ${e} 30%, white)`}const e=document.createElement("span");e.className="header";if(this.#Ws?.str){const t=document.createElement("span");t.className="title";e.append(t);({dir:t.dir,str:t.textContent}=this.#Ws)}t.append(e);if(this.#Ls){const t=document.createElement("time");t.className="popupDate";t.setAttribute("data-l10n-id","pdfjs-annotation-date-time-string");t.setAttribute("data-l10n-args",JSON.stringify({dateObj:this.#Ls.valueOf()}));t.dateTime=this.#Ls.toISOString();e.append(t)}const i=this.#qs;if(i){XfaLayer.render({xfaHtml:i,intent:"richText",div:t});t.lastChild.classList.add("richText","popupContent")}else{const e=this._formatContents(this.#Fs);t.append(e)}this.#bt.append(t)}get#qs(){const t=this.#Gs,e=this.#Fs;return!t?.str||e?.str&&e.str!==t.str?null:this.#Gs.html||null}get#Xs(){return this.#qs?.attributes?.style?.fontSize||0}get#Ks(){return this.#qs?.attributes?.style?.color||null}#Ys(t){const e=[],i={str:t,html:{name:"div",attributes:{dir:"auto"},children:[{name:"p",children:e}]}},n={style:{color:this.#Ks,fontSize:this.#Xs?`calc(${this.#Xs}px * var(--total-scale-factor))`:""}};for(const i of t.split("\n"))e.push({name:"span",value:i,attributes:n});return i}_formatContents({str:t,dir:e}){const i=document.createElement("p");i.classList.add("popupContent");i.dir=e;const n=t.split(/(?:\r\n?|\n)/);for(let t=0,e=n.length;t<e;++t){const s=n[t];i.append(document.createTextNode(s));t<e-1&&i.append(document.createElement("br"))}return i}#gs(t){t.altKey||t.shiftKey||t.ctrlKey||t.metaKey||("Enter"===t.key||"Escape"===t.key&&this.#Us)&&this.#Is()}updateEdited({rect:t,popup:e,deleted:i}){if(i||e?.deleted)this.remove();else{this.#$s();this.#ws||={contentsObj:this.#Fs,richText:this.#Gs};t&&(this.#T=null);if(e){this.#Gs=this.#Ys(e.text);this.#Ls=PDFDateString.toDateObject(e.date);this.#Fs=null}this.#Hs?.remove();this.#Hs=null}}resetEdited(){if(this.#ws){({contentsObj:this.#Fs,richText:this.#Gs}=this.#ws);this.#ws=null;this.#Hs?.remove();this.#Hs=null;this.#T=null}}remove(){this.#zs?.abort();this.#zs=null;this.#Hs?.remove();this.#Hs=null;this.#Vs=!1;this.#Us=!1;for(const t of this.trigger)t.classList.remove("popupTriggerArea")}#Qs(){if(null!==this.#T)return;const{page:{view:t},viewport:{rawDims:{pageWidth:e,pageHeight:i,pageX:n,pageY:s}}}=this.#Ns;let r=!!this.#Bs,a=r?this.#Bs:this.#js;for(const t of this.#Os)if(!a||null!==Util.intersect(t.data.rect,a)){a=t.data.rect;r=!0;break}const o=Util.normalizeRect([a[0],t[3]-a[1]+t[1],a[2],t[3]-a[3]+t[1]]),l=r?a[2]-a[0]+5:0,h=o[0]+l,c=o[1];this.#T=[100*(h-n)/e,100*(c-s)/i];const{style:d}=this.#bt;d.left=`${this.#T[0]}%`;d.top=`${this.#T[1]}%`}#Is(){this.#Us=!this.#Us;if(this.#Us){this.#Ps();this.#bt.addEventListener("click",this.#ks);this.#bt.addEventListener("keydown",this.#Cs)}else{this.#Ms();this.#bt.removeEventListener("click",this.#ks);this.#bt.removeEventListener("keydown",this.#Cs)}}#Ps(){this.#Hs||this.render();if(this.isVisible)this.#Us&&this.#bt.classList.add("focused");else{this.#Qs();this.#bt.hidden=!1;this.#bt.style.zIndex=parseInt(this.#bt.style.zIndex)+1e3}}#Ms(){this.#bt.classList.remove("focused");if(!this.#Us&&this.isVisible){this.#bt.hidden=!0;this.#bt.style.zIndex=parseInt(this.#bt.style.zIndex)-1e3}}forceHide(){this.#Vs=this.isVisible;this.#Vs&&(this.#bt.hidden=!0)}maybeShow(){this.#$s();if(this.#Vs){this.#Hs||this.#Ps();this.#Vs=!1;this.#bt.hidden=!1}}get isVisible(){return!1===this.#bt.hidden}}class FreeTextAnnotationElement extends AnnotationElement{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0});this.textContent=t.data.textContent;this.textPosition=t.data.textPosition;this.annotationEditorType=f.FREETEXT}render(){this.container.classList.add("freeTextAnnotation");if(this.textContent){const t=document.createElement("div");t.classList.add("annotationTextContent");t.setAttribute("role","comment");for(const e of this.textContent){const i=document.createElement("span");i.textContent=e;t.append(i)}this.container.append(t)}!this.data.popupRef&&this.hasPopupData&&this._createPopup();this._editOnDoubleClick();return this.container}}class LineAnnotationElement extends AnnotationElement{#Js=null;constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0})}render(){this.container.classList.add("lineAnnotation");const{data:t,width:e,height:i}=this,n=this.svgFactory.create(e,i,!0),s=this.#Js=this.svgFactory.createElement("svg:line");s.setAttribute("x1",t.rect[2]-t.lineCoordinates[0]);s.setAttribute("y1",t.rect[3]-t.lineCoordinates[1]);s.setAttribute("x2",t.rect[2]-t.lineCoordinates[2]);s.setAttribute("y2",t.rect[3]-t.lineCoordinates[3]);s.setAttribute("stroke-width",t.borderStyle.width||1);s.setAttribute("stroke","transparent");s.setAttribute("fill","transparent");n.append(s);this.container.append(n);!t.popupRef&&this.hasPopupData&&this._createPopup();return this.container}getElementsToTriggerPopup(){return this.#Js}addHighlightArea(){this.container.classList.add("highlightArea")}}class SquareAnnotationElement extends AnnotationElement{#Zs=null;constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0})}render(){this.container.classList.add("squareAnnotation");const{data:t,width:e,height:i}=this,n=this.svgFactory.create(e,i,!0),s=t.borderStyle.width,r=this.#Zs=this.svgFactory.createElement("svg:rect");r.setAttribute("x",s/2);r.setAttribute("y",s/2);r.setAttribute("width",e-s);r.setAttribute("height",i-s);r.setAttribute("stroke-width",s||1);r.setAttribute("stroke","transparent");r.setAttribute("fill","transparent");n.append(r);this.container.append(n);!t.popupRef&&this.hasPopupData&&this._createPopup();return this.container}getElementsToTriggerPopup(){return this.#Zs}addHighlightArea(){this.container.classList.add("highlightArea")}}class CircleAnnotationElement extends AnnotationElement{#tr=null;constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0})}render(){this.container.classList.add("circleAnnotation");const{data:t,width:e,height:i}=this,n=this.svgFactory.create(e,i,!0),s=t.borderStyle.width,r=this.#tr=this.svgFactory.createElement("svg:ellipse");r.setAttribute("cx",e/2);r.setAttribute("cy",i/2);r.setAttribute("rx",e/2-s/2);r.setAttribute("ry",i/2-s/2);r.setAttribute("stroke-width",s||1);r.setAttribute("stroke","transparent");r.setAttribute("fill","transparent");n.append(r);this.container.append(n);!t.popupRef&&this.hasPopupData&&this._createPopup();return this.container}getElementsToTriggerPopup(){return this.#tr}addHighlightArea(){this.container.classList.add("highlightArea")}}class PolylineAnnotationElement extends AnnotationElement{#er=null;constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0});this.containerClassName="polylineAnnotation";this.svgElementName="svg:polyline"}render(){this.container.classList.add(this.containerClassName);const{data:{rect:t,vertices:e,borderStyle:i,popupRef:n},width:s,height:r}=this;if(!e)return this.container;const a=this.svgFactory.create(s,r,!0);let o=[];for(let i=0,n=e.length;i<n;i+=2){const n=e[i]-t[0],s=t[3]-e[i+1];o.push(`${n},${s}`)}o=o.join(" ");const l=this.#er=this.svgFactory.createElement(this.svgElementName);l.setAttribute("points",o);l.setAttribute("stroke-width",i.width||1);l.setAttribute("stroke","transparent");l.setAttribute("fill","transparent");a.append(l);this.container.append(a);!n&&this.hasPopupData&&this._createPopup();return this.container}getElementsToTriggerPopup(){return this.#er}addHighlightArea(){this.container.classList.add("highlightArea")}}class PolygonAnnotationElement extends PolylineAnnotationElement{constructor(t){super(t);this.containerClassName="polygonAnnotation";this.svgElementName="svg:polygon"}}class CaretAnnotationElement extends AnnotationElement{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0})}render(){this.container.classList.add("caretAnnotation");!this.data.popupRef&&this.hasPopupData&&this._createPopup();return this.container}}class InkAnnotationElement extends AnnotationElement{#ir=null;#nr=[];constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0});this.containerClassName="inkAnnotation";this.svgElementName="svg:polyline";this.annotationEditorType="InkHighlight"===this.data.it?f.HIGHLIGHT:f.INK}#sr(t,e){switch(t){case 90:return{transform:`rotate(90) translate(${-e[0]},${e[1]}) scale(1,-1)`,width:e[3]-e[1],height:e[2]-e[0]};case 180:return{transform:`rotate(180) translate(${-e[2]},${e[1]}) scale(1,-1)`,width:e[2]-e[0],height:e[3]-e[1]};case 270:return{transform:`rotate(270) translate(${-e[2]},${e[3]}) scale(1,-1)`,width:e[3]-e[1],height:e[2]-e[0]};default:return{transform:`translate(${-e[0]},${e[3]}) scale(1,-1)`,width:e[2]-e[0],height:e[3]-e[1]}}}render(){this.container.classList.add(this.containerClassName);const{data:{rect:t,rotation:e,inkLists:i,borderStyle:n,popupRef:s}}=this,{transform:r,width:a,height:o}=this.#sr(e,t),l=this.svgFactory.create(a,o,!0),h=this.#ir=this.svgFactory.createElement("svg:g");l.append(h);h.setAttribute("stroke-width",n.width||1);h.setAttribute("stroke-linecap","round");h.setAttribute("stroke-linejoin","round");h.setAttribute("stroke-miterlimit",10);h.setAttribute("stroke","transparent");h.setAttribute("fill","transparent");h.setAttribute("transform",r);for(let t=0,e=i.length;t<e;t++){const e=this.svgFactory.createElement(this.svgElementName);this.#nr.push(e);e.setAttribute("points",i[t].join(","));h.append(e)}!s&&this.hasPopupData&&this._createPopup();this.container.append(l);this._editOnDoubleClick();return this.container}updateEdited(t){super.updateEdited(t);const{thickness:e,points:i,rect:n}=t,s=this.#ir;e>=0&&s.setAttribute("stroke-width",e||1);if(i)for(let t=0,e=this.#nr.length;t<e;t++)this.#nr[t].setAttribute("points",i[t].join(","));if(n){const{transform:t,width:e,height:i}=this.#sr(this.data.rotation,n);s.parentElement.setAttribute("viewBox",`0 0 ${e} ${i}`);s.setAttribute("transform",t)}}getElementsToTriggerPopup(){return this.#nr}addHighlightArea(){this.container.classList.add("highlightArea")}}class HighlightAnnotationElement extends AnnotationElement{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0});this.annotationEditorType=f.HIGHLIGHT}render(){const{data:{overlaidText:t,popupRef:e}}=this;!e&&this.hasPopupData&&this._createPopup();this.container.classList.add("highlightAnnotation");this._editOnDoubleClick();if(t){const e=document.createElement("mark");e.classList.add("overlaidText");e.textContent=t;this.container.append(e)}return this.container}}class UnderlineAnnotationElement extends AnnotationElement{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0})}render(){const{data:{overlaidText:t,popupRef:e}}=this;!e&&this.hasPopupData&&this._createPopup();this.container.classList.add("underlineAnnotation");if(t){const e=document.createElement("u");e.classList.add("overlaidText");e.textContent=t;this.container.append(e)}return this.container}}class SquigglyAnnotationElement extends AnnotationElement{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0})}render(){const{data:{overlaidText:t,popupRef:e}}=this;!e&&this.hasPopupData&&this._createPopup();this.container.classList.add("squigglyAnnotation");if(t){const e=document.createElement("u");e.classList.add("overlaidText");e.textContent=t;this.container.append(e)}return this.container}}class StrikeOutAnnotationElement extends AnnotationElement{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0})}render(){const{data:{overlaidText:t,popupRef:e}}=this;!e&&this.hasPopupData&&this._createPopup();this.container.classList.add("strikeoutAnnotation");if(t){const e=document.createElement("s");e.classList.add("overlaidText");e.textContent=t;this.container.append(e)}return this.container}}class StampAnnotationElement extends AnnotationElement{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0});this.annotationEditorType=f.STAMP}render(){this.container.classList.add("stampAnnotation");this.container.setAttribute("role","img");!this.data.popupRef&&this.hasPopupData&&this._createPopup();this._editOnDoubleClick();return this.container}}class FileAttachmentAnnotationElement extends AnnotationElement{#rr=null;constructor(t){super(t,{isRenderable:!0});const{file:e}=this.data;this.filename=e.filename;this.content=e.content;this.linkService.eventBus?.dispatch("fileattachmentannotation",{source:this,...e})}render(){this.container.classList.add("fileAttachmentAnnotation");const{container:t,data:e}=this;let i;if(e.hasAppearance||0===e.fillAlpha)i=document.createElement("div");else{i=document.createElement("img");i.src=`${this.imageResourcesPath}annotation-${/paperclip/i.test(e.name)?"paperclip":"pushpin"}.svg`;e.fillAlpha&&e.fillAlpha<1&&(i.style=`filter: opacity(${Math.round(100*e.fillAlpha)}%);`)}i.addEventListener("dblclick",this.#ar.bind(this));this.#rr=i;const{isMac:n}=util_FeatureTest.platform;t.addEventListener("keydown",(t=>{"Enter"===t.key&&(n?t.metaKey:t.ctrlKey)&&this.#ar()}));!e.popupRef&&this.hasPopupData?this._createPopup():i.classList.add("popupTriggerArea");t.append(i);return t}getElementsToTriggerPopup(){return this.#rr}addHighlightArea(){this.container.classList.add("highlightArea")}#ar(){this.downloadManager?.openOrDownloadData(this.content,this.filename)}}class AnnotationLayer{#or=null;#lr=null;#hr=new Map;#cr=null;constructor({div:t,accessibilityManager:e,annotationCanvasMap:i,annotationEditorUIManager:n,page:s,viewport:r,structTreeLayer:a}){this.div=t;this.#or=e;this.#lr=i;this.#cr=a||null;this.page=s;this.viewport=r;this.zIndex=0;this._annotationEditorUIManager=n}hasEditableAnnotations(){return this.#hr.size>0}async#dr(t,e,i){const n=t.firstChild||t,s=n.id=`${W}${e}`,r=await(this.#cr?.getAriaAttributes(s));if(r)for(const[t,e]of r)n.setAttribute(t,e);if(i)i.at(-1).container.after(t);else{this.div.append(t);this.#or?.moveElementInDOM(this.div,t,n,!1)}}async render(t){const{annotations:e}=t,i=this.div;setLayerDimensions(i,this.viewport);const n=new Map,s={data:null,layer:i,linkService:t.linkService,downloadManager:t.downloadManager,imageResourcesPath:t.imageResourcesPath||"",renderForms:!1!==t.renderForms,svgFactory:new DOMSVGFactory,annotationStorage:t.annotationStorage||new AnnotationStorage,enableScripting:!0===t.enableScripting,hasJSActions:t.hasJSActions,fieldObjects:t.fieldObjects,parent:this,elements:null};for(const t of e){if(t.noHTML)continue;const e=t.annotationType===S.POPUP;if(e){const e=n.get(t.id);if(!e)continue;s.elements=e}else if(t.rect[2]===t.rect[0]||t.rect[3]===t.rect[1])continue;s.data=t;const i=AnnotationElementFactory.create(s);if(!i.isRenderable)continue;if(!e&&t.popupRef){const e=n.get(t.popupRef);e?e.push(i):n.set(t.popupRef,[i])}const r=i.render();t.hidden&&(r.style.visibility="hidden");await this.#dr(r,t.id,s.elements);if(i._isEditable){this.#hr.set(i.data.id,i);this._annotationEditorUIManager?.renderAnnotationElement(i)}}this.#ur()}async addLinkAnnotations(t,e){const i={data:null,layer:this.div,linkService:e,svgFactory:new DOMSVGFactory,parent:this};for(const e of t){e.borderStyle||=AnnotationLayer._defaultBorderStyle;i.data=e;const t=AnnotationElementFactory.create(i);if(!t.isRenderable)continue;const n=t.render();await this.#dr(n,e.id,null)}}update({viewport:t}){const e=this.div;this.viewport=t;setLayerDimensions(e,{rotation:t.rotation});this.#ur();e.hidden=!1}#ur(){if(!this.#lr)return;const t=this.div;for(const[e,i]of this.#lr){const n=t.querySelector(`[data-annotation-id="${e}"]`);if(!n)continue;i.className="annotationContent";const{firstChild:s}=n;s?"CANVAS"===s.nodeName?s.replaceWith(i):s.classList.contains("annotationContent")?s.after(i):s.before(i):n.append(i);const r=this.#hr.get(e);if(r)if(r._hasNoCanvas){this._annotationEditorUIManager?.setMissingCanvas(e,n.id,i);r._hasNoCanvas=!1}else r.canvas=i}this.#lr.clear()}getEditableAnnotations(){return Array.from(this.#hr.values())}getEditableAnnotation(t){return this.#hr.get(t)}static get _defaultBorderStyle(){return shadow(this,"_defaultBorderStyle",Object.freeze({width:1,rawWidth:1,style:C,dashArray:[3],horizontalCornerRadius:0,verticalCornerRadius:0}))}}const Dt=/\r\n?|\n/g;class FreeTextEditor extends AnnotationEditor{#Rs;#pr="";#gr=`${this.id}-editor`;#fr=null;#Xs;_colorPicker=null;static _freeTextDefaultContent="";static _internalPadding=0;static _defaultColor=null;static _defaultFontSize=10;static get _keyboardManager(){const t=FreeTextEditor.prototype,arrowChecker=t=>t.isEmpty(),e=AnnotationEditorUIManager.TRANSLATE_SMALL,i=AnnotationEditorUIManager.TRANSLATE_BIG;return shadow(this,"_keyboardManager",new KeyboardManager([[["ctrl+s","mac+meta+s","ctrl+p","mac+meta+p"],t.commitOrRemove,{bubbles:!0}],[["ctrl+Enter","mac+meta+Enter","Escape","mac+Escape"],t.commitOrRemove],[["ArrowLeft","mac+ArrowLeft"],t._translateEmpty,{args:[-e,0],checker:arrowChecker}],[["ctrl+ArrowLeft","mac+shift+ArrowLeft"],t._translateEmpty,{args:[-i,0],checker:arrowChecker}],[["ArrowRight","mac+ArrowRight"],t._translateEmpty,{args:[e,0],checker:arrowChecker}],[["ctrl+ArrowRight","mac+shift+ArrowRight"],t._translateEmpty,{args:[i,0],checker:arrowChecker}],[["ArrowUp","mac+ArrowUp"],t._translateEmpty,{args:[0,-e],checker:arrowChecker}],[["ctrl+ArrowUp","mac+shift+ArrowUp"],t._translateEmpty,{args:[0,-i],checker:arrowChecker}],[["ArrowDown","mac+ArrowDown"],t._translateEmpty,{args:[0,e],checker:arrowChecker}],[["ctrl+ArrowDown","mac+shift+ArrowDown"],t._translateEmpty,{args:[0,i],checker:arrowChecker}]]))}static _type="freetext";static _editorType=f.FREETEXT;constructor(t){super({...t,name:"freeTextEditor"});this.#Rs=t.color||FreeTextEditor._defaultColor||AnnotationEditor._defaultLineColor;this.#Xs=t.fontSize||FreeTextEditor._defaultFontSize;this.annotationElementId||this._uiManager.a11yAlert("pdfjs-editor-freetext-added-alert")}static initialize(t,e){AnnotationEditor.initialize(t,e);const i=getComputedStyle(document.documentElement);this._internalPadding=parseFloat(i.getPropertyValue("--freetext-padding"))}static updateDefaultParams(t,e){switch(t){case m.FREETEXT_SIZE:FreeTextEditor._defaultFontSize=e;break;case m.FREETEXT_COLOR:FreeTextEditor._defaultColor=e}}updateParams(t,e){switch(t){case m.FREETEXT_SIZE:this.#mr(e);break;case m.FREETEXT_COLOR:this.#br(e)}}static get defaultPropertiesToUpdate(){return[[m.FREETEXT_SIZE,FreeTextEditor._defaultFontSize],[m.FREETEXT_COLOR,FreeTextEditor._defaultColor||AnnotationEditor._defaultLineColor]]}get propertiesToUpdate(){return[[m.FREETEXT_SIZE,this.#Xs],[m.FREETEXT_COLOR,this.#Rs]]}get toolbarButtons(){this._colorPicker||=new BasicColorPicker(this);return[["colorPicker",this._colorPicker]]}get colorType(){return m.FREETEXT_COLOR}get colorValue(){return this.#Rs}#mr(t){const setFontsize=t=>{this.editorDiv.style.fontSize=`calc(${t}px * var(--total-scale-factor))`;this.translate(0,-(t-this.#Xs)*this.parentScale);this.#Xs=t;this.#vr()},e=this.#Xs;this.addCommands({cmd:setFontsize.bind(this,t),undo:setFontsize.bind(this,e),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:m.FREETEXT_SIZE,overwriteIfSameType:!0,keepUndo:!0})}#br(t){const setColor=t=>{this.#Rs=this.editorDiv.style.color=t;this._colorPicker?.update(t)},e=this.#Rs;this.addCommands({cmd:setColor.bind(this,t),undo:setColor.bind(this,e),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:m.FREETEXT_COLOR,overwriteIfSameType:!0,keepUndo:!0})}_translateEmpty(t,e){this._uiManager.translateSelectedEditors(t,e,!0)}getInitialTranslation(){const t=this.parentScale;return[-FreeTextEditor._internalPadding*t,-(FreeTextEditor._internalPadding+this.#Xs)*t]}rebuild(){if(this.parent){super.rebuild();null!==this.div&&(this.isAttachedToDOM||this.parent.add(this))}}enableEditMode(){if(!super.enableEditMode())return!1;this.overlayDiv.classList.remove("enabled");this.editorDiv.contentEditable=!0;this._isDraggable=!1;this.div.removeAttribute("aria-activedescendant");this.#fr=new AbortController;const t=this._uiManager.combinedSignal(this.#fr);this.editorDiv.addEventListener("keydown",this.editorDivKeydown.bind(this),{signal:t});this.editorDiv.addEventListener("focus",this.editorDivFocus.bind(this),{signal:t});this.editorDiv.addEventListener("blur",this.editorDivBlur.bind(this),{signal:t});this.editorDiv.addEventListener("input",this.editorDivInput.bind(this),{signal:t});this.editorDiv.addEventListener("paste",this.editorDivPaste.bind(this),{signal:t});return!0}disableEditMode(){if(!super.disableEditMode())return!1;this.overlayDiv.classList.add("enabled");this.editorDiv.contentEditable=!1;this.div.setAttribute("aria-activedescendant",this.#gr);this._isDraggable=!0;this.#fr?.abort();this.#fr=null;this.div.focus({preventScroll:!0});this.isEditing=!1;this.parent.div.classList.add("freetextEditing");return!0}focusin(t){if(this._focusEventsAllowed){super.focusin(t);t.target!==this.editorDiv&&this.editorDiv.focus()}}onceAdded(t){if(!this.width){this.enableEditMode();t&&this.editorDiv.focus();this._initialOptions?.isCentered&&this.center();this._initialOptions=null}}isEmpty(){return!this.editorDiv||""===this.editorDiv.innerText.trim()}remove(){this.isEditing=!1;if(this.parent){this.parent.setEditingState(!0);this.parent.div.classList.add("freetextEditing")}super.remove()}#wr(){const t=[];this.editorDiv.normalize();let e=null;for(const i of this.editorDiv.childNodes)if(e?.nodeType!==Node.TEXT_NODE||"BR"!==i.nodeName){t.push(FreeTextEditor.#yr(i));e=i}return t.join("\n")}#vr(){const[t,e]=this.parentDimensions;let i;if(this.isAttachedToDOM)i=this.div.getBoundingClientRect();else{const{currentLayer:t,div:e}=this,n=e.style.display,s=e.classList.contains("hidden");e.classList.remove("hidden");e.style.display="hidden";t.div.append(this.div);i=e.getBoundingClientRect();e.remove();e.style.display=n;e.classList.toggle("hidden",s)}if(this.rotation%180==this.parentRotation%180){this.width=i.width/t;this.height=i.height/e}else{this.width=i.height/t;this.height=i.width/e}this.fixAndSetPosition()}commit(){if(!this.isInEditMode())return;super.commit();this.disableEditMode();const t=this.#pr,e=this.#pr=this.#wr().trimEnd();if(t===e)return;const setText=t=>{this.#pr=t;if(t){this.#Ar();this._uiManager.rebuild(this);this.#vr()}else this.remove()};this.addCommands({cmd:()=>{setText(e)},undo:()=>{setText(t)},mustExec:!1});this.#vr()}shouldGetKeyboardEvents(){return this.isInEditMode()}enterInEditMode(){this.enableEditMode();this.editorDiv.focus()}keydown(t){if(t.target===this.div&&"Enter"===t.key){this.enterInEditMode();t.preventDefault()}}editorDivKeydown(t){FreeTextEditor._keyboardManager.exec(this,t)}editorDivFocus(t){this.isEditing=!0}editorDivBlur(t){this.isEditing=!1}editorDivInput(t){this.parent.div.classList.toggle("freetextEditing",this.isEmpty())}disableEditing(){this.editorDiv.setAttribute("role","comment");this.editorDiv.removeAttribute("aria-multiline")}enableEditing(){this.editorDiv.setAttribute("role","textbox");this.editorDiv.setAttribute("aria-multiline",!0)}get canChangeContent(){return!0}render(){if(this.div)return this.div;let t,e;if(this._isCopy||this.annotationElementId){t=this.x;e=this.y}super.render();this.editorDiv=document.createElement("div");this.editorDiv.className="internal";this.editorDiv.setAttribute("id",this.#gr);this.editorDiv.setAttribute("data-l10n-id","pdfjs-free-text2");this.editorDiv.setAttribute("data-l10n-attrs","default-content");this.enableEditing();this.editorDiv.contentEditable=!0;const{style:i}=this.editorDiv;i.fontSize=`calc(${this.#Xs}px * var(--total-scale-factor))`;i.color=this.#Rs;this.div.append(this.editorDiv);this.overlayDiv=document.createElement("div");this.overlayDiv.classList.add("overlay","enabled");this.div.append(this.overlayDiv);if(this._isCopy||this.annotationElementId){const[i,n]=this.parentDimensions;if(this.annotationElementId){const{position:s}=this._initialData;let[r,a]=this.getInitialTranslation();[r,a]=this.pageTranslationToScreen(r,a);const[o,l]=this.pageDimensions,[h,c]=this.pageTranslation;let d,u;switch(this.rotation){case 0:d=t+(s[0]-h)/o;u=e+this.height-(s[1]-c)/l;break;case 90:d=t+(s[0]-h)/o;u=e-(s[1]-c)/l;[r,a]=[a,-r];break;case 180:d=t-this.width+(s[0]-h)/o;u=e-(s[1]-c)/l;[r,a]=[-r,-a];break;case 270:d=t+(s[0]-h-this.height*l)/o;u=e+(s[1]-c-this.width*o)/l;[r,a]=[-a,r]}this.setAt(d*i,u*n,r,a)}else this._moveAfterPaste(t,e);this.#Ar();this._isDraggable=!0;this.editorDiv.contentEditable=!1}else{this._isDraggable=!1;this.editorDiv.contentEditable=!0}return this.div}static#yr(t){return(t.nodeType===Node.TEXT_NODE?t.nodeValue:t.innerText).replaceAll(Dt,"")}editorDivPaste(t){const e=t.clipboardData||window.clipboardData,{types:i}=e;if(1===i.length&&"text/plain"===i[0])return;t.preventDefault();const n=FreeTextEditor.#xr(e.getData("text")||"").replaceAll(Dt,"\n");if(!n)return;const s=window.getSelection();if(!s.rangeCount)return;this.editorDiv.normalize();s.deleteFromDocument();const r=s.getRangeAt(0);if(!n.includes("\n")){r.insertNode(document.createTextNode(n));this.editorDiv.normalize();s.collapseToStart();return}const{startContainer:a,startOffset:o}=r,l=[],h=[];if(a.nodeType===Node.TEXT_NODE){const t=a.parentElement;h.push(a.nodeValue.slice(o).replaceAll(Dt,""));if(t!==this.editorDiv){let e=l;for(const i of this.editorDiv.childNodes)i!==t?e.push(FreeTextEditor.#yr(i)):e=h}l.push(a.nodeValue.slice(0,o).replaceAll(Dt,""))}else if(a===this.editorDiv){let t=l,e=0;for(const i of this.editorDiv.childNodes){e++===o&&(t=h);t.push(FreeTextEditor.#yr(i))}}this.#pr=`${l.join("\n")}${n}${h.join("\n")}`;this.#Ar();const c=new Range;let d=Math.sumPrecise(l.map((t=>t.length)));for(const{firstChild:t}of this.editorDiv.childNodes)if(t.nodeType===Node.TEXT_NODE){const e=t.nodeValue.length;if(d<=e){c.setStart(t,d);c.setEnd(t,d);break}d-=e}s.removeAllRanges();s.addRange(c)}#Ar(){this.editorDiv.replaceChildren();if(this.#pr)for(const t of this.#pr.split("\n")){const e=document.createElement("div");e.append(t?document.createTextNode(t):document.createElement("br"));this.editorDiv.append(e)}}#_r(){return this.#pr.replaceAll(" "," ")}static#xr(t){return t.replaceAll(" "," ")}get contentDiv(){return this.editorDiv}static async deserialize(t,e,i){let n=null;if(t instanceof FreeTextAnnotationElement){const{data:{defaultAppearanceData:{fontSize:e,fontColor:i},rect:s,rotation:r,id:a,popupRef:o,contentsObj:l},textContent:h,textPosition:c,parent:{page:{pageNumber:d}}}=t;if(!h||0===h.length)return null;n=t={annotationType:f.FREETEXT,color:Array.from(i),fontSize:e,value:h.join("\n"),position:c,pageIndex:d-1,rect:s.slice(0),rotation:r,annotationElementId:a,id:a,deleted:!1,popupRef:o,comment:l?.str||null}}const s=await super.deserialize(t,e,i);s.#Xs=t.fontSize;s.#Rs=Util.makeHexColor(...t.color);s.#pr=FreeTextEditor.#xr(t.value);s._initialData=n;t.comment&&s.setCommentData(t.comment);return s}serialize(t=!1){if(this.isEmpty())return null;if(this.deleted)return this.serializeDeleted();const e=FreeTextEditor._internalPadding*this.parentScale,i=this.getRect(e,e),n=AnnotationEditor._colorManager.convert(this.isAttachedToDOM?getComputedStyle(this.editorDiv).color:this.#Rs),s={annotationType:f.FREETEXT,color:n,fontSize:this.#Xs,value:this.#_r(),pageIndex:this.pageIndex,rect:i,rotation:this.rotation,structTreeParentId:this._structTreeParentId};this.addComment(s);if(t){s.isCopy=!0;return s}if(this.annotationElementId&&!this.#Er(s))return null;s.id=this.annotationElementId;return s}#Er(t){const{value:e,fontSize:i,color:n,pageIndex:s}=this._initialData;return this.hasEditedComment||this._hasBeenMoved||t.value!==e||t.fontSize!==i||t.color.some(((t,e)=>t!==n[e]))||t.pageIndex!==s}renderAnnotationElement(t){const e=super.renderAnnotationElement(t),{style:i}=e;i.fontSize=`calc(${this.#Xs}px * var(--total-scale-factor))`;i.color=this.#Rs;e.replaceChildren();for(const t of this.#pr.split("\n")){const i=document.createElement("div");i.append(t?document.createTextNode(t):document.createElement("br"));e.append(i)}const n=FreeTextEditor._internalPadding*this.parentScale,s={rect:this.getRect(n,n)};s.popup=this.hasEditedComment?this.comment:{text:this.#pr};t.updateEdited(s);return e}resetAnnotationElement(t){super.resetAnnotationElement(t);t.resetEdited()}}class Outline{static PRECISION=1e-4;toSVGPath(){unreachable("Abstract method `toSVGPath` must be implemented.")}get box(){unreachable("Abstract getter `box` must be implemented.")}serialize(t,e){unreachable("Abstract method `serialize` must be implemented.")}static _rescale(t,e,i,n,s,r){r||=new Float32Array(t.length);for(let a=0,o=t.length;a<o;a+=2){r[a]=e+t[a]*n;r[a+1]=i+t[a+1]*s}return r}static _rescaleAndSwap(t,e,i,n,s,r){r||=new Float32Array(t.length);for(let a=0,o=t.length;a<o;a+=2){r[a]=e+t[a+1]*n;r[a+1]=i+t[a]*s}return r}static _translate(t,e,i,n){n||=new Float32Array(t.length);for(let s=0,r=t.length;s<r;s+=2){n[s]=e+t[s];n[s+1]=i+t[s+1]}return n}static svgRound(t){return Math.round(1e4*t)}static _normalizePoint(t,e,i,n,s){switch(s){case 90:return[1-e/i,t/n];case 180:return[1-t/i,1-e/n];case 270:return[e/i,1-t/n];default:return[t/i,e/n]}}static _normalizePagePoint(t,e,i){switch(i){case 90:return[1-e,t];case 180:return[1-t,1-e];case 270:return[e,1-t];default:return[t,e]}}static createBezierPoints(t,e,i,n,s,r){return[(t+5*i)/6,(e+5*n)/6,(5*i+s)/6,(5*n+r)/6,(i+s)/2,(n+r)/2]}}class FreeDrawOutliner{#Sr;#Cr=[];#Tr;#Mr;#Dr=[];#Pr=new Float32Array(18);#kr;#Ir;#Rr;#Fr;#Lr;#Or;#Nr=[];static#Br=8;static#Ur=2;static#Hr=FreeDrawOutliner.#Br+FreeDrawOutliner.#Ur;constructor({x:t,y:e},i,n,s,r,a=0){this.#Sr=i;this.#Or=s*n;this.#Mr=r;this.#Pr.set([NaN,NaN,NaN,NaN,t,e],6);this.#Tr=a;this.#Fr=FreeDrawOutliner.#Br*n;this.#Rr=FreeDrawOutliner.#Hr*n;this.#Lr=n;this.#Nr.push(t,e)}isEmpty(){return isNaN(this.#Pr[8])}#zr(){const t=this.#Pr.subarray(4,6),e=this.#Pr.subarray(16,18),[i,n,s,r]=this.#Sr;return[(this.#kr+(t[0]-e[0])/2-i)/s,(this.#Ir+(t[1]-e[1])/2-n)/r,(this.#kr+(e[0]-t[0])/2-i)/s,(this.#Ir+(e[1]-t[1])/2-n)/r]}add({x:t,y:e}){this.#kr=t;this.#Ir=e;const[i,n,s,r]=this.#Sr;let[a,o,l,h]=this.#Pr.subarray(8,12);const c=t-l,d=e-h,u=Math.hypot(c,d);if(u<this.#Rr)return!1;const p=u-this.#Fr,g=p/u,f=g*c,m=g*d;let b=a,v=o;a=l;o=h;l+=f;h+=m;this.#Nr?.push(t,e);const w=f/p,y=-m/p*this.#Or,A=w*this.#Or;this.#Pr.set(this.#Pr.subarray(2,8),0);this.#Pr.set([l+y,h+A],4);this.#Pr.set(this.#Pr.subarray(14,18),12);this.#Pr.set([l-y,h-A],16);if(isNaN(this.#Pr[6])){if(0===this.#Dr.length){this.#Pr.set([a+y,o+A],2);this.#Dr.push(NaN,NaN,NaN,NaN,(a+y-i)/s,(o+A-n)/r);this.#Pr.set([a-y,o-A],14);this.#Cr.push(NaN,NaN,NaN,NaN,(a-y-i)/s,(o-A-n)/r)}this.#Pr.set([b,v,a,o,l,h],6);return!this.isEmpty()}this.#Pr.set([b,v,a,o,l,h],6);if(Math.abs(Math.atan2(v-o,b-a)-Math.atan2(m,f))<Math.PI/2){[a,o,l,h]=this.#Pr.subarray(2,6);this.#Dr.push(NaN,NaN,NaN,NaN,((a+l)/2-i)/s,((o+h)/2-n)/r);[a,o,b,v]=this.#Pr.subarray(14,18);this.#Cr.push(NaN,NaN,NaN,NaN,((b+a)/2-i)/s,((v+o)/2-n)/r);return!0}[b,v,a,o,l,h]=this.#Pr.subarray(0,6);this.#Dr.push(((b+5*a)/6-i)/s,((v+5*o)/6-n)/r,((5*a+l)/6-i)/s,((5*o+h)/6-n)/r,((a+l)/2-i)/s,((o+h)/2-n)/r);[l,h,a,o,b,v]=this.#Pr.subarray(12,18);this.#Cr.push(((b+5*a)/6-i)/s,((v+5*o)/6-n)/r,((5*a+l)/6-i)/s,((5*o+h)/6-n)/r,((a+l)/2-i)/s,((o+h)/2-n)/r);return!0}toSVGPath(){if(this.isEmpty())return"";const t=this.#Dr,e=this.#Cr;if(isNaN(this.#Pr[6])&&!this.isEmpty())return this.#jr();const i=[];i.push(`M${t[4]} ${t[5]}`);for(let e=6;e<t.length;e+=6)isNaN(t[e])?i.push(`L${t[e+4]} ${t[e+5]}`):i.push(`C${t[e]} ${t[e+1]} ${t[e+2]} ${t[e+3]} ${t[e+4]} ${t[e+5]}`);this.#Gr(i);for(let t=e.length-6;t>=6;t-=6)isNaN(e[t])?i.push(`L${e[t+4]} ${e[t+5]}`):i.push(`C${e[t]} ${e[t+1]} ${e[t+2]} ${e[t+3]} ${e[t+4]} ${e[t+5]}`);this.#Wr(i);return i.join(" ")}#jr(){const[t,e,i,n]=this.#Sr,[s,r,a,o]=this.#zr();return`M${(this.#Pr[2]-t)/i} ${(this.#Pr[3]-e)/n} L${(this.#Pr[4]-t)/i} ${(this.#Pr[5]-e)/n} L${s} ${r} L${a} ${o} L${(this.#Pr[16]-t)/i} ${(this.#Pr[17]-e)/n} L${(this.#Pr[14]-t)/i} ${(this.#Pr[15]-e)/n} Z`}#Wr(t){const e=this.#Cr;t.push(`L${e[4]} ${e[5]} Z`)}#Gr(t){const[e,i,n,s]=this.#Sr,r=this.#Pr.subarray(4,6),a=this.#Pr.subarray(16,18),[o,l,h,c]=this.#zr();t.push(`L${(r[0]-e)/n} ${(r[1]-i)/s} L${o} ${l} L${h} ${c} L${(a[0]-e)/n} ${(a[1]-i)/s}`)}newFreeDrawOutline(t,e,i,n,s,r){return new FreeDrawOutline(t,e,i,n,s,r)}getOutlines(){const t=this.#Dr,e=this.#Cr,i=this.#Pr,[n,s,r,a]=this.#Sr,o=new Float32Array((this.#Nr?.length??0)+2);for(let t=0,e=o.length-2;t<e;t+=2){o[t]=(this.#Nr[t]-n)/r;o[t+1]=(this.#Nr[t+1]-s)/a}o[o.length-2]=(this.#kr-n)/r;o[o.length-1]=(this.#Ir-s)/a;if(isNaN(i[6])&&!this.isEmpty())return this.#Vr(o);const l=new Float32Array(this.#Dr.length+24+this.#Cr.length);let h=t.length;for(let e=0;e<h;e+=2)if(isNaN(t[e]))l[e]=l[e+1]=NaN;else{l[e]=t[e];l[e+1]=t[e+1]}h=this.#$r(l,h);for(let t=e.length-6;t>=6;t-=6)for(let i=0;i<6;i+=2)if(isNaN(e[t+i])){l[h]=l[h+1]=NaN;h+=2}else{l[h]=e[t+i];l[h+1]=e[t+i+1];h+=2}this.#qr(l,h);return this.newFreeDrawOutline(l,o,this.#Sr,this.#Lr,this.#Tr,this.#Mr)}#Vr(t){const e=this.#Pr,[i,n,s,r]=this.#Sr,[a,o,l,h]=this.#zr(),c=new Float32Array(36);c.set([NaN,NaN,NaN,NaN,(e[2]-i)/s,(e[3]-n)/r,NaN,NaN,NaN,NaN,(e[4]-i)/s,(e[5]-n)/r,NaN,NaN,NaN,NaN,a,o,NaN,NaN,NaN,NaN,l,h,NaN,NaN,NaN,NaN,(e[16]-i)/s,(e[17]-n)/r,NaN,NaN,NaN,NaN,(e[14]-i)/s,(e[15]-n)/r],0);return this.newFreeDrawOutline(c,t,this.#Sr,this.#Lr,this.#Tr,this.#Mr)}#qr(t,e){const i=this.#Cr;t.set([NaN,NaN,NaN,NaN,i[4],i[5]],e);return e+6}#$r(t,e){const i=this.#Pr.subarray(4,6),n=this.#Pr.subarray(16,18),[s,r,a,o]=this.#Sr,[l,h,c,d]=this.#zr();t.set([NaN,NaN,NaN,NaN,(i[0]-s)/a,(i[1]-r)/o,NaN,NaN,NaN,NaN,l,h,NaN,NaN,NaN,NaN,c,d,NaN,NaN,NaN,NaN,(n[0]-s)/a,(n[1]-r)/o],e);return e+24}}class FreeDrawOutline extends Outline{#Sr;#Xr=new Float32Array(4);#Tr;#Mr;#Nr;#Lr;#Kr;constructor(t,e,i,n,s,r){super();this.#Kr=t;this.#Nr=e;this.#Sr=i;this.#Lr=n;this.#Tr=s;this.#Mr=r;this.lastPoint=[NaN,NaN];this.#Yr(r);const[a,o,l,h]=this.#Xr;for(let e=0,i=t.length;e<i;e+=2){t[e]=(t[e]-a)/l;t[e+1]=(t[e+1]-o)/h}for(let t=0,i=e.length;t<i;t+=2){e[t]=(e[t]-a)/l;e[t+1]=(e[t+1]-o)/h}}toSVGPath(){const t=[`M${this.#Kr[4]} ${this.#Kr[5]}`];for(let e=6,i=this.#Kr.length;e<i;e+=6)isNaN(this.#Kr[e])?t.push(`L${this.#Kr[e+4]} ${this.#Kr[e+5]}`):t.push(`C${this.#Kr[e]} ${this.#Kr[e+1]} ${this.#Kr[e+2]} ${this.#Kr[e+3]} ${this.#Kr[e+4]} ${this.#Kr[e+5]}`);t.push("Z");return t.join(" ")}serialize([t,e,i,n],s){const r=i-t,a=n-e;let o,l;switch(s){case 0:o=Outline._rescale(this.#Kr,t,n,r,-a);l=Outline._rescale(this.#Nr,t,n,r,-a);break;case 90:o=Outline._rescaleAndSwap(this.#Kr,t,e,r,a);l=Outline._rescaleAndSwap(this.#Nr,t,e,r,a);break;case 180:o=Outline._rescale(this.#Kr,i,e,-r,a);l=Outline._rescale(this.#Nr,i,e,-r,a);break;case 270:o=Outline._rescaleAndSwap(this.#Kr,i,n,-r,-a);l=Outline._rescaleAndSwap(this.#Nr,i,n,-r,-a)}return{outline:Array.from(o),points:[Array.from(l)]}}#Yr(t){const e=this.#Kr;let i=e[4],n=e[5];const s=[i,n,i,n];let r=i,a=n;const o=t?Math.max:Math.min;for(let t=6,l=e.length;t<l;t+=6){const l=e[t+4],h=e[t+5];if(isNaN(e[t])){Util.pointBoundingBox(l,h,s);if(a<h){r=l;a=h}else a===h&&(r=o(r,l))}else{const l=[1/0,1/0,-1/0,-1/0];Util.bezierBoundingBox(i,n,...e.slice(t,t+6),l);Util.rectBoundingBox(...l,s);if(a<l[3]){r=l[2];a=l[3]}else a===l[3]&&(r=o(r,l[2]))}i=l;n=h}const l=this.#Xr;l[0]=s[0]-this.#Tr;l[1]=s[1]-this.#Tr;l[2]=s[2]-s[0]+2*this.#Tr;l[3]=s[3]-s[1]+2*this.#Tr;this.lastPoint=[r,a]}get box(){return this.#Xr}newOutliner(t,e,i,n,s,r=0){return new FreeDrawOutliner(t,e,i,n,s,r)}getNewOutline(t,e){const[i,n,s,r]=this.#Xr,[a,o,l,h]=this.#Sr,c=s*l,d=r*h,u=i*l+a,p=n*h+o,g=this.newOutliner({x:this.#Nr[0]*c+u,y:this.#Nr[1]*d+p},this.#Sr,this.#Lr,t,this.#Mr,e??this.#Tr);for(let t=2;t<this.#Nr.length;t+=2)g.add({x:this.#Nr[t]*c+u,y:this.#Nr[t+1]*d+p});return g.getOutlines()}}class HighlightOutliner{#Sr;#Qr;#Jr=[];#Zr=[];constructor(t,e=0,i=0,n=!0){const s=[1/0,1/0,-1/0,-1/0],r=10**-4;for(const{x:i,y:n,width:a,height:o}of t){const t=Math.floor((i-e)/r)*r,l=Math.ceil((i+a+e)/r)*r,h=Math.floor((n-e)/r)*r,c=Math.ceil((n+o+e)/r)*r,d=[t,h,c,!0],u=[l,h,c,!1];this.#Jr.push(d,u);Util.rectBoundingBox(t,h,l,c,s)}const a=s[2]-s[0]+2*i,o=s[3]-s[1]+2*i,l=s[0]-i,h=s[1]-i,c=this.#Jr.at(n?-1:-2),d=[c[0],c[2]];for(const t of this.#Jr){const[e,i,n]=t;t[0]=(e-l)/a;t[1]=(i-h)/o;t[2]=(n-h)/o}this.#Sr=new Float32Array([l,h,a,o]);this.#Qr=d}getOutlines(){this.#Jr.sort(((t,e)=>t[0]-e[0]||t[1]-e[1]||t[2]-e[2]));const t=[];for(const e of this.#Jr)if(e[3]){t.push(...this.#ta(e));this.#ea(e)}else{this.#ia(e);t.push(...this.#ta(e))}return this.#na(t)}#na(t){const e=[],i=new Set;for(const i of t){const[t,n,s]=i;e.push([t,n,i],[t,s,i])}e.sort(((t,e)=>t[1]-e[1]||t[0]-e[0]));for(let t=0,n=e.length;t<n;t+=2){const n=e[t][2],s=e[t+1][2];n.push(s);s.push(n);i.add(n);i.add(s)}const n=[];let s;for(;i.size>0;){const t=i.values().next().value;let[e,r,a,o,l]=t;i.delete(t);let h=e,c=r;s=[e,a];n.push(s);for(;;){let t;if(i.has(o))t=o;else{if(!i.has(l))break;t=l}i.delete(t);[e,r,a,o,l]=t;if(h!==e){s.push(h,c,e,c===r?r:a);h=e}c=c===r?a:r}s.push(h,c)}return new HighlightOutline(n,this.#Sr,this.#Qr)}#sa(t){const e=this.#Zr;let i=0,n=e.length-1;for(;i<=n;){const s=i+n>>1,r=e[s][0];if(r===t)return s;r<t?i=s+1:n=s-1}return n+1}#ea([,t,e]){const i=this.#sa(t);this.#Zr.splice(i,0,[t,e])}#ia([,t,e]){const i=this.#sa(t);for(let n=i;n<this.#Zr.length;n++){const[i,s]=this.#Zr[n];if(i!==t)break;if(i===t&&s===e){this.#Zr.splice(n,1);return}}for(let n=i-1;n>=0;n--){const[i,s]=this.#Zr[n];if(i!==t)break;if(i===t&&s===e){this.#Zr.splice(n,1);return}}}#ta(t){const[e,i,n]=t,s=[[e,i,n]],r=this.#sa(n);for(let t=0;t<r;t++){const[i,n]=this.#Zr[t];for(let t=0,r=s.length;t<r;t++){const[,a,o]=s[t];if(!(n<=a||o<=i))if(a>=i)if(o>n)s[t][1]=n;else{if(1===r)return[];s.splice(t,1);t--;r--}else{s[t][2]=i;o>n&&s.push([e,n,o])}}}return s}}class HighlightOutline extends Outline{#Sr;#ra;constructor(t,e,i){super();this.#ra=t;this.#Sr=e;this.lastPoint=i}toSVGPath(){const t=[];for(const e of this.#ra){let[i,n]=e;t.push(`M${i} ${n}`);for(let s=2;s<e.length;s+=2){const r=e[s],a=e[s+1];if(r===i){t.push(`V${a}`);n=a}else if(a===n){t.push(`H${r}`);i=r}}t.push("Z")}return t.join(" ")}serialize([t,e,i,n],s){const r=[],a=i-t,o=n-e;for(const e of this.#ra){const i=new Array(e.length);for(let s=0;s<e.length;s+=2){i[s]=t+e[s]*a;i[s+1]=n-e[s+1]*o}r.push(i)}return r}get box(){return this.#Sr}get classNamesForOutlining(){return["highlightOutline"]}}class FreeHighlightOutliner extends FreeDrawOutliner{newFreeDrawOutline(t,e,i,n,s,r){return new FreeHighlightOutline(t,e,i,n,s,r)}}class FreeHighlightOutline extends FreeDrawOutline{newOutliner(t,e,i,n,s,r=0){return new FreeHighlightOutliner(t,e,i,n,s,r)}}class HighlightEditor extends AnnotationEditor{#aa=null;#oa=0;#la;#ha=null;#s=null;#ca=null;#da=null;#ua=0;#pa=null;#ga=null;#y=null;#fa=!1;#Qr=null;#ma;#ba=null;#se="";#Or;#va="";static _defaultColor=null;static _defaultOpacity=1;static _defaultThickness=12;static _type="highlight";static _editorType=f.HIGHLIGHT;static _freeHighlightId=-1;static _freeHighlight=null;static _freeHighlightClipId="";static get _keyboardManager(){const t=HighlightEditor.prototype;return shadow(this,"_keyboardManager",new KeyboardManager([[["ArrowLeft","mac+ArrowLeft"],t._moveCaret,{args:[0]}],[["ArrowRight","mac+ArrowRight"],t._moveCaret,{args:[1]}],[["ArrowUp","mac+ArrowUp"],t._moveCaret,{args:[2]}],[["ArrowDown","mac+ArrowDown"],t._moveCaret,{args:[3]}]]))}constructor(t){super({...t,name:"highlightEditor"});this.color=t.color||HighlightEditor._defaultColor;this.#Or=t.thickness||HighlightEditor._defaultThickness;this.#ma=t.opacity||HighlightEditor._defaultOpacity;this.#la=t.boxes||null;this.#va=t.methodOfCreation||"";this.#se=t.text||"";this._isDraggable=!1;this.defaultL10nId="pdfjs-editor-highlight-editor";if(t.highlightId>-1){this.#fa=!0;this.#wa(t);this.#ya()}else if(this.#la){this.#aa=t.anchorNode;this.#oa=t.anchorOffset;this.#da=t.focusNode;this.#ua=t.focusOffset;this.#Aa();this.#ya();this.rotate(this.rotation)}this.annotationElementId||this._uiManager.a11yAlert("pdfjs-editor-highlight-added-alert")}get telemetryInitialData(){return{action:"added",type:this.#fa?"free_highlight":"highlight",color:this._uiManager.getNonHCMColorName(this.color),thickness:this.#Or,methodOfCreation:this.#va}}get telemetryFinalData(){return{type:"highlight",color:this._uiManager.getNonHCMColorName(this.color)}}get commentColor(){return this.color}static computeTelemetryFinalData(t){return{numberOfColors:t.get("color").size}}#Aa(){const t=new HighlightOutliner(this.#la,.001);this.#ga=t.getOutlines();[this.x,this.y,this.width,this.height]=this.#ga.box;const e=new HighlightOutliner(this.#la,.0025,.001,"ltr"===this._uiManager.direction);this.#ca=e.getOutlines();const{lastPoint:i}=this.#ca;this.#Qr=[(i[0]-this.x)/this.width,(i[1]-this.y)/this.height]}#wa({highlightOutlines:t,highlightId:e,clipPathId:i}){this.#ga=t;this.#ca=t.getNewOutline(this.#Or/2****,.0025);if(e>=0){this.#y=e;this.#ha=i;this.parent.drawLayer.finalizeDraw(e,{bbox:t.box,path:{d:t.toSVGPath()}});this.#ba=this.parent.drawLayer.drawOutline({rootClass:{highlightOutline:!0,free:!0},bbox:this.#ca.box,path:{d:this.#ca.toSVGPath()}},!0)}else if(this.parent){const e=this.parent.viewport.rotation;this.parent.drawLayer.updateProperties(this.#y,{bbox:HighlightEditor.#xa(this.#ga.box,(e-this.rotation+360)%360),path:{d:t.toSVGPath()}});this.parent.drawLayer.updateProperties(this.#ba,{bbox:HighlightEditor.#xa(this.#ca.box,e),path:{d:this.#ca.toSVGPath()}})}const[n,s,r,a]=t.box;switch(this.rotation){case 0:this.x=n;this.y=s;this.width=r;this.height=a;break;case 90:{const[t,e]=this.parentDimensions;this.x=s;this.y=1-n;this.width=r*e/t;this.height=a*t/e;break}case 180:this.x=1-n;this.y=1-s;this.width=r;this.height=a;break;case 270:{const[t,e]=this.parentDimensions;this.x=1-s;this.y=n;this.width=r*e/t;this.height=a*t/e;break}}const{lastPoint:o}=this.#ca;this.#Qr=[(o[0]-n)/r,(o[1]-s)/a]}static initialize(t,e){AnnotationEditor.initialize(t,e);HighlightEditor._defaultColor||=e.highlightColors?.values().next().value||"#fff066"}static updateDefaultParams(t,e){switch(t){case m.HIGHLIGHT_COLOR:HighlightEditor._defaultColor=e;break;case m.HIGHLIGHT_THICKNESS:HighlightEditor._defaultThickness=e}}translateInPage(t,e){}get toolbarPosition(){return this.#Qr}updateParams(t,e){switch(t){case m.HIGHLIGHT_COLOR:this.#br(e);break;case m.HIGHLIGHT_THICKNESS:this.#_a(e)}}static get defaultPropertiesToUpdate(){return[[m.HIGHLIGHT_COLOR,HighlightEditor._defaultColor],[m.HIGHLIGHT_THICKNESS,HighlightEditor._defaultThickness]]}get propertiesToUpdate(){return[[m.HIGHLIGHT_COLOR,this.color||HighlightEditor._defaultColor],[m.HIGHLIGHT_THICKNESS,this.#Or||HighlightEditor._defaultThickness],[m.HIGHLIGHT_FREE,this.#fa]]}#br(t){const setColorAndOpacity=(t,e)=>{this.color=t;this.#ma=e;this.parent?.drawLayer.updateProperties(this.#y,{root:{fill:t,"fill-opacity":e}});this.#s?.updateColor(t)},e=this.color,i=this.#ma;this.addCommands({cmd:setColorAndOpacity.bind(this,t,HighlightEditor._defaultOpacity),undo:setColorAndOpacity.bind(this,e,i),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:m.HIGHLIGHT_COLOR,overwriteIfSameType:!0,keepUndo:!0});this._reportTelemetry({action:"color_changed",color:this._uiManager.getNonHCMColorName(t)},!0)}#_a(t){const e=this.#Or,setThickness=t=>{this.#Or=t;this.#Ea(t)};this.addCommands({cmd:setThickness.bind(this,t),undo:setThickness.bind(this,e),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:m.INK_THICKNESS,overwriteIfSameType:!0,keepUndo:!0});this._reportTelemetry({action:"thickness_changed",thickness:t},!0)}get toolbarButtons(){if(this._uiManager.highlightColors){return[["colorPicker",this.#s=new ColorPicker({editor:this})]]}return super.toolbarButtons}disableEditing(){super.disableEditing();this.div.classList.toggle("disabled",!0)}enableEditing(){super.enableEditing();this.div.classList.toggle("disabled",!1)}fixAndSetPosition(){return super.fixAndSetPosition(this.#Sa())}getBaseTranslation(){return[0,0]}getRect(t,e){return super.getRect(t,e,this.#Sa())}onceAdded(t){this.annotationElementId||this.parent.addUndoableEditor(this);t&&this.div.focus()}remove(){this.#Ca();this._reportTelemetry({action:"deleted"});super.remove()}rebuild(){if(this.parent){super.rebuild();if(null!==this.div){this.#ya();this.isAttachedToDOM||this.parent.add(this)}}}setParent(t){let e=!1;if(this.parent&&!t)this.#Ca();else if(t){this.#ya(t);e=!this.parent&&this.div?.classList.contains("selectedEditor")}super.setParent(t);this.show(this._isVisible);e&&this.select()}#Ea(t){if(!this.#fa)return;this.#wa({highlightOutlines:this.#ga.getNewOutline(t/2)});this.fixAndSetPosition();const[e,i]=this.parentDimensions;this.setDims(this.width*e,this.height*i)}#Ca(){if(null!==this.#y&&this.parent){this.parent.drawLayer.remove(this.#y);this.#y=null;this.parent.drawLayer.remove(this.#ba);this.#ba=null}}#ya(t=this.parent){if(null===this.#y){({id:this.#y,clipPathId:this.#ha}=t.drawLayer.draw({bbox:this.#ga.box,root:{viewBox:"0 0 1 1",fill:this.color,"fill-opacity":this.#ma},rootClass:{highlight:!0,free:this.#fa},path:{d:this.#ga.toSVGPath()}},!1,!0));this.#ba=t.drawLayer.drawOutline({rootClass:{highlightOutline:!0,free:this.#fa},bbox:this.#ca.box,path:{d:this.#ca.toSVGPath()}},this.#fa);this.#pa&&(this.#pa.style.clipPath=this.#ha)}}static#xa([t,e,i,n],s){switch(s){case 90:return[1-e-n,t,n,i];case 180:return[1-t-i,1-e-n,i,n];case 270:return[e,1-t-i,n,i]}return[t,e,i,n]}rotate(t){const{drawLayer:e}=this.parent;let i;if(this.#fa){t=(t-this.rotation+360)%360;i=HighlightEditor.#xa(this.#ga.box,t)}else i=HighlightEditor.#xa([this.x,this.y,this.width,this.height],t);e.updateProperties(this.#y,{bbox:i,root:{"data-main-rotation":t}});e.updateProperties(this.#ba,{bbox:HighlightEditor.#xa(this.#ca.box,t),root:{"data-main-rotation":t}})}render(){if(this.div)return this.div;const t=super.render();if(this.#se){t.setAttribute("aria-label",this.#se);t.setAttribute("role","mark")}this.#fa?t.classList.add("free"):this.div.addEventListener("keydown",this.#Ta.bind(this),{signal:this._uiManager._signal});const e=this.#pa=document.createElement("div");t.append(e);e.setAttribute("aria-hidden","true");e.className="internal";e.style.clipPath=this.#ha;const[i,n]=this.parentDimensions;this.setDims(this.width*i,this.height*n);bindEvents(this,this.#pa,["pointerover","pointerleave"]);this.enableEditing();return t}pointerover(){this.isSelected||this.parent?.drawLayer.updateProperties(this.#ba,{rootClass:{hovered:!0}})}pointerleave(){this.isSelected||this.parent?.drawLayer.updateProperties(this.#ba,{rootClass:{hovered:!1}})}#Ta(t){HighlightEditor._keyboardManager.exec(this,t)}_moveCaret(t){this.parent.unselect(this);switch(t){case 0:case 2:this.#Ma(!0);break;case 1:case 3:this.#Ma(!1)}}#Ma(t){if(!this.#aa)return;const e=window.getSelection();t?e.setPosition(this.#aa,this.#oa):e.setPosition(this.#da,this.#ua)}select(){super.select();this.#ba&&this.parent?.drawLayer.updateProperties(this.#ba,{rootClass:{hovered:!1,selected:!0}})}unselect(){super.unselect();if(this.#ba){this.parent?.drawLayer.updateProperties(this.#ba,{rootClass:{selected:!1}});this.#fa||this.#Ma(!1)}}get _mustFixPosition(){return!this.#fa}show(t=this._isVisible){super.show(t);if(this.parent){this.parent.drawLayer.updateProperties(this.#y,{rootClass:{hidden:!t}});this.parent.drawLayer.updateProperties(this.#ba,{rootClass:{hidden:!t}})}}#Sa(){return this.#fa?this.rotation:0}#Da(){if(this.#fa)return null;const[t,e]=this.pageDimensions,[i,n]=this.pageTranslation,s=this.#la,r=new Float32Array(8*s.length);let a=0;for(const{x:o,y:l,width:h,height:c}of s){const s=o*t+i,d=(1-l)*e+n;r[a]=r[a+4]=s;r[a+1]=r[a+3]=d;r[a+2]=r[a+6]=s+h*t;r[a+5]=r[a+7]=d-c*e;a+=8}return r}#Pa(t){return this.#ga.serialize(t,this.#Sa())}static startHighlighting(t,e,{target:i,x:n,y:s}){const{x:r,y:a,width:o,height:l}=i.getBoundingClientRect(),h=new AbortController,c=t.combinedSignal(h),pointerUpCallback=e=>{h.abort();this.#ka(t,e)};window.addEventListener("blur",pointerUpCallback,{signal:c});window.addEventListener("pointerup",pointerUpCallback,{signal:c});window.addEventListener("pointerdown",stopEvent,{capture:!0,passive:!1,signal:c});window.addEventListener("contextmenu",noContextMenu,{signal:c});i.addEventListener("pointermove",this.#Ia.bind(this,t),{signal:c});this._freeHighlight=new FreeHighlightOutliner({x:n,y:s},[r,a,o,l],t.scale,this._defaultThickness/2,e,.001);({id:this._freeHighlightId,clipPathId:this._freeHighlightClipId}=t.drawLayer.draw({bbox:[0,0,1,1],root:{viewBox:"0 0 1 1",fill:this._defaultColor,"fill-opacity":this._defaultOpacity},rootClass:{highlight:!0,free:!0},path:{d:this._freeHighlight.toSVGPath()}},!0,!0))}static#Ia(t,e){this._freeHighlight.add(e)&&t.drawLayer.updateProperties(this._freeHighlightId,{path:{d:this._freeHighlight.toSVGPath()}})}static#ka(t,e){this._freeHighlight.isEmpty()?t.drawLayer.remove(this._freeHighlightId):t.createAndAddNewEditor(e,!1,{highlightId:this._freeHighlightId,highlightOutlines:this._freeHighlight.getOutlines(),clipPathId:this._freeHighlightClipId,methodOfCreation:"main_toolbar"});this._freeHighlightId=-1;this._freeHighlight=null;this._freeHighlightClipId=""}static async deserialize(t,e,i){let n=null;if(t instanceof HighlightAnnotationElement){const{data:{quadPoints:e,rect:i,rotation:s,id:r,color:a,opacity:o,popupRef:l,contentsObj:h},parent:{page:{pageNumber:c}}}=t;n=t={annotationType:f.HIGHLIGHT,color:Array.from(a),opacity:o,quadPoints:e,boxes:null,pageIndex:c-1,rect:i.slice(0),rotation:s,annotationElementId:r,id:r,deleted:!1,popupRef:l,comment:h?.str||null}}else if(t instanceof InkAnnotationElement){const{data:{inkLists:e,rect:i,rotation:s,id:r,color:a,borderStyle:{rawWidth:o},popupRef:l,contentsObj:h},parent:{page:{pageNumber:c}}}=t;n=t={annotationType:f.HIGHLIGHT,color:Array.from(a),thickness:o,inkLists:e,boxes:null,pageIndex:c-1,rect:i.slice(0),rotation:s,annotationElementId:r,id:r,deleted:!1,popupRef:l,comment:h?.str||null}}const{color:s,quadPoints:r,inkLists:a,opacity:o}=t,l=await super.deserialize(t,e,i);l.color=Util.makeHexColor(...s);l.#ma=o||1;a&&(l.#Or=t.thickness);l._initialData=n;t.comment&&l.setCommentData(t.comment);const[h,c]=l.pageDimensions,[d,u]=l.pageTranslation;if(r){const t=l.#la=[];for(let e=0;e<r.length;e+=8)t.push({x:(r[e]-d)/h,y:1-(r[e+1]-u)/c,width:(r[e+2]-r[e])/h,height:(r[e+1]-r[e+5])/c});l.#Aa();l.#ya();l.rotate(l.rotation)}else if(a){l.#fa=!0;const t=a[0],i={x:t[0]-d,y:c-(t[1]-u)},n=new FreeHighlightOutliner(i,[0,0,h,c],1,l.#Or/2,!0,.001);for(let e=0,s=t.length;e<s;e+=2){i.x=t[e]-d;i.y=c-(t[e+1]-u);n.add(i)}const{id:s,clipPathId:r}=e.drawLayer.draw({bbox:[0,0,1,1],root:{viewBox:"0 0 1 1",fill:l.color,"fill-opacity":l._defaultOpacity},rootClass:{highlight:!0,free:!0},path:{d:n.toSVGPath()}},!0,!0);l.#wa({highlightOutlines:n.getOutlines(),highlightId:s,clipPathId:r});l.#ya();l.rotate(l.parentRotation)}return l}serialize(t=!1){if(this.isEmpty()||t)return null;if(this.deleted)return this.serializeDeleted();const e=this.getRect(0,0),i=AnnotationEditor._colorManager.convert(this._uiManager.getNonHCMColor(this.color)),n={annotationType:f.HIGHLIGHT,color:i,opacity:this.#ma,thickness:this.#Or,quadPoints:this.#Da(),outlines:this.#Pa(e),pageIndex:this.pageIndex,rect:e,rotation:this.#Sa(),structTreeParentId:this._structTreeParentId};this.addComment(n);if(this.annotationElementId&&!this.#Er(n))return null;n.id=this.annotationElementId;return n}#Er(t){const{color:e}=this._initialData;return this.hasEditedComment||t.color.some(((t,i)=>t!==e[i]))}renderAnnotationElement(t){const e={rect:this.getRect(0,0)};this.hasEditedComment&&(e.popup=this.comment);t.updateEdited(e);return null}static canCreateNewEmptyEditor(){return!1}}class DrawingOptions{#Ra=Object.create(null);updateProperty(t,e){this[t]=e;this.updateSVGProperty(t,e)}updateProperties(t){if(t)for(const[e,i]of Object.entries(t))e.startsWith("_")||this.updateProperty(e,i)}updateSVGProperty(t,e){this.#Ra[t]=e}toSVGProperties(){const t=this.#Ra;this.#Ra=Object.create(null);return{root:t}}reset(){this.#Ra=Object.create(null)}updateAll(t=this){this.updateProperties(t)}clone(){unreachable("Not implemented")}}class DrawingEditor extends AnnotationEditor{#Fa=null;#La;_colorPicker=null;_drawId=null;static _currentDrawId=-1;static _currentParent=null;static#Oa=null;static#Na=null;static#Ba=null;static#Ua=NaN;static#Ha=null;static#za=null;static#ja=NaN;static _INNER_MARGIN=3;constructor(t){super(t);this.#La=t.mustBeCommitted||!1;this._addOutlines(t)}_addOutlines(t){if(t.drawOutlines){this.#Ga(t);this.#ya()}}#Ga({drawOutlines:t,drawId:e,drawingOptions:i}){this.#Fa=t;this._drawingOptions||=i;this.annotationElementId||this._uiManager.a11yAlert(`pdfjs-editor-${this.editorType}-added-alert`);if(e>=0){this._drawId=e;this.parent.drawLayer.finalizeDraw(e,t.defaultProperties)}else this._drawId=this.#Wa(t,this.parent);this.#Va(t.box)}#Wa(t,e){const{id:i}=e.drawLayer.draw(DrawingEditor._mergeSVGProperties(this._drawingOptions.toSVGProperties(),t.defaultSVGProperties),!1,!1);return i}static _mergeSVGProperties(t,e){const i=new Set(Object.keys(t));for(const[n,s]of Object.entries(e))i.has(n)?Object.assign(t[n],s):t[n]=s;return t}static getDefaultDrawingOptions(t){unreachable("Not implemented")}static get typesMap(){unreachable("Not implemented")}static get isDrawer(){return!0}static get supportMultipleDrawings(){return!1}static updateDefaultParams(t,e){const i=this.typesMap.get(t);i&&this._defaultDrawingOptions.updateProperty(i,e);if(this._currentParent){DrawingEditor.#Oa.updateProperty(i,e);this._currentParent.drawLayer.updateProperties(this._currentDrawId,this._defaultDrawingOptions.toSVGProperties())}}updateParams(t,e){const i=this.constructor.typesMap.get(t);i&&this._updateProperty(t,i,e)}static get defaultPropertiesToUpdate(){const t=[],e=this._defaultDrawingOptions;for(const[i,n]of this.typesMap)t.push([i,e[n]]);return t}get propertiesToUpdate(){const t=[],{_drawingOptions:e}=this;for(const[i,n]of this.constructor.typesMap)t.push([i,e[n]]);return t}_updateProperty(t,e,i){const n=this._drawingOptions,s=n[e],setter=i=>{n.updateProperty(e,i);const s=this.#Fa.updateProperty(e,i);s&&this.#Va(s);this.parent?.drawLayer.updateProperties(this._drawId,n.toSVGProperties());t===this.colorType&&this._colorPicker?.update(i)};this.addCommands({cmd:setter.bind(this,i),undo:setter.bind(this,s),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:t,overwriteIfSameType:!0,keepUndo:!0})}_onResizing(){this.parent?.drawLayer.updateProperties(this._drawId,DrawingEditor._mergeSVGProperties(this.#Fa.getPathResizingSVGProperties(this.#$a()),{bbox:this.#qa()}))}_onResized(){this.parent?.drawLayer.updateProperties(this._drawId,DrawingEditor._mergeSVGProperties(this.#Fa.getPathResizedSVGProperties(this.#$a()),{bbox:this.#qa()}))}_onTranslating(t,e){this.parent?.drawLayer.updateProperties(this._drawId,{bbox:this.#qa()})}_onTranslated(){this.parent?.drawLayer.updateProperties(this._drawId,DrawingEditor._mergeSVGProperties(this.#Fa.getPathTranslatedSVGProperties(this.#$a(),this.parentDimensions),{bbox:this.#qa()}))}_onStartDragging(){this.parent?.drawLayer.updateProperties(this._drawId,{rootClass:{moving:!0}})}_onStopDragging(){this.parent?.drawLayer.updateProperties(this._drawId,{rootClass:{moving:!1}})}commit(){super.commit();this.disableEditMode();this.disableEditing()}disableEditing(){super.disableEditing();this.div.classList.toggle("disabled",!0)}enableEditing(){super.enableEditing();this.div.classList.toggle("disabled",!1)}getBaseTranslation(){return[0,0]}get isResizable(){return!0}onceAdded(t){this.annotationElementId||this.parent.addUndoableEditor(this);this._isDraggable=!0;if(this.#La){this.#La=!1;this.commit();this.parent.setSelected(this);t&&this.isOnScreen&&this.div.focus()}}remove(){this.#Ca();super.remove()}rebuild(){if(this.parent){super.rebuild();if(null!==this.div){this.#ya();this.#Va(this.#Fa.box);this.isAttachedToDOM||this.parent.add(this)}}}setParent(t){let e=!1;if(this.parent&&!t){this._uiManager.removeShouldRescale(this);this.#Ca()}else if(t){this._uiManager.addShouldRescale(this);this.#ya(t);e=!this.parent&&this.div?.classList.contains("selectedEditor")}super.setParent(t);e&&this.select()}#Ca(){if(null!==this._drawId&&this.parent){this.parent.drawLayer.remove(this._drawId);this._drawId=null;this._drawingOptions.reset()}}#ya(t=this.parent){if(null===this._drawId||this.parent!==t)if(null===this._drawId){this._drawingOptions.updateAll();this._drawId=this.#Wa(this.#Fa,t)}else this.parent.drawLayer.updateParent(this._drawId,t.drawLayer)}#Xa([t,e,i,n]){const{parentDimensions:[s,r],rotation:a}=this;switch(a){case 90:return[e,1-t,i*(r/s),n*(s/r)];case 180:return[1-t,1-e,i,n];case 270:return[1-e,t,i*(r/s),n*(s/r)];default:return[t,e,i,n]}}#$a(){const{x:t,y:e,width:i,height:n,parentDimensions:[s,r],rotation:a}=this;switch(a){case 90:return[1-e,t,i*(s/r),n*(r/s)];case 180:return[1-t,1-e,i,n];case 270:return[e,1-t,i*(s/r),n*(r/s)];default:return[t,e,i,n]}}#Va(t){[this.x,this.y,this.width,this.height]=this.#Xa(t);if(this.div){this.fixAndSetPosition();const[t,e]=this.parentDimensions;this.setDims(this.width*t,this.height*e)}this._onResized()}#qa(){const{x:t,y:e,width:i,height:n,rotation:s,parentRotation:r,parentDimensions:[a,o]}=this;switch((4*s+r)/90){case 1:return[1-e-n,t,n,i];case 2:return[1-t-i,1-e-n,i,n];case 3:return[e,1-t-i,n,i];case 4:return[t,e-i*(a/o),n*(o/a),i*(a/o)];case 5:return[1-e,t,i*(a/o),n*(o/a)];case 6:return[1-t-n*(o/a),1-e,n*(o/a),i*(a/o)];case 7:return[e-i*(a/o),1-t-n*(o/a),i*(a/o),n*(o/a)];case 8:return[t-i,e-n,i,n];case 9:return[1-e,t-i,n,i];case 10:return[1-t,1-e,i,n];case 11:return[e-n,1-t,n,i];case 12:return[t-n*(o/a),e,n*(o/a),i*(a/o)];case 13:return[1-e-i*(a/o),t-n*(o/a),i*(a/o),n*(o/a)];case 14:return[1-t,1-e-i*(a/o),n*(o/a),i*(a/o)];case 15:return[e,1-t,i*(a/o),n*(o/a)];default:return[t,e,i,n]}}rotate(){this.parent&&this.parent.drawLayer.updateProperties(this._drawId,DrawingEditor._mergeSVGProperties({bbox:this.#qa()},this.#Fa.updateRotation((this.parentRotation-this.rotation+360)%360)))}onScaleChanging(){this.parent&&this.#Va(this.#Fa.updateParentDimensions(this.parentDimensions,this.parent.scale))}static onScaleChangingWhenDrawing(){}render(){if(this.div)return this.div;let t,e;if(this._isCopy){t=this.x;e=this.y}const i=super.render();i.classList.add("draw");const n=document.createElement("div");i.append(n);n.setAttribute("aria-hidden","true");n.className="internal";const[s,r]=this.parentDimensions;this.setDims(this.width*s,this.height*r);this._uiManager.addShouldRescale(this);this.disableEditing();this._isCopy&&this._moveAfterPaste(t,e);return i}static createDrawerInstance(t,e,i,n,s){unreachable("Not implemented")}static startDrawing(t,e,i,n){const{target:s,offsetX:r,offsetY:a,pointerId:o,pointerType:l}=n;if(DrawingEditor.#Ha&&DrawingEditor.#Ha!==l)return;const{viewport:{rotation:h}}=t,{width:c,height:d}=s.getBoundingClientRect(),u=DrawingEditor.#Na=new AbortController,p=t.combinedSignal(u);DrawingEditor.#Ua||=o;DrawingEditor.#Ha??=l;window.addEventListener("pointerup",(t=>{DrawingEditor.#Ua===t.pointerId?this._endDraw(t):DrawingEditor.#za?.delete(t.pointerId)}),{signal:p});window.addEventListener("pointercancel",(t=>{DrawingEditor.#Ua===t.pointerId?this._currentParent.endDrawingSession():DrawingEditor.#za?.delete(t.pointerId)}),{signal:p});window.addEventListener("pointerdown",(t=>{if(DrawingEditor.#Ha===t.pointerType){(DrawingEditor.#za||=new Set).add(t.pointerId);if(DrawingEditor.#Oa.isCancellable()){DrawingEditor.#Oa.removeLastElement();DrawingEditor.#Oa.isEmpty()?this._currentParent.endDrawingSession(!0):this._endDraw(null)}}}),{capture:!0,passive:!1,signal:p});window.addEventListener("contextmenu",noContextMenu,{signal:p});s.addEventListener("pointermove",this._drawMove.bind(this),{signal:p});s.addEventListener("touchmove",(t=>{t.timeStamp===DrawingEditor.#ja&&stopEvent(t)}),{signal:p});t.toggleDrawing();e._editorUndoBar?.hide();if(DrawingEditor.#Oa)t.drawLayer.updateProperties(this._currentDrawId,DrawingEditor.#Oa.startNew(r,a,c,d,h));else{e.updateUIForDefaultProperties(this);DrawingEditor.#Oa=this.createDrawerInstance(r,a,c,d,h);DrawingEditor.#Ba=this.getDefaultDrawingOptions();this._currentParent=t;({id:this._currentDrawId}=t.drawLayer.draw(this._mergeSVGProperties(DrawingEditor.#Ba.toSVGProperties(),DrawingEditor.#Oa.defaultSVGProperties),!0,!1))}}static _drawMove(t){DrawingEditor.#ja=-1;if(!DrawingEditor.#Oa)return;const{offsetX:e,offsetY:i,pointerId:n}=t;if(DrawingEditor.#Ua===n)if(DrawingEditor.#za?.size>=1)this._endDraw(t);else{this._currentParent.drawLayer.updateProperties(this._currentDrawId,DrawingEditor.#Oa.add(e,i));DrawingEditor.#ja=t.timeStamp;stopEvent(t)}}static _cleanup(t){if(t){this._currentDrawId=-1;this._currentParent=null;DrawingEditor.#Oa=null;DrawingEditor.#Ba=null;DrawingEditor.#Ha=null;DrawingEditor.#ja=NaN}if(DrawingEditor.#Na){DrawingEditor.#Na.abort();DrawingEditor.#Na=null;DrawingEditor.#Ua=NaN;DrawingEditor.#za=null}}static _endDraw(t){const e=this._currentParent;if(e){e.toggleDrawing(!0);this._cleanup(!1);t?.target===e.div&&e.drawLayer.updateProperties(this._currentDrawId,DrawingEditor.#Oa.end(t.offsetX,t.offsetY));if(this.supportMultipleDrawings){const t=DrawingEditor.#Oa,i=this._currentDrawId,n=t.getLastElement();e.addCommands({cmd:()=>{e.drawLayer.updateProperties(i,t.setLastElement(n))},undo:()=>{e.drawLayer.updateProperties(i,t.removeLastElement())},mustExec:!1,type:m.DRAW_STEP})}else this.endDrawing(!1)}}static endDrawing(t){const e=this._currentParent;if(!e)return null;e.toggleDrawing(!0);e.cleanUndoStack(m.DRAW_STEP);if(!DrawingEditor.#Oa.isEmpty()){const{pageDimensions:[i,n],scale:s}=e,r=e.createAndAddNewEditor({offsetX:0,offsetY:0},!1,{drawId:this._currentDrawId,drawOutlines:DrawingEditor.#Oa.getOutlines(i*s,n*s,s,this._INNER_MARGIN),drawingOptions:DrawingEditor.#Ba,mustBeCommitted:!t});this._cleanup(!0);return r}e.drawLayer.remove(this._currentDrawId);this._cleanup(!0);return null}createDrawingOptions(t){}static deserializeDraw(t,e,i,n,s,r){unreachable("Not implemented")}static async deserialize(t,e,i){const{rawDims:{pageWidth:n,pageHeight:s,pageX:r,pageY:a}}=e.viewport,o=this.deserializeDraw(r,a,n,s,this._INNER_MARGIN,t),l=await super.deserialize(t,e,i);l.createDrawingOptions(t);l.#Ga({drawOutlines:o});l.#ya();l.onScaleChanging();l.rotate();return l}serializeDraw(t){const[e,i]=this.pageTranslation,[n,s]=this.pageDimensions;return this.#Fa.serialize([e,i,n,s],t)}renderAnnotationElement(t){t.updateEdited({rect:this.getRect(0,0)});return null}static canCreateNewEmptyEditor(){return!1}}class InkDrawOutliner{#Pr=new Float64Array(6);#Js;#Ka;#pn;#Or;#Nr;#Ya="";#Qa=0;#ra=new InkDrawOutline;#Ja;#Za;constructor(t,e,i,n,s,r){this.#Ja=i;this.#Za=n;this.#pn=s;this.#Or=r;[t,e]=this.#to(t,e);const a=this.#Js=[NaN,NaN,NaN,NaN,t,e];this.#Nr=[t,e];this.#Ka=[{line:a,points:this.#Nr}];this.#Pr.set(a,0)}updateProperty(t,e){"stroke-width"===t&&(this.#Or=e)}#to(t,e){return Outline._normalizePoint(t,e,this.#Ja,this.#Za,this.#pn)}isEmpty(){return!this.#Ka||0===this.#Ka.length}isCancellable(){return this.#Nr.length<=10}add(t,e){[t,e]=this.#to(t,e);const[i,n,s,r]=this.#Pr.subarray(2,6),a=t-s,o=e-r;if(Math.hypot(this.#Ja*a,this.#Za*o)<=2)return null;this.#Nr.push(t,e);if(isNaN(i)){this.#Pr.set([s,r,t,e],2);this.#Js.push(NaN,NaN,NaN,NaN,t,e);return{path:{d:this.toSVGPath()}}}isNaN(this.#Pr[0])&&this.#Js.splice(6,6);this.#Pr.set([i,n,s,r,t,e],0);this.#Js.push(...Outline.createBezierPoints(i,n,s,r,t,e));return{path:{d:this.toSVGPath()}}}end(t,e){const i=this.add(t,e);return i||(2===this.#Nr.length?{path:{d:this.toSVGPath()}}:null)}startNew(t,e,i,n,s){this.#Ja=i;this.#Za=n;this.#pn=s;[t,e]=this.#to(t,e);const r=this.#Js=[NaN,NaN,NaN,NaN,t,e];this.#Nr=[t,e];const a=this.#Ka.at(-1);if(a){a.line=new Float32Array(a.line);a.points=new Float32Array(a.points)}this.#Ka.push({line:r,points:this.#Nr});this.#Pr.set(r,0);this.#Qa=0;this.toSVGPath();return null}getLastElement(){return this.#Ka.at(-1)}setLastElement(t){if(!this.#Ka)return this.#ra.setLastElement(t);this.#Ka.push(t);this.#Js=t.line;this.#Nr=t.points;this.#Qa=0;return{path:{d:this.toSVGPath()}}}removeLastElement(){if(!this.#Ka)return this.#ra.removeLastElement();this.#Ka.pop();this.#Ya="";for(let t=0,e=this.#Ka.length;t<e;t++){const{line:e,points:i}=this.#Ka[t];this.#Js=e;this.#Nr=i;this.#Qa=0;this.toSVGPath()}return{path:{d:this.#Ya}}}toSVGPath(){const t=Outline.svgRound(this.#Js[4]),e=Outline.svgRound(this.#Js[5]);if(2===this.#Nr.length){this.#Ya=`${this.#Ya} M ${t} ${e} Z`;return this.#Ya}if(this.#Nr.length<=6){const i=this.#Ya.lastIndexOf("M");this.#Ya=`${this.#Ya.slice(0,i)} M ${t} ${e}`;this.#Qa=6}if(4===this.#Nr.length){const t=Outline.svgRound(this.#Js[10]),e=Outline.svgRound(this.#Js[11]);this.#Ya=`${this.#Ya} L ${t} ${e}`;this.#Qa=12;return this.#Ya}const i=[];if(0===this.#Qa){i.push(`M ${t} ${e}`);this.#Qa=6}for(let t=this.#Qa,e=this.#Js.length;t<e;t+=6){const[e,n,s,r,a,o]=this.#Js.slice(t,t+6).map(Outline.svgRound);i.push(`C${e} ${n} ${s} ${r} ${a} ${o}`)}this.#Ya+=i.join(" ");this.#Qa=this.#Js.length;return this.#Ya}getOutlines(t,e,i,n){const s=this.#Ka.at(-1);s.line=new Float32Array(s.line);s.points=new Float32Array(s.points);this.#ra.build(this.#Ka,t,e,i,this.#pn,this.#Or,n);this.#Pr=null;this.#Js=null;this.#Ka=null;this.#Ya=null;return this.#ra}get defaultSVGProperties(){return{root:{viewBox:"0 0 10000 10000"},rootClass:{draw:!0},bbox:[0,0,1,1]}}}class InkDrawOutline extends Outline{#Xr;#eo=0;#Tr;#Ka;#Ja;#Za;#io;#pn;#Or;build(t,e,i,n,s,r,a){this.#Ja=e;this.#Za=i;this.#io=n;this.#pn=s;this.#Or=r;this.#Tr=a??0;this.#Ka=t;this.#no()}get thickness(){return this.#Or}setLastElement(t){this.#Ka.push(t);return{path:{d:this.toSVGPath()}}}removeLastElement(){this.#Ka.pop();return{path:{d:this.toSVGPath()}}}toSVGPath(){const t=[];for(const{line:e}of this.#Ka){t.push(`M${Outline.svgRound(e[4])} ${Outline.svgRound(e[5])}`);if(6!==e.length)if(12===e.length&&isNaN(e[6]))t.push(`L${Outline.svgRound(e[10])} ${Outline.svgRound(e[11])}`);else for(let i=6,n=e.length;i<n;i+=6){const[n,s,r,a,o,l]=e.subarray(i,i+6).map(Outline.svgRound);t.push(`C${n} ${s} ${r} ${a} ${o} ${l}`)}else t.push("Z")}return t.join("")}serialize([t,e,i,n],s){const r=[],a=[],[o,l,h,c]=this.#so();let d,u,p,g,f,m,b,v,w;switch(this.#pn){case 0:w=Outline._rescale;d=t;u=e+n;p=i;g=-n;f=t+o*i;m=e+(1-l-c)*n;b=t+(o+h)*i;v=e+(1-l)*n;break;case 90:w=Outline._rescaleAndSwap;d=t;u=e;p=i;g=n;f=t+l*i;m=e+o*n;b=t+(l+c)*i;v=e+(o+h)*n;break;case 180:w=Outline._rescale;d=t+i;u=e;p=-i;g=n;f=t+(1-o-h)*i;m=e+l*n;b=t+(1-o)*i;v=e+(l+c)*n;break;case 270:w=Outline._rescaleAndSwap;d=t+i;u=e+n;p=-i;g=-n;f=t+(1-l-c)*i;m=e+(1-o-h)*n;b=t+(1-l)*i;v=e+(1-o)*n}for(const{line:t,points:e}of this.#Ka){r.push(w(t,d,u,p,g,s?new Array(t.length):null));a.push(w(e,d,u,p,g,s?new Array(e.length):null))}return{lines:r,points:a,rect:[f,m,b,v]}}static deserialize(t,e,i,n,s,{paths:{lines:r,points:a},rotation:o,thickness:l}){const h=[];let c,d,u,p,g;switch(o){case 0:g=Outline._rescale;c=-t/i;d=e/n+1;u=1/i;p=-1/n;break;case 90:g=Outline._rescaleAndSwap;c=-e/n;d=-t/i;u=1/n;p=1/i;break;case 180:g=Outline._rescale;c=t/i+1;d=-e/n;u=-1/i;p=1/n;break;case 270:g=Outline._rescaleAndSwap;c=e/n+1;d=t/i+1;u=-1/n;p=-1/i}if(!r){r=[];for(const t of a){const e=t.length;if(2===e){r.push(new Float32Array([NaN,NaN,NaN,NaN,t[0],t[1]]));continue}if(4===e){r.push(new Float32Array([NaN,NaN,NaN,NaN,t[0],t[1],NaN,NaN,NaN,NaN,t[2],t[3]]));continue}const i=new Float32Array(3*(e-2));r.push(i);let[n,s,a,o]=t.subarray(0,4);i.set([NaN,NaN,NaN,NaN,n,s],0);for(let r=4;r<e;r+=2){const e=t[r],l=t[r+1];i.set(Outline.createBezierPoints(n,s,a,o,e,l),3*(r-2));[n,s,a,o]=[a,o,e,l]}}}for(let t=0,e=r.length;t<e;t++)h.push({line:g(r[t].map((t=>t??NaN)),c,d,u,p),points:g(a[t].map((t=>t??NaN)),c,d,u,p)});const f=new this.prototype.constructor;f.build(h,i,n,1,o,l,s);return f}#ro(t=this.#Or){const e=this.#Tr+t/2*this.#io;return this.#pn%180==0?[e/this.#Ja,e/this.#Za]:[e/this.#Za,e/this.#Ja]}#so(){const[t,e,i,n]=this.#Xr,[s,r]=this.#ro(0);return[t+s,e+r,i-2*s,n-2*r]}#no(){const t=this.#Xr=new Float32Array([1/0,1/0,-1/0,-1/0]);for(const{line:e}of this.#Ka){if(e.length<=12){for(let i=4,n=e.length;i<n;i+=6)Util.pointBoundingBox(e[i],e[i+1],t);continue}let i=e[4],n=e[5];for(let s=6,r=e.length;s<r;s+=6){const[r,a,o,l,h,c]=e.subarray(s,s+6);Util.bezierBoundingBox(i,n,r,a,o,l,h,c,t);i=h;n=c}}const[e,i]=this.#ro();t[0]=MathClamp(t[0]-e,0,1);t[1]=MathClamp(t[1]-i,0,1);t[2]=MathClamp(t[2]+e,0,1);t[3]=MathClamp(t[3]+i,0,1);t[2]-=t[0];t[3]-=t[1]}get box(){return this.#Xr}updateProperty(t,e){return"stroke-width"===t?this.#_a(e):null}#_a(t){const[e,i]=this.#ro();this.#Or=t;const[n,s]=this.#ro(),[r,a]=[n-e,s-i],o=this.#Xr;o[0]-=r;o[1]-=a;o[2]+=2*r;o[3]+=2*a;return o}updateParentDimensions([t,e],i){const[n,s]=this.#ro();this.#Ja=t;this.#Za=e;this.#io=i;const[r,a]=this.#ro(),o=r-n,l=a-s,h=this.#Xr;h[0]-=o;h[1]-=l;h[2]+=2*o;h[3]+=2*l;return h}updateRotation(t){this.#eo=t;return{path:{transform:this.rotationTransform}}}get viewBox(){return this.#Xr.map(Outline.svgRound).join(" ")}get defaultProperties(){const[t,e]=this.#Xr;return{root:{viewBox:this.viewBox},path:{"transform-origin":`${Outline.svgRound(t)} ${Outline.svgRound(e)}`}}}get rotationTransform(){const[,,t,e]=this.#Xr;let i=0,n=0,s=0,r=0,a=0,o=0;switch(this.#eo){case 90:n=e/t;s=-t/e;a=t;break;case 180:i=-1;r=-1;a=t;o=e;break;case 270:n=-e/t;s=t/e;o=e;break;default:return""}return`matrix(${i} ${n} ${s} ${r} ${Outline.svgRound(a)} ${Outline.svgRound(o)})`}getPathResizingSVGProperties([t,e,i,n]){const[s,r]=this.#ro(),[a,o,l,h]=this.#Xr;if(Math.abs(l-s)<=Outline.PRECISION||Math.abs(h-r)<=Outline.PRECISION){const s=t+i/2-(a+l/2),r=e+n/2-(o+h/2);return{path:{"transform-origin":`${Outline.svgRound(t)} ${Outline.svgRound(e)}`,transform:`${this.rotationTransform} translate(${s} ${r})`}}}const c=(i-2*s)/(l-2*s),d=(n-2*r)/(h-2*r),u=l/i,p=h/n;return{path:{"transform-origin":`${Outline.svgRound(a)} ${Outline.svgRound(o)}`,transform:`${this.rotationTransform} scale(${u} ${p}) translate(${Outline.svgRound(s)} ${Outline.svgRound(r)}) scale(${c} ${d}) translate(${Outline.svgRound(-s)} ${Outline.svgRound(-r)})`}}}getPathResizedSVGProperties([t,e,i,n]){const[s,r]=this.#ro(),a=this.#Xr,[o,l,h,c]=a;a[0]=t;a[1]=e;a[2]=i;a[3]=n;if(Math.abs(h-s)<=Outline.PRECISION||Math.abs(c-r)<=Outline.PRECISION){const s=t+i/2-(o+h/2),r=e+n/2-(l+c/2);for(const{line:t,points:e}of this.#Ka){Outline._translate(t,s,r,t);Outline._translate(e,s,r,e)}return{root:{viewBox:this.viewBox},path:{"transform-origin":`${Outline.svgRound(t)} ${Outline.svgRound(e)}`,transform:this.rotationTransform||null,d:this.toSVGPath()}}}const d=(i-2*s)/(h-2*s),u=(n-2*r)/(c-2*r),p=-d*(o+s)+t+s,g=-u*(l+r)+e+r;if(1!==d||1!==u||0!==p||0!==g)for(const{line:t,points:e}of this.#Ka){Outline._rescale(t,p,g,d,u,t);Outline._rescale(e,p,g,d,u,e)}return{root:{viewBox:this.viewBox},path:{"transform-origin":`${Outline.svgRound(t)} ${Outline.svgRound(e)}`,transform:this.rotationTransform||null,d:this.toSVGPath()}}}getPathTranslatedSVGProperties([t,e],i){const[n,s]=i,r=this.#Xr,a=t-r[0],o=e-r[1];if(this.#Ja===n&&this.#Za===s)for(const{line:t,points:e}of this.#Ka){Outline._translate(t,a,o,t);Outline._translate(e,a,o,e)}else{const t=this.#Ja/n,e=this.#Za/s;this.#Ja=n;this.#Za=s;for(const{line:i,points:n}of this.#Ka){Outline._rescale(i,a,o,t,e,i);Outline._rescale(n,a,o,t,e,n)}r[2]*=t;r[3]*=e}r[0]=t;r[1]=e;return{root:{viewBox:this.viewBox},path:{d:this.toSVGPath(),"transform-origin":`${Outline.svgRound(t)} ${Outline.svgRound(e)}`}}}get defaultSVGProperties(){const t=this.#Xr;return{root:{viewBox:this.viewBox},rootClass:{draw:!0},path:{d:this.toSVGPath(),"transform-origin":`${Outline.svgRound(t[0])} ${Outline.svgRound(t[1])}`,transform:this.rotationTransform||null},bbox:t}}}class InkDrawingOptions extends DrawingOptions{constructor(t){super();this._viewParameters=t;super.updateProperties({fill:"none",stroke:AnnotationEditor._defaultLineColor,"stroke-opacity":1,"stroke-width":1,"stroke-linecap":"round","stroke-linejoin":"round","stroke-miterlimit":10})}updateSVGProperty(t,e){if("stroke-width"===t){e??=this["stroke-width"];e*=this._viewParameters.realScale}super.updateSVGProperty(t,e)}clone(){const t=new InkDrawingOptions(this._viewParameters);t.updateAll(this);return t}}class InkEditor extends DrawingEditor{static _type="ink";static _editorType=f.INK;static _defaultDrawingOptions=null;constructor(t){super({...t,name:"inkEditor"});this._willKeepAspectRatio=!0;this.defaultL10nId="pdfjs-editor-ink-editor"}static initialize(t,e){AnnotationEditor.initialize(t,e);this._defaultDrawingOptions=new InkDrawingOptions(e.viewParameters)}static getDefaultDrawingOptions(t){const e=this._defaultDrawingOptions.clone();e.updateProperties(t);return e}static get supportMultipleDrawings(){return!0}static get typesMap(){return shadow(this,"typesMap",new Map([[m.INK_THICKNESS,"stroke-width"],[m.INK_COLOR,"stroke"],[m.INK_OPACITY,"stroke-opacity"]]))}static createDrawerInstance(t,e,i,n,s){return new InkDrawOutliner(t,e,i,n,s,this._defaultDrawingOptions["stroke-width"])}static deserializeDraw(t,e,i,n,s,r){return InkDrawOutline.deserialize(t,e,i,n,s,r)}static async deserialize(t,e,i){let n=null;if(t instanceof InkAnnotationElement){const{data:{inkLists:e,rect:i,rotation:s,id:r,color:a,opacity:o,borderStyle:{rawWidth:l},popupRef:h,contentsObj:c},parent:{page:{pageNumber:d}}}=t;n=t={annotationType:f.INK,color:Array.from(a),thickness:l,opacity:o,paths:{points:e},boxes:null,pageIndex:d-1,rect:i.slice(0),rotation:s,annotationElementId:r,id:r,deleted:!1,popupRef:h,comment:c?.str||null}}const s=await super.deserialize(t,e,i);s._initialData=n;t.comment&&s.setCommentData(t.comment);return s}get toolbarButtons(){this._colorPicker||=new BasicColorPicker(this);return[["colorPicker",this._colorPicker]]}get colorType(){return m.INK_COLOR}get colorValue(){return this._drawingOptions.stroke}onScaleChanging(){if(!this.parent)return;super.onScaleChanging();const{_drawId:t,_drawingOptions:e,parent:i}=this;e.updateSVGProperty("stroke-width");i.drawLayer.updateProperties(t,e.toSVGProperties())}static onScaleChangingWhenDrawing(){const t=this._currentParent;if(t){super.onScaleChangingWhenDrawing();this._defaultDrawingOptions.updateSVGProperty("stroke-width");t.drawLayer.updateProperties(this._currentDrawId,this._defaultDrawingOptions.toSVGProperties())}}createDrawingOptions({color:t,thickness:e,opacity:i}){this._drawingOptions=InkEditor.getDefaultDrawingOptions({stroke:Util.makeHexColor(...t),"stroke-width":e,"stroke-opacity":i})}serialize(t=!1){if(this.isEmpty())return null;if(this.deleted)return this.serializeDeleted();const{lines:e,points:i,rect:n}=this.serializeDraw(t),{_drawingOptions:{stroke:s,"stroke-opacity":r,"stroke-width":a}}=this,o={annotationType:f.INK,color:AnnotationEditor._colorManager.convert(s),opacity:r,thickness:a,paths:{lines:e,points:i},pageIndex:this.pageIndex,rect:n,rotation:this.rotation,structTreeParentId:this._structTreeParentId};this.addComment(o);if(t){o.isCopy=!0;return o}if(this.annotationElementId&&!this.#Er(o))return null;o.id=this.annotationElementId;return o}#Er(t){const{color:e,thickness:i,opacity:n,pageIndex:s}=this._initialData;return this.hasEditedComment||this._hasBeenMoved||this._hasBeenResized||t.color.some(((t,i)=>t!==e[i]))||t.thickness!==i||t.opacity!==n||t.pageIndex!==s}renderAnnotationElement(t){const{points:e,rect:i}=this.serializeDraw(!1),n={rect:i,thickness:this._drawingOptions["stroke-width"],points:e};this.hasEditedComment&&(n.popup=this.comment);t.updateEdited(n);return null}}class ContourDrawOutline extends InkDrawOutline{toSVGPath(){let t=super.toSVGPath();t.endsWith("Z")||(t+="Z");return t}}class SignatureExtractor{static#ao={maxDim:512,sigmaSFactor:.02,sigmaR:25,kernelSize:16};static#oo(t,e,i,n){n-=e;return 0===(i-=t)?n>0?0:4:1===i?n+6:2-n}static#lo=new Int32Array([0,1,-1,1,-1,0,-1,-1,0,-1,1,-1,1,0,1,1]);static#ho(t,e,i,n,s,r,a){const o=this.#oo(i,n,s,r);for(let s=0;s<8;s++){const r=(-s+o-a+16)%8;if(0!==t[(i+this.#lo[2*r])*e+(n+this.#lo[2*r+1])])return r}return-1}static#co(t,e,i,n,s,r,a){const o=this.#oo(i,n,s,r);for(let s=0;s<8;s++){const r=(s+o+a+16)%8;if(0!==t[(i+this.#lo[2*r])*e+(n+this.#lo[2*r+1])])return r}return-1}static#do(t,e,i,n){const s=t.length,r=new Int32Array(s);for(let e=0;e<s;e++)r[e]=t[e]<=n?1:0;for(let t=1;t<i-1;t++)r[t*e]=r[t*e+e-1]=0;for(let t=0;t<e;t++)r[t]=r[e*i-1-t]=0;let a,o=1;const l=[];for(let t=1;t<i-1;t++){a=1;for(let i=1;i<e-1;i++){const n=t*e+i,s=r[n];if(0===s)continue;let h=t,c=i;if(1===s&&0===r[n-1]){o+=1;c-=1}else{if(!(s>=1&&0===r[n+1])){1!==s&&(a=Math.abs(s));continue}o+=1;c+=1;s>1&&(a=s)}const d=[i,t],u=c===i+1,p={isHole:u,points:d,id:o,parent:0};l.push(p);let g;for(const t of l)if(t.id===a){g=t;break}g?g.isHole?p.parent=u?g.parent:a:p.parent=u?a:g.parent:p.parent=u?a:0;const f=this.#ho(r,e,t,i,h,c,0);if(-1===f){r[n]=-o;1!==r[n]&&(a=Math.abs(r[n]));continue}let m=this.#lo[2*f],b=this.#lo[2*f+1];const v=t+m,w=i+b;h=v;c=w;let y=t,A=i;for(;;){const s=this.#co(r,e,y,A,h,c,1);m=this.#lo[2*s];b=this.#lo[2*s+1];const l=y+m,u=A+b;d.push(u,l);const p=y*e+A;0===r[p+1]?r[p]=-o:1===r[p]&&(r[p]=o);if(l===t&&u===i&&y===v&&A===w){1!==r[n]&&(a=Math.abs(r[n]));break}h=y;c=A;y=l;A=u}}}return l}static#uo(t,e,i,n){if(i-e<=4){for(let s=e;s<i-2;s+=2)n.push(t[s],t[s+1]);return}const s=t[e],r=t[e+1],a=t[i-4]-s,o=t[i-3]-r,l=Math.hypot(a,o),h=a/l,c=o/l,d=h*r-c*s,u=o/a,p=1/l,g=Math.atan(u),f=Math.cos(g),m=Math.sin(g),b=p*(Math.abs(f)+Math.abs(m)),v=p*(1-b+b**2),w=Math.max(Math.atan(Math.abs(m+f)*v),Math.atan(Math.abs(m-f)*v));let y=0,A=e;for(let n=e+2;n<i-2;n+=2){const e=Math.abs(d-h*t[n+1]+c*t[n]);if(e>y){A=n;y=e}}if(y>(l*w)**2){this.#uo(t,e,A+2,n);this.#uo(t,A,i,n)}else n.push(s,r)}static#po(t){const e=[],i=t.length;this.#uo(t,0,i,e);e.push(t[i-2],t[i-1]);return e.length<=4?null:e}static#go(t,e,i,n,s,r){const a=new Float32Array(r**2),o=-2*n**2,l=r>>1;for(let t=0;t<r;t++){const e=(t-l)**2;for(let i=0;i<r;i++)a[t*r+i]=Math.exp((e+(i-l)**2)/o)}const h=new Float32Array(256),c=-2*s**2;for(let t=0;t<256;t++)h[t]=Math.exp(t**2/c);const d=t.length,u=new Uint8Array(d),p=new Uint32Array(256);for(let n=0;n<i;n++)for(let s=0;s<e;s++){const o=n*e+s,c=t[o];let d=0,g=0;for(let o=0;o<r;o++){const u=n+o-l;if(!(u<0||u>=i))for(let i=0;i<r;i++){const n=s+i-l;if(n<0||n>=e)continue;const p=t[u*e+n],f=a[o*r+i]*h[Math.abs(p-c)];d+=p*f;g+=f}}p[u[o]=Math.round(d/g)]++}return[u,p]}static#fo(t){const e=new Uint32Array(256);for(const i of t)e[i]++;return e}static#mo(t){const e=t.length,i=new Uint8ClampedArray(e>>2);let n=-1/0,s=1/0;for(let e=0,r=i.length;e<r;e++){const r=i[e]=t[e<<2];n=Math.max(n,r);s=Math.min(s,r)}const r=255/(n-s);for(let t=0,e=i.length;t<e;t++)i[t]=(i[t]-s)*r;return i}static#bo(t){let e,i=-1/0,n=-1/0;const s=t.findIndex((t=>0!==t));let r=s,a=s;for(e=s;e<256;e++){const s=t[e];if(s>i){if(e-r>n){n=e-r;a=e-1}i=s;r=e}}for(e=a-1;e>=0&&!(t[e]>t[e+1]);e--);return e}static#vo(t){const e=t,{width:i,height:n}=t,{maxDim:s}=this.#ao;let r=i,a=n;if(i>s||n>s){let o=i,l=n,h=Math.log2(Math.max(i,n)/s);const c=Math.floor(h);h=h===c?c-1:c;for(let i=0;i<h;i++){r=Math.ceil(o/2);a=Math.ceil(l/2);const i=new OffscreenCanvas(r,a);i.getContext("2d").drawImage(t,0,0,o,l,0,0,r,a);o=r;l=a;t!==e&&t.close();t=i.transferToImageBitmap()}const d=Math.min(s/r,s/a);r=Math.round(r*d);a=Math.round(a*d)}const o=new OffscreenCanvas(r,a).getContext("2d",{willReadFrequently:!0});o.fillStyle="white";o.fillRect(0,0,r,a);o.filter="grayscale(1)";o.drawImage(t,0,0,t.width,t.height,0,0,r,a);const l=o.getImageData(0,0,r,a).data;return[this.#mo(l),r,a]}static extractContoursFromText(t,{fontFamily:e,fontStyle:i,fontWeight:n},s,r,a,o){let l=new OffscreenCanvas(1,1),h=l.getContext("2d",{alpha:!1});const c=h.font=`${i} ${n} 200px ${e}`,{actualBoundingBoxLeft:d,actualBoundingBoxRight:u,actualBoundingBoxAscent:p,actualBoundingBoxDescent:g,fontBoundingBoxAscent:f,fontBoundingBoxDescent:m,width:b}=h.measureText(t),v=1.5,w=Math.ceil(Math.max(Math.abs(d)+Math.abs(u)||0,b)*v),y=Math.ceil(Math.max(Math.abs(p)+Math.abs(g)||200,Math.abs(f)+Math.abs(m)||200)*v);l=new OffscreenCanvas(w,y);h=l.getContext("2d",{alpha:!0,willReadFrequently:!0});h.font=c;h.filter="grayscale(1)";h.fillStyle="white";h.fillRect(0,0,w,y);h.fillStyle="black";h.fillText(t,.5*w/2,1.5*y/2);const A=this.#mo(h.getImageData(0,0,w,y).data),x=this.#fo(A),_=this.#bo(x),E=this.#do(A,w,y,_);return this.processDrawnLines({lines:{curves:E,width:w,height:y},pageWidth:s,pageHeight:r,rotation:a,innerMargin:o,mustSmooth:!0,areContours:!0})}static process(t,e,i,n,s){const[r,a,o]=this.#vo(t),[l,h]=this.#go(r,a,o,Math.hypot(a,o)*this.#ao.sigmaSFactor,this.#ao.sigmaR,this.#ao.kernelSize),c=this.#bo(h),d=this.#do(l,a,o,c);return this.processDrawnLines({lines:{curves:d,width:a,height:o},pageWidth:e,pageHeight:i,rotation:n,innerMargin:s,mustSmooth:!0,areContours:!0})}static processDrawnLines({lines:t,pageWidth:e,pageHeight:i,rotation:n,innerMargin:s,mustSmooth:r,areContours:a}){n%180!=0&&([e,i]=[i,e]);const{curves:o,width:l,height:h}=t,c=t.thickness??0,d=[],u=Math.min(e/l,i/h),p=u/e,g=u/i,f=[];for(const{points:t}of o){const e=r?this.#po(t):t;if(!e)continue;f.push(e);const i=e.length,n=new Float32Array(i),s=new Float32Array(3*(2===i?2:i-2));d.push({line:s,points:n});if(2===i){n[0]=e[0]*p;n[1]=e[1]*g;s.set([NaN,NaN,NaN,NaN,n[0],n[1]],0);continue}let[a,o,l,h]=e;a*=p;o*=g;l*=p;h*=g;n.set([a,o,l,h],0);s.set([NaN,NaN,NaN,NaN,a,o],0);for(let t=4;t<i;t+=2){const i=n[t]=e[t]*p,r=n[t+1]=e[t+1]*g;s.set(Outline.createBezierPoints(a,o,l,h,i,r),3*(t-2));[a,o,l,h]=[l,h,i,r]}}if(0===d.length)return null;const m=a?new ContourDrawOutline:new InkDrawOutline;m.build(d,e,i,1,n,a?0:c,s);return{outline:m,newCurves:f,areContours:a,thickness:c,width:l,height:h}}static async compressSignature({outlines:t,areContours:e,thickness:i,width:n,height:s}){let r,a=1/0,o=-1/0,l=0;for(const e of t){l+=e.length;for(let t=2,i=e.length;t<i;t++){const i=e[t]-e[t-2];a=Math.min(a,i);o=Math.max(o,i)}}r=a>=-128&&o<=127?Int8Array:a>=-32768&&o<=32767?Int16Array:Int32Array;const h=t.length,c=8+3*h,d=new Uint32Array(c);let u=0;d[u++]=c*Uint32Array.BYTES_PER_ELEMENT+(l-2*h)*r.BYTES_PER_ELEMENT;d[u++]=0;d[u++]=n;d[u++]=s;d[u++]=e?0:1;d[u++]=Math.max(0,Math.floor(i??0));d[u++]=h;d[u++]=r.BYTES_PER_ELEMENT;for(const e of t){d[u++]=e.length-2;d[u++]=e[0];d[u++]=e[1]}const p=new CompressionStream("deflate-raw"),g=p.writable.getWriter();await g.ready;g.write(d);const f=r.prototype.constructor;for(const e of t){const t=new f(e.length-2);for(let i=2,n=e.length;i<n;i++)t[i-2]=e[i]-e[i-2];g.write(t)}g.close();const m=await new Response(p.readable).arrayBuffer();return toBase64Util(new Uint8Array(m))}static async decompressSignature(t){try{const e=function fromBase64Util(t){return Uint8Array.fromBase64?Uint8Array.fromBase64(t):stringToBytes(atob(t))}(t),{readable:i,writable:n}=new DecompressionStream("deflate-raw"),s=n.getWriter();await s.ready;s.write(e).then((async()=>{await s.ready;await s.close()})).catch((()=>{}));let r=null,a=0;for await(const t of i){r||=new Uint8Array(new Uint32Array(t.buffer,0,4)[0]);r.set(t,a);a+=t.length}const o=new Uint32Array(r.buffer,0,r.length>>2),l=o[1];if(0!==l)throw new Error(`Invalid version: ${l}`);const h=o[2],c=o[3],d=0===o[4],u=o[5],p=o[6],g=o[7],f=[],m=(8+3*p)*Uint32Array.BYTES_PER_ELEMENT;let b;switch(g){case Int8Array.BYTES_PER_ELEMENT:b=new Int8Array(r.buffer,m);break;case Int16Array.BYTES_PER_ELEMENT:b=new Int16Array(r.buffer,m);break;case Int32Array.BYTES_PER_ELEMENT:b=new Int32Array(r.buffer,m)}a=0;for(let t=0;t<p;t++){const e=o[3*t+8],i=new Float32Array(e+2);f.push(i);for(let e=0;e<2;e++)i[e]=o[3*t+8+e+1];for(let t=0;t<e;t++)i[t+2]=i[t]+b[a++]}return{areContours:d,thickness:u,outlines:f,width:h,height:c}}catch(t){warn(`decompressSignature: ${t}`);return null}}}class SignatureOptions extends DrawingOptions{constructor(){super();super.updateProperties({fill:AnnotationEditor._defaultLineColor,"stroke-width":0})}clone(){const t=new SignatureOptions;t.updateAll(this);return t}}class DrawnSignatureOptions extends InkDrawingOptions{constructor(t){super(t);super.updateProperties({stroke:AnnotationEditor._defaultLineColor,"stroke-width":1})}clone(){const t=new DrawnSignatureOptions(this._viewParameters);t.updateAll(this);return t}}class SignatureEditor extends DrawingEditor{#wo=!1;#yo=null;#Ao=null;#xo=null;static _type="signature";static _editorType=f.SIGNATURE;static _defaultDrawingOptions=null;constructor(t){super({...t,mustBeCommitted:!0,name:"signatureEditor"});this._willKeepAspectRatio=!0;this.#Ao=t.signatureData||null;this.#yo=null;this.defaultL10nId="pdfjs-editor-signature-editor1"}static initialize(t,e){AnnotationEditor.initialize(t,e);this._defaultDrawingOptions=new SignatureOptions;this._defaultDrawnSignatureOptions=new DrawnSignatureOptions(e.viewParameters)}static getDefaultDrawingOptions(t){const e=this._defaultDrawingOptions.clone();e.updateProperties(t);return e}static get supportMultipleDrawings(){return!1}static get typesMap(){return shadow(this,"typesMap",new Map)}static get isDrawer(){return!1}get telemetryFinalData(){return{type:"signature",hasDescription:!!this.#yo}}static computeTelemetryFinalData(t){const e=t.get("hasDescription");return{hasAltText:e.get(!0)??0,hasNoAltText:e.get(!1)??0}}get isResizable(){return!0}onScaleChanging(){null!==this._drawId&&super.onScaleChanging()}render(){if(this.div)return this.div;let t,e;const{_isCopy:i}=this;if(i){this._isCopy=!1;t=this.x;e=this.y}super.render();if(null===this._drawId)if(this.#Ao){const{lines:t,mustSmooth:e,areContours:i,description:n,uuid:s,heightInPage:r}=this.#Ao,{rawDims:{pageWidth:a,pageHeight:o},rotation:l}=this.parent.viewport,h=SignatureExtractor.processDrawnLines({lines:t,pageWidth:a,pageHeight:o,rotation:l,innerMargin:SignatureEditor._INNER_MARGIN,mustSmooth:e,areContours:i});this.addSignature(h,r,n,s)}else{this.div.setAttribute("data-l10n-args",JSON.stringify({description:""}));this.div.hidden=!0;this._uiManager.getSignature(this)}else this.div.setAttribute("data-l10n-args",JSON.stringify({description:this.#yo||""}));if(i){this._isCopy=!0;this._moveAfterPaste(t,e)}return this.div}setUuid(t){this.#xo=t;this.addEditToolbar()}getUuid(){return this.#xo}get description(){return this.#yo}set description(t){this.#yo=t;if(this.div){this.div.setAttribute("data-l10n-args",JSON.stringify({description:t}));super.addEditToolbar().then((e=>{e?.updateEditSignatureButton(t)}))}}getSignaturePreview(){const{newCurves:t,areContours:e,thickness:i,width:n,height:s}=this.#Ao,r=Math.max(n,s);return{areContours:e,outline:SignatureExtractor.processDrawnLines({lines:{curves:t.map((t=>({points:t}))),thickness:i,width:n,height:s},pageWidth:r,pageHeight:r,rotation:0,innerMargin:0,mustSmooth:!1,areContours:e}).outline}}get toolbarButtons(){return this._uiManager.signatureManager?[["editSignature",this._uiManager.signatureManager]]:super.toolbarButtons}addSignature(t,e,i,n){const{x:s,y:r}=this,{outline:a}=this.#Ao=t;this.#wo=a instanceof ContourDrawOutline;this.description=i;let o;if(this.#wo)o=SignatureEditor.getDefaultDrawingOptions();else{o=SignatureEditor._defaultDrawnSignatureOptions.clone();o.updateProperties({"stroke-width":a.thickness})}this._addOutlines({drawOutlines:a,drawingOptions:o});const[l,h]=this.parentDimensions,[,c]=this.pageDimensions;let d=e/c;d=d>=1?.5:d;this.width*=d/this.height;if(this.width>=1){d*=.9/this.width;this.width=.9}this.height=d;this.setDims(l*this.width,h*this.height);this.x=s;this.y=r;this.center();this._onResized();this.onScaleChanging();this.rotate();this._uiManager.addToAnnotationStorage(this);this.setUuid(n);this._reportTelemetry({action:"pdfjs.signature.inserted",data:{hasBeenSaved:!!n,hasDescription:!!i}});this.div.hidden=!1}getFromImage(t){const{rawDims:{pageWidth:e,pageHeight:i},rotation:n}=this.parent.viewport;return SignatureExtractor.process(t,e,i,n,SignatureEditor._INNER_MARGIN)}getFromText(t,e){const{rawDims:{pageWidth:i,pageHeight:n},rotation:s}=this.parent.viewport;return SignatureExtractor.extractContoursFromText(t,e,i,n,s,SignatureEditor._INNER_MARGIN)}getDrawnSignature(t){const{rawDims:{pageWidth:e,pageHeight:i},rotation:n}=this.parent.viewport;return SignatureExtractor.processDrawnLines({lines:t,pageWidth:e,pageHeight:i,rotation:n,innerMargin:SignatureEditor._INNER_MARGIN,mustSmooth:!1,areContours:!1})}createDrawingOptions({areContours:t,thickness:e}){if(t)this._drawingOptions=SignatureEditor.getDefaultDrawingOptions();else{this._drawingOptions=SignatureEditor._defaultDrawnSignatureOptions.clone();this._drawingOptions.updateProperties({"stroke-width":e})}}serialize(t=!1){if(this.isEmpty())return null;const{lines:e,points:i,rect:n}=this.serializeDraw(t),{_drawingOptions:{"stroke-width":s}}=this,r={annotationType:f.SIGNATURE,isSignature:!0,areContours:this.#wo,color:[0,0,0],thickness:this.#wo?0:s,pageIndex:this.pageIndex,rect:n,rotation:this.rotation,structTreeParentId:this._structTreeParentId};this.addComment(r);if(t){r.paths={lines:e,points:i};r.uuid=this.#xo;r.isCopy=!0}else r.lines=e;this.#yo&&(r.accessibilityData={type:"Figure",alt:this.#yo});return r}static deserializeDraw(t,e,i,n,s,r){return r.areContours?ContourDrawOutline.deserialize(t,e,i,n,s,r):InkDrawOutline.deserialize(t,e,i,n,s,r)}static async deserialize(t,e,i){const n=await super.deserialize(t,e,i);n.#wo=t.areContours;n.description=t.accessibilityData?.alt||"";n.#xo=t.uuid;return n}}class StampEditor extends AnnotationEditor{#_o=null;#Eo=null;#So=null;#Co=null;#To=null;#Mo="";#Do=null;#Po=!1;#ko=null;#Io=!1;#Ro=!1;static _type="stamp";static _editorType=f.STAMP;constructor(t){super({...t,name:"stampEditor"});this.#Co=t.bitmapUrl;this.#To=t.bitmapFile;this.defaultL10nId="pdfjs-editor-stamp-editor"}static initialize(t,e){AnnotationEditor.initialize(t,e)}static isHandlingMimeForPasting(t){return $.includes(t)}static paste(t,e){e.pasteEditor({mode:f.STAMP},{bitmapFile:t.getAsFile()})}altTextFinish(){this._uiManager.useNewAltTextFlow&&(this.div.hidden=!1);super.altTextFinish()}get telemetryFinalData(){return{type:"stamp",hasAltText:!!this.altTextData?.altText}}static computeTelemetryFinalData(t){const e=t.get("hasAltText");return{hasAltText:e.get(!0)??0,hasNoAltText:e.get(!1)??0}}#Fo(t,e=!1){if(t){this.#_o=t.bitmap;if(!e){this.#Eo=t.id;this.#Io=t.isSvg}t.file&&(this.#Mo=t.file.name);this.#Lo()}else this.remove()}#Oo(){this.#So=null;this._uiManager.enableWaiting(!1);if(this.#Do)if(this._uiManager.useNewAltTextWhenAddingImage&&this._uiManager.useNewAltTextFlow&&this.#_o)this.addEditToolbar().then((()=>{this._editToolbar.hide();this._uiManager.editAltText(this,!0)}));else{if(!this._uiManager.useNewAltTextWhenAddingImage&&this._uiManager.useNewAltTextFlow&&this.#_o){this._reportTelemetry({action:"pdfjs.image.image_added",data:{alt_text_modal:!1,alt_text_type:"empty"}});try{this.mlGuessAltText()}catch{}}this.div.focus()}}async mlGuessAltText(t=null,e=!0){if(this.hasAltTextData())return null;const{mlManager:i}=this._uiManager;if(!i)throw new Error("No ML.");if(!await i.isEnabledFor("altText"))throw new Error("ML isn't enabled for alt text.");const{data:n,width:s,height:r}=t||this.copyCanvas(null,null,!0).imageData,a=await i.guess({name:"altText",request:{data:n,width:s,height:r,channels:n.length/(s*r)}});if(!a)throw new Error("No response from the AI service.");if(a.error)throw new Error("Error from the AI service.");if(a.cancel)return null;if(!a.output)throw new Error("No valid response from the AI service.");const o=a.output;await this.setGuessedAltText(o);e&&!this.hasAltTextData()&&(this.altTextData={alt:o,decorative:!1});return o}#No(){if(this.#Eo){this._uiManager.enableWaiting(!0);this._uiManager.imageManager.getFromId(this.#Eo).then((t=>this.#Fo(t,!0))).finally((()=>this.#Oo()));return}if(this.#Co){const t=this.#Co;this.#Co=null;this._uiManager.enableWaiting(!0);this.#So=this._uiManager.imageManager.getFromUrl(t).then((t=>this.#Fo(t))).finally((()=>this.#Oo()));return}if(this.#To){const t=this.#To;this.#To=null;this._uiManager.enableWaiting(!0);this.#So=this._uiManager.imageManager.getFromFile(t).then((t=>this.#Fo(t))).finally((()=>this.#Oo()));return}const t=document.createElement("input");t.type="file";t.accept=$.join(",");const e=this._uiManager._signal;this.#So=new Promise((i=>{t.addEventListener("change",(async()=>{if(t.files&&0!==t.files.length){this._uiManager.enableWaiting(!0);const e=await this._uiManager.imageManager.getFromFile(t.files[0]);this._reportTelemetry({action:"pdfjs.image.image_selected",data:{alt_text_modal:this._uiManager.useNewAltTextFlow}});this.#Fo(e)}else this.remove();i()}),{signal:e});t.addEventListener("cancel",(()=>{this.remove();i()}),{signal:e})})).finally((()=>this.#Oo()));t.click()}remove(){if(this.#Eo){this.#_o=null;this._uiManager.imageManager.deleteId(this.#Eo);this.#Do?.remove();this.#Do=null;if(this.#ko){clearTimeout(this.#ko);this.#ko=null}}super.remove()}rebuild(){if(this.parent){super.rebuild();if(null!==this.div){this.#Eo&&null===this.#Do&&this.#No();this.isAttachedToDOM||this.parent.add(this)}}else this.#Eo&&this.#No()}onceAdded(t){this._isDraggable=!0;t&&this.div.focus()}isEmpty(){return!(this.#So||this.#_o||this.#Co||this.#To||this.#Eo||this.#Po)}get toolbarButtons(){return[["altText",this.createAltText()]]}get isResizable(){return!0}render(){if(this.div)return this.div;let t,e;if(this._isCopy){t=this.x;e=this.y}super.render();this.div.hidden=!0;this.createAltText();this.#Po||(this.#_o?this.#Lo():this.#No());this._isCopy&&this._moveAfterPaste(t,e);this._uiManager.addShouldRescale(this);return this.div}setCanvas(t,e){const{id:i,bitmap:n}=this._uiManager.imageManager.getFromCanvas(t,e);e.remove();if(i&&this._uiManager.imageManager.isValidId(i)){this.#Eo=i;n&&(this.#_o=n);this.#Po=!1;this.#Lo()}}_onResized(){this.onScaleChanging()}onScaleChanging(){if(!this.parent)return;null!==this.#ko&&clearTimeout(this.#ko);this.#ko=setTimeout((()=>{this.#ko=null;this.#Bo()}),200)}#Lo(){const{div:t}=this;let{width:e,height:i}=this.#_o;const[n,s]=this.pageDimensions,r=.75;if(this.width){e=this.width*n;i=this.height*s}else if(e>r*n||i>r*s){const t=Math.min(r*n/e,r*s/i);e*=t;i*=t}const[a,o]=this.parentDimensions;this.setDims(e*a/n,i*o/s);this._uiManager.enableWaiting(!1);const l=this.#Do=document.createElement("canvas");l.setAttribute("role","img");this.addContainer(l);this.width=e/n;this.height=i/s;this._initialOptions?.isCentered?this.center():this.fixAndSetPosition();this._initialOptions=null;this._uiManager.useNewAltTextWhenAddingImage&&this._uiManager.useNewAltTextFlow&&!this.annotationElementId||(t.hidden=!1);this.#Bo();if(!this.#Ro){this.parent.addUndoableEditor(this);this.#Ro=!0}this._reportTelemetry({action:"inserted_image"});this.#Mo&&this.div.setAttribute("aria-description",this.#Mo);this.annotationElementId||this._uiManager.a11yAlert("pdfjs-editor-stamp-added-alert")}copyCanvas(t,e,i=!1){t||(t=224);const{width:n,height:s}=this.#_o,r=new OutputScale;let a=this.#_o,o=n,l=s,h=null;if(e){if(n>e||s>e){const t=Math.min(e/n,e/s);o=Math.floor(n*t);l=Math.floor(s*t)}h=document.createElement("canvas");const t=h.width=Math.ceil(o*r.sx),i=h.height=Math.ceil(l*r.sy);this.#Io||(a=this.#Uo(t,i));const c=h.getContext("2d");c.filter=this._uiManager.hcmFilter;let d="white",u="#cfcfd8";if("none"!==this._uiManager.hcmFilter)u="black";else if(window.matchMedia?.("(prefers-color-scheme: dark)").matches){d="#8f8f9d";u="#42414d"}const p=15,g=p*r.sx,f=p*r.sy,m=new OffscreenCanvas(2*g,2*f),b=m.getContext("2d");b.fillStyle=d;b.fillRect(0,0,2*g,2*f);b.fillStyle=u;b.fillRect(0,0,g,f);b.fillRect(g,f,g,f);c.fillStyle=c.createPattern(m,"repeat");c.fillRect(0,0,t,i);c.drawImage(a,0,0,a.width,a.height,0,0,t,i)}let c=null;if(i){let e,i;if(r.symmetric&&a.width<t&&a.height<t){e=a.width;i=a.height}else{a=this.#_o;if(n>t||s>t){const r=Math.min(t/n,t/s);e=Math.floor(n*r);i=Math.floor(s*r);this.#Io||(a=this.#Uo(e,i))}}const o=new OffscreenCanvas(e,i).getContext("2d",{willReadFrequently:!0});o.drawImage(a,0,0,a.width,a.height,0,0,e,i);c={width:e,height:i,data:o.getImageData(0,0,e,i).data}}return{canvas:h,width:o,height:l,imageData:c}}#Uo(t,e){const{width:i,height:n}=this.#_o;let s=i,r=n,a=this.#_o;for(;s>2*t||r>2*e;){const i=s,n=r;s>2*t&&(s=s>=16384?Math.floor(s/2)-1:Math.ceil(s/2));r>2*e&&(r=r>=16384?Math.floor(r/2)-1:Math.ceil(r/2));const o=new OffscreenCanvas(s,r);o.getContext("2d").drawImage(a,0,0,i,n,0,0,s,r);a=o.transferToImageBitmap()}return a}#Bo(){const[t,e]=this.parentDimensions,{width:i,height:n}=this,s=new OutputScale,r=Math.ceil(i*t*s.sx),a=Math.ceil(n*e*s.sy),o=this.#Do;if(!o||o.width===r&&o.height===a)return;o.width=r;o.height=a;const l=this.#Io?this.#_o:this.#Uo(r,a),h=o.getContext("2d");h.filter=this._uiManager.hcmFilter;h.drawImage(l,0,0,l.width,l.height,0,0,r,a)}#Ho(t){if(t){if(this.#Io){const t=this._uiManager.imageManager.getSvgUrl(this.#Eo);if(t)return t}const t=document.createElement("canvas");({width:t.width,height:t.height}=this.#_o);t.getContext("2d").drawImage(this.#_o,0,0);return t.toDataURL()}if(this.#Io){const[t,e]=this.pageDimensions,i=Math.round(this.width*t*PixelsPerInch.PDF_TO_CSS_UNITS),n=Math.round(this.height*e*PixelsPerInch.PDF_TO_CSS_UNITS),s=new OffscreenCanvas(i,n);s.getContext("2d").drawImage(this.#_o,0,0,this.#_o.width,this.#_o.height,0,0,i,n);return s.transferToImageBitmap()}return structuredClone(this.#_o)}static async deserialize(t,e,i){let n=null,s=!1;if(t instanceof StampAnnotationElement){const{data:{rect:r,rotation:a,id:o,structParent:l,popupRef:h,contentsObj:c},container:d,parent:{page:{pageNumber:u}},canvas:p}=t;let g,m;if(p){delete t.canvas;({id:g,bitmap:m}=i.imageManager.getFromCanvas(d.id,p));p.remove()}else{s=!0;t._hasNoCanvas=!0}const b=(await e._structTree.getAriaAttributes(`${W}${o}`))?.get("aria-label")||"";n=t={annotationType:f.STAMP,bitmapId:g,bitmap:m,pageIndex:u-1,rect:r.slice(0),rotation:a,annotationElementId:o,id:o,deleted:!1,accessibilityData:{decorative:!1,altText:b},isSvg:!1,structParent:l,popupRef:h,comment:c?.str||null}}const r=await super.deserialize(t,e,i),{rect:a,bitmap:o,bitmapUrl:l,bitmapId:h,isSvg:c,accessibilityData:d}=t;if(s){i.addMissingCanvas(t.id,r);r.#Po=!0}else if(h&&i.imageManager.isValidId(h)){r.#Eo=h;o&&(r.#_o=o)}else r.#Co=l;r.#Io=c;const[u,p]=r.pageDimensions;r.width=(a[2]-a[0])/u;r.height=(a[3]-a[1])/p;d&&(r.altTextData=d);r._initialData=n;t.comment&&r.setCommentData(t.comment);r.#Ro=!!n;return r}serialize(t=!1,e=null){if(this.isEmpty())return null;if(this.deleted)return this.serializeDeleted();const i={annotationType:f.STAMP,bitmapId:this.#Eo,pageIndex:this.pageIndex,rect:this.getRect(0,0),rotation:this.rotation,isSvg:this.#Io,structTreeParentId:this._structTreeParentId};this.addComment(i);if(t){i.bitmapUrl=this.#Ho(!0);i.accessibilityData=this.serializeAltText(!0);i.isCopy=!0;return i}const{decorative:n,altText:s}=this.serializeAltText(!1);!n&&s&&(i.accessibilityData={type:"Figure",alt:s});if(this.annotationElementId){const t=this.#Er(i);if(t.isSame)return null;t.isSameAltText?delete i.accessibilityData:i.accessibilityData.structParent=this._initialData.structParent??-1}i.id=this.annotationElementId;if(null===e)return i;e.stamps||=new Map;const r=this.#Io?(i.rect[2]-i.rect[0])*(i.rect[3]-i.rect[1]):null;if(e.stamps.has(this.#Eo)){if(this.#Io){const t=e.stamps.get(this.#Eo);if(r>t.area){t.area=r;t.serialized.bitmap.close();t.serialized.bitmap=this.#Ho(!1)}}}else{e.stamps.set(this.#Eo,{area:r,serialized:i});i.bitmap=this.#Ho(!1)}return i}#Er(t){const{pageIndex:e,accessibilityData:{altText:i}}=this._initialData,n=t.pageIndex===e,s=(t.accessibilityData?.alt||"")===i;return{isSame:!this.hasEditedComment&&!this._hasBeenMoved&&!this._hasBeenResized&&n&&s,isSameAltText:s}}renderAnnotationElement(t){const e={rect:this.getRect(0,0)};this.hasEditedComment&&(e.popup=this.comment);t.updateEdited(e);return null}}class AnnotationEditorLayer{#or;#zo=!1;#jo=null;#Go=null;#Wo=null;#Vo=new Map;#$o=!1;#qo=!1;#Xo=!1;#Ko=null;#Yo=null;#Qo=null;#Jo=null;#Zo=null;#tl=-1;#m;static _initialized=!1;static#G=new Map([FreeTextEditor,InkEditor,StampEditor,HighlightEditor,SignatureEditor].map((t=>[t._editorType,t])));constructor({uiManager:t,pageIndex:e,div:i,structTreeLayer:n,accessibilityManager:s,annotationLayer:r,drawLayer:a,textLayer:o,viewport:l,l10n:h}){const c=[...AnnotationEditorLayer.#G.values()];if(!AnnotationEditorLayer._initialized){AnnotationEditorLayer._initialized=!0;for(const e of c)e.initialize(h,t)}t.registerEditorTypes(c);this.#m=t;this.pageIndex=e;this.div=i;this.#or=s;this.#jo=r;this.viewport=l;this.#Qo=o;this.drawLayer=a;this._structTree=n;this.#m.addLayer(this)}get isEmpty(){return 0===this.#Vo.size}get isInvisible(){return this.isEmpty&&this.#m.getMode()===f.NONE}updateToolbar(t){this.#m.updateToolbar(t)}updateMode(t=this.#m.getMode()){this.#el();switch(t){case f.NONE:this.disableTextSelection();this.togglePointerEvents(!1);this.toggleAnnotationLayerPointerEvents(!0);this.disableClick();return;case f.INK:this.disableTextSelection();this.togglePointerEvents(!0);this.enableClick();break;case f.HIGHLIGHT:this.enableTextSelection();this.togglePointerEvents(!1);this.disableClick();break;default:this.disableTextSelection();this.togglePointerEvents(!0);this.enableClick()}this.toggleAnnotationLayerPointerEvents(!1);const{classList:e}=this.div;for(const i of AnnotationEditorLayer.#G.values())e.toggle(`${i._type}Editing`,t===i._editorType);this.div.hidden=!1}hasTextLayer(t){return t===this.#Qo?.div}setEditingState(t){this.#m.setEditingState(t)}addCommands(t){this.#m.addCommands(t)}cleanUndoStack(t){this.#m.cleanUndoStack(t)}toggleDrawing(t=!1){this.div.classList.toggle("drawing",!t)}togglePointerEvents(t=!1){this.div.classList.toggle("disabled",!t)}toggleAnnotationLayerPointerEvents(t=!1){this.#jo?.div.classList.toggle("disabled",!t)}async enable(){this.#Xo=!0;this.div.tabIndex=0;this.togglePointerEvents(!0);this.#Zo?.abort();this.#Zo=null;const t=new Set;for(const e of this.#Vo.values()){e.enableEditing();e.show(!0);if(e.annotationElementId){this.#m.removeChangedExistingAnnotation(e);t.add(e.annotationElementId)}}if(!this.#jo){this.#Xo=!1;return}const e=this.#jo.getEditableAnnotations();for(const i of e){i.hide();if(this.#m.isDeletedAnnotationElement(i.data.id))continue;if(t.has(i.data.id))continue;const e=await this.deserialize(i);if(e){this.addOrRebuild(e);e.enableEditing()}}this.#Xo=!1}disable(){this.#qo=!0;this.div.tabIndex=-1;this.togglePointerEvents(!1);if(this.#Qo&&!this.#Zo){this.#Zo=new AbortController;const t=this.#m.combinedSignal(this.#Zo);this.#Qo.div.addEventListener("pointerdown",(t=>{const{clientX:e,clientY:i,timeStamp:n}=t;if(n-this.#tl>500){this.#tl=n;return}this.#tl=-1;const{classList:s}=this.div;s.toggle("getElements",!0);const r=document.elementsFromPoint(e,i);s.toggle("getElements",!1);if(!this.div.contains(r[0]))return;let a;const o=new RegExp(`^${g}[0-9]+$`);for(const t of r)if(o.test(t.id)){a=t.id;break}if(!a)return;const l=this.#Vo.get(a);if(null===l?.annotationElementId){t.stopPropagation();t.preventDefault();l.dblclick()}}),{signal:t,capture:!0})}const t=new Map,e=new Map;for(const i of this.#Vo.values()){i.disableEditing();if(i.annotationElementId)if(null===i.serialize()){e.set(i.annotationElementId,i);this.getEditableAnnotation(i.annotationElementId)?.show();i.remove()}else t.set(i.annotationElementId,i)}if(this.#jo){const i=this.#jo.getEditableAnnotations();for(const n of i){const{id:i}=n.data;if(this.#m.isDeletedAnnotationElement(i)){n.updateEdited({deleted:!0});continue}let s=e.get(i);if(s){s.resetAnnotationElement(n);s.show(!1);n.show()}else{s=t.get(i);if(s){this.#m.addChangedExistingAnnotation(s);s.renderAnnotationElement(n)&&s.show(!1)}n.show()}}}this.#el();this.isEmpty&&(this.div.hidden=!0);const{classList:i}=this.div;for(const t of AnnotationEditorLayer.#G.values())i.remove(`${t._type}Editing`);this.disableTextSelection();this.toggleAnnotationLayerPointerEvents(!0);this.#qo=!1}getEditableAnnotation(t){return this.#jo?.getEditableAnnotation(t)||null}setActiveEditor(t){this.#m.getActive()!==t&&this.#m.setActiveEditor(t)}enableTextSelection(){this.div.tabIndex=-1;if(this.#Qo?.div&&!this.#Jo){this.#Jo=new AbortController;const t=this.#m.combinedSignal(this.#Jo);this.#Qo.div.addEventListener("pointerdown",this.#il.bind(this),{signal:t});this.#Qo.div.classList.add("highlighting")}}disableTextSelection(){this.div.tabIndex=0;if(this.#Qo?.div&&this.#Jo){this.#Jo.abort();this.#Jo=null;this.#Qo.div.classList.remove("highlighting")}}#il(t){this.#m.unselectAll();const{target:e}=t;if(e===this.#Qo.div||("img"===e.getAttribute("role")||e.classList.contains("endOfContent"))&&this.#Qo.div.contains(e)){const{isMac:e}=util_FeatureTest.platform;if(0!==t.button||t.ctrlKey&&e)return;this.#m.showAllEditors("highlight",!0,!0);this.#Qo.div.classList.add("free");this.toggleDrawing();HighlightEditor.startHighlighting(this,"ltr"===this.#m.direction,{target:this.#Qo.div,x:t.x,y:t.y});this.#Qo.div.addEventListener("pointerup",(()=>{this.#Qo.div.classList.remove("free");this.toggleDrawing(!0)}),{once:!0,signal:this.#m._signal});t.preventDefault()}}enableClick(){if(this.#Go)return;this.#Go=new AbortController;const t=this.#m.combinedSignal(this.#Go);this.div.addEventListener("pointerdown",this.pointerdown.bind(this),{signal:t});const e=this.pointerup.bind(this);this.div.addEventListener("pointerup",e,{signal:t});this.div.addEventListener("pointercancel",e,{signal:t})}disableClick(){this.#Go?.abort();this.#Go=null}attach(t){this.#Vo.set(t.id,t);const{annotationElementId:e}=t;e&&this.#m.isDeletedAnnotationElement(e)&&this.#m.removeDeletedAnnotationElement(t)}detach(t){this.#Vo.delete(t.id);this.#or?.removePointerInTextLayer(t.contentDiv);!this.#qo&&t.annotationElementId&&this.#m.addDeletedAnnotationElement(t)}remove(t){this.detach(t);this.#m.removeEditor(t);t.div.remove();t.isAttachedToDOM=!1}changeParent(t){if(t.parent!==this){if(t.parent&&t.annotationElementId){this.#m.addDeletedAnnotationElement(t.annotationElementId);AnnotationEditor.deleteAnnotationElement(t);t.annotationElementId=null}this.attach(t);t.parent?.detach(t);t.setParent(this);if(t.div&&t.isAttachedToDOM){t.div.remove();this.div.append(t.div)}}}add(t){if(t.parent!==this||!t.isAttachedToDOM){this.changeParent(t);this.#m.addEditor(t);this.attach(t);if(!t.isAttachedToDOM){const e=t.render();this.div.append(e);t.isAttachedToDOM=!0}t.fixAndSetPosition();t.onceAdded(!this.#Xo);this.#m.addToAnnotationStorage(t);t._reportTelemetry(t.telemetryInitialData)}}moveEditorInDOM(t){if(!t.isAttachedToDOM)return;const{activeElement:e}=document;if(t.div.contains(e)&&!this.#Wo){t._focusEventsAllowed=!1;this.#Wo=setTimeout((()=>{this.#Wo=null;if(t.div.contains(document.activeElement))t._focusEventsAllowed=!0;else{t.div.addEventListener("focusin",(()=>{t._focusEventsAllowed=!0}),{once:!0,signal:this.#m._signal});e.focus()}}),0)}t._structTreeParentId=this.#or?.moveElementInDOM(this.div,t.div,t.contentDiv,!0)}addOrRebuild(t){if(t.needsToBeRebuilt()){t.parent||=this;t.rebuild();t.show()}else this.add(t)}addUndoableEditor(t){this.addCommands({cmd:()=>t._uiManager.rebuild(t),undo:()=>{t.remove()},mustExec:!1})}getNextId(){return this.#m.getId()}get#nl(){return AnnotationEditorLayer.#G.get(this.#m.getMode())}combinedSignal(t){return this.#m.combinedSignal(t)}#sl(t){const e=this.#nl;return e?new e.prototype.constructor(t):null}canCreateNewEmptyEditor(){return this.#nl?.canCreateNewEmptyEditor()}async pasteEditor(t,e){this.updateToolbar(t);await this.#m.updateMode(t.mode);const{offsetX:i,offsetY:n}=this.#rl(),s=this.getNextId(),r=this.#sl({parent:this,id:s,x:i,y:n,uiManager:this.#m,isCentered:!0,...e});r&&this.add(r)}async deserialize(t){return await(AnnotationEditorLayer.#G.get(t.annotationType??t.annotationEditorType)?.deserialize(t,this,this.#m))||null}createAndAddNewEditor(t,e,i={}){const n=this.getNextId(),s=this.#sl({parent:this,id:n,x:t.offsetX,y:t.offsetY,uiManager:this.#m,isCentered:e,...i});s&&this.add(s);return s}#rl(){const{x:t,y:e,width:i,height:n}=this.div.getBoundingClientRect(),s=Math.max(0,t),r=Math.max(0,e),a=(s+Math.min(window.innerWidth,t+i))/2-t,o=(r+Math.min(window.innerHeight,e+n))/2-e,[l,h]=this.viewport.rotation%180==0?[a,o]:[o,a];return{offsetX:l,offsetY:h}}addNewEditor(t={}){this.createAndAddNewEditor(this.#rl(),!0,t)}setSelected(t){this.#m.setSelected(t)}toggleSelected(t){this.#m.toggleSelected(t)}unselect(t){this.#m.unselect(t)}pointerup(t){const{isMac:e}=util_FeatureTest.platform;if(0!==t.button||t.ctrlKey&&e)return;if(t.target!==this.div)return;if(!this.#$o)return;this.#$o=!1;if(this.#nl?.isDrawer&&this.#nl.supportMultipleDrawings)return;if(!this.#zo){this.#zo=!0;return}const i=this.#m.getMode();i!==f.STAMP&&i!==f.SIGNATURE?this.createAndAddNewEditor(t,!1):this.#m.unselectAll()}pointerdown(t){this.#m.getMode()===f.HIGHLIGHT&&this.enableTextSelection();if(this.#$o){this.#$o=!1;return}const{isMac:e}=util_FeatureTest.platform;if(0!==t.button||t.ctrlKey&&e)return;if(t.target!==this.div)return;this.#$o=!0;if(this.#nl?.isDrawer){this.startDrawingSession(t);return}const i=this.#m.getActive();this.#zo=!i||i.isEmpty()}startDrawingSession(t){this.div.focus({preventScroll:!0});if(this.#Ko){this.#nl.startDrawing(this,this.#m,!1,t);return}this.#m.setCurrentDrawingSession(this);this.#Ko=new AbortController;const e=this.#m.combinedSignal(this.#Ko);this.div.addEventListener("blur",(({relatedTarget:t})=>{if(t&&!this.div.contains(t)){this.#Yo=null;this.commitOrRemove()}}),{signal:e});this.#nl.startDrawing(this,this.#m,!1,t)}pause(t){if(t){const{activeElement:t}=document;this.div.contains(t)&&(this.#Yo=t)}else this.#Yo&&setTimeout((()=>{this.#Yo?.focus();this.#Yo=null}),0)}endDrawingSession(t=!1){if(!this.#Ko)return null;this.#m.setCurrentDrawingSession(null);this.#Ko.abort();this.#Ko=null;this.#Yo=null;return this.#nl.endDrawing(t)}findNewParent(t,e,i){const n=this.#m.findParent(e,i);if(null===n||n===this)return!1;n.changeParent(t);return!0}commitOrRemove(){if(this.#Ko){this.endDrawingSession();return!0}return!1}onScaleChanging(){this.#Ko&&this.#nl.onScaleChangingWhenDrawing(this)}destroy(){this.commitOrRemove();if(this.#m.getActive()?.parent===this){this.#m.commitOrRemove();this.#m.setActiveEditor(null)}if(this.#Wo){clearTimeout(this.#Wo);this.#Wo=null}for(const t of this.#Vo.values()){this.#or?.removePointerInTextLayer(t.contentDiv);t.setParent(null);t.isAttachedToDOM=!1;t.div.remove()}this.div=null;this.#Vo.clear();this.#m.removeLayer(this)}#el(){for(const t of this.#Vo.values())t.isEmpty()&&t.remove()}render({viewport:t}){this.viewport=t;setLayerDimensions(this.div,t);for(const t of this.#m.getEditors(this.pageIndex)){this.add(t);t.rebuild()}this.updateMode()}update({viewport:t}){this.#m.commitOrRemove();this.#el();const e=this.viewport.rotation,i=t.rotation;this.viewport=t;setLayerDimensions(this.div,{rotation:i});if(e!==i)for(const t of this.#Vo.values())t.rotate(i)}get pageDimensions(){const{pageWidth:t,pageHeight:e}=this.viewport.rawDims;return[t,e]}get scale(){return this.#m.viewParameters.realScale}}class DrawLayer{#Ns=null;#al=new Map;#ol=new Map;static#y=0;constructor({pageIndex:t}){this.pageIndex=t}setParent(t){if(this.#Ns){if(this.#Ns!==t){if(this.#al.size>0)for(const e of this.#al.values()){e.remove();t.append(e)}this.#Ns=t}}else this.#Ns=t}static get _svgFactory(){return shadow(this,"_svgFactory",new DOMSVGFactory)}static#ll(t,[e,i,n,s]){const{style:r}=t;r.top=100*i+"%";r.left=100*e+"%";r.width=100*n+"%";r.height=100*s+"%"}#hl(){const t=DrawLayer._svgFactory.create(1,1,!0);this.#Ns.append(t);t.setAttribute("aria-hidden",!0);return t}#cl(t,e){const i=DrawLayer._svgFactory.createElement("clipPath");t.append(i);const n=`clip_${e}`;i.setAttribute("id",n);i.setAttribute("clipPathUnits","objectBoundingBox");const s=DrawLayer._svgFactory.createElement("use");i.append(s);s.setAttribute("href",`#${e}`);s.classList.add("clip");return n}#dl(t,e){for(const[i,n]of Object.entries(e))null===n?t.removeAttribute(i):t.setAttribute(i,n)}draw(t,e=!1,i=!1){const n=DrawLayer.#y++,s=this.#hl(),r=DrawLayer._svgFactory.createElement("defs");s.append(r);const a=DrawLayer._svgFactory.createElement("path");r.append(a);const o=`path_p${this.pageIndex}_${n}`;a.setAttribute("id",o);a.setAttribute("vector-effect","non-scaling-stroke");e&&this.#ol.set(n,a);const l=i?this.#cl(r,o):null,h=DrawLayer._svgFactory.createElement("use");s.append(h);h.setAttribute("href",`#${o}`);this.updateProperties(s,t);this.#al.set(n,s);return{id:n,clipPathId:`url(#${l})`}}drawOutline(t,e){const i=DrawLayer.#y++,n=this.#hl(),s=DrawLayer._svgFactory.createElement("defs");n.append(s);const r=DrawLayer._svgFactory.createElement("path");s.append(r);const a=`path_p${this.pageIndex}_${i}`;r.setAttribute("id",a);r.setAttribute("vector-effect","non-scaling-stroke");let o;if(e){const t=DrawLayer._svgFactory.createElement("mask");s.append(t);o=`mask_p${this.pageIndex}_${i}`;t.setAttribute("id",o);t.setAttribute("maskUnits","objectBoundingBox");const e=DrawLayer._svgFactory.createElement("rect");t.append(e);e.setAttribute("width","1");e.setAttribute("height","1");e.setAttribute("fill","white");const n=DrawLayer._svgFactory.createElement("use");t.append(n);n.setAttribute("href",`#${a}`);n.setAttribute("stroke","none");n.setAttribute("fill","black");n.setAttribute("fill-rule","nonzero");n.classList.add("mask")}const l=DrawLayer._svgFactory.createElement("use");n.append(l);l.setAttribute("href",`#${a}`);o&&l.setAttribute("mask",`url(#${o})`);const h=l.cloneNode();n.append(h);l.classList.add("mainOutline");h.classList.add("secondaryOutline");this.updateProperties(n,t);this.#al.set(i,n);return i}finalizeDraw(t,e){this.#ol.delete(t);this.updateProperties(t,e)}updateProperties(t,e){if(!e)return;const{root:i,bbox:n,rootClass:s,path:r}=e,a="number"==typeof t?this.#al.get(t):t;if(a){i&&this.#dl(a,i);n&&DrawLayer.#ll(a,n);if(s){const{classList:t}=a;for(const[e,i]of Object.entries(s))t.toggle(e,i)}if(r){const t=a.firstChild.firstChild;this.#dl(t,r)}}}updateParent(t,e){if(e===this)return;const i=this.#al.get(t);if(i){e.#Ns.append(i);this.#al.delete(t);e.#al.set(t,i)}}remove(t){this.#ol.delete(t);if(null!==this.#Ns){this.#al.get(t).remove();this.#al.delete(t)}}destroy(){this.#Ns=null;for(const t of this.#al.values())t.remove();this.#al.clear();this.#ol.clear()}}globalThis._pdfjsTestingUtils={HighlightOutliner};globalThis.pdfjsLib={AbortException,AnnotationEditorLayer,AnnotationEditorParamsType:m,AnnotationEditorType:f,AnnotationEditorUIManager,AnnotationLayer,AnnotationMode:p,AnnotationType:S,build:Ct,ColorPicker,createValidAbsoluteUrl,DOMSVGFactory,DrawLayer,FeatureTest:util_FeatureTest,fetchData,getDocument,getFilenameFromUrl,getPdfFilenameFromUrl,getRGB,getUuid,getXfaPageViewport,GlobalWorkerOptions,ImageKind:E,InvalidPDFException,isDataScheme,isPdfFile,isValidExplicitDest:Q,MathClamp,noContextMenu,normalizeUnicode,OPS:I,OutputScale,PasswordResponses:B,PDFDataRangeTransport,PDFDateString,PDFWorker,PermissionFlag:b,PixelsPerInch,RenderingCancelledException,ResponseException,setLayerDimensions,shadow,SignatureExtractor,stopEvent,SupportedImageMimeTypes:$,TextLayer,TouchManager,updateUrlHash,Util,VerbosityLevel:k,version:St,XfaLayer};export{AbortException,AnnotationEditorLayer,m as AnnotationEditorParamsType,f as AnnotationEditorType,AnnotationEditorUIManager,AnnotationLayer,p as AnnotationMode,S as AnnotationType,ColorPicker,DOMSVGFactory,DrawLayer,util_FeatureTest as FeatureTest,GlobalWorkerOptions,E as ImageKind,InvalidPDFException,MathClamp,I as OPS,OutputScale,PDFDataRangeTransport,PDFDateString,PDFWorker,B as PasswordResponses,b as PermissionFlag,PixelsPerInch,RenderingCancelledException,ResponseException,SignatureExtractor,$ as SupportedImageMimeTypes,TextLayer,TouchManager,Util,k as VerbosityLevel,XfaLayer,Ct as build,createValidAbsoluteUrl,fetchData,getDocument,getFilenameFromUrl,getPdfFilenameFromUrl,getRGB,getUuid,getXfaPageViewport,isDataScheme,isPdfFile,Q as isValidExplicitDest,noContextMenu,normalizeUnicode,setLayerDimensions,shadow,stopEvent,updateUrlHash,St as version};