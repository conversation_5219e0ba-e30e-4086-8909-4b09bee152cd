<template>
  <el-dialog
    v-model="visible"
    :title="dialogTitle"
    width="90%"
    :before-close="handleClose"
    class="file-preview-dialog"
    destroy-on-close
  >
    <div class="preview-container" v-loading="loading">
      <!-- 图片预览 -->
      <div v-if="previewType === 'image'" class="image-preview">
        <div class="image-viewer">
          <img
            ref="previewImage"
            :src="imageUrl"
            :alt="file?.name"
            class="preview-image"
            :style="imageStyle"
            @load="handleImageLoad"
            @error="handleImageError"
          />
        </div>

        <!-- 图片控制工具栏 -->
        <div class="image-controls">
          <el-button-group>
            <el-button @click="zoomOut" :disabled="scale <= 0.1">
              <el-icon><ZoomOut /></el-icon>
            </el-button>
            <el-button @click="resetZoom">
              {{ Math.round(scale * 100) }}%
            </el-button>
            <el-button @click="zoomIn" :disabled="scale >= 3">
              <el-icon><ZoomIn /></el-icon>
            </el-button>
          </el-button-group>
        </div>
      </div>

      <!-- PDF预览 -->
      <div v-else-if="previewType === 'pdf'" class="pdf-preview">
        <div class="pdf-toolbar">
          <div class="pdf-info">
            <span>{{ file?.name }}</span>
            <span v-if="pdfPageCount > 0" class="page-info">
              第 {{ currentPage }} 页，共 {{ pdfPageCount }} 页
            </span>
          </div>
          <div class="pdf-controls">
            <el-button-group>
              <el-button
                :disabled="currentPage <= 1"
                @click="previousPage"
                size="small"
              >
                上一页
              </el-button>
              <el-button
                :disabled="currentPage >= pdfPageCount"
                @click="nextPage"
                size="small"
              >
                下一页
              </el-button>
            </el-button-group>
            <el-button-group style="margin-left: 10px;">
              <el-button @click="zoomOutPdf" size="small" :disabled="pdfScale <= 0.5">
                <el-icon><ZoomOut /></el-icon>
              </el-button>
              <el-button @click="resetPdfZoom" size="small">
                {{ Math.round(pdfScale * 100) }}%
              </el-button>
              <el-button @click="zoomInPdf" size="small" :disabled="pdfScale >= 3">
                <el-icon><ZoomIn /></el-icon>
              </el-button>
            </el-button-group>
          </div>
        </div>
        <div class="pdf-viewer" ref="pdfViewerContainer">
          <canvas
            ref="pdfCanvas"
            class="pdf-canvas"
            :style="pdfCanvasStyle"
          ></canvas>
        </div>
      </div>

      <!-- Word文档预览 -->
      <div v-else-if="previewType === 'word'" class="word-preview">
        <div class="word-notice">
          <el-icon size="48"><Document /></el-icon>
          <h3>Word文档</h3>
          <p>{{ file?.name }}</p>
          <p class="notice-text">Word文档暂不支持在线预览</p>
        </div>
      </div>

      <!-- 不支持预览的文件类型 -->
      <div v-else class="unsupported-preview">
        <div class="unsupported-notice">
          <el-icon size="48"><Document /></el-icon>
          <h3>文件预览</h3>
          <p>{{ file?.name }}</p>
          <p class="notice-text">此文件类型暂不支持预览</p>
        </div>
      </div>

      <!-- 错误状态 -->
      <div v-if="error" class="error-state">
        <el-icon size="48" color="#f56c6c"><Warning /></el-icon>
        <h3>预览失败</h3>
        <p>{{ error }}</p>
        <el-button @click="retryPreview">重试</el-button>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Document, Warning, ZoomIn, ZoomOut } from '@element-plus/icons-vue'
import { filesApi } from '../services/api'
import { loadPdfDocument, renderPdfPage } from '../utils/pdfUtils'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  file: {
    type: Object,
    default: null
  },
  previewMode: {
    type: String,
    default: 'original', // 'original' 或 'watermark'
    validator: (value) => ['original', 'watermark'].includes(value)
  }
})

const emit = defineEmits(['update:modelValue'])

// 响应式数据
const loading = ref(false)
const error = ref('')
const scale = ref(1)
const previewImage = ref(null)

// PDF相关数据
const pdfCanvas = ref(null)
const pdfViewerContainer = ref(null)
const pdfDoc = ref(null)
const currentPage = ref(1)
const pdfPageCount = ref(0)
const pdfScale = ref(1)
const renderTask = ref(null)

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const dialogTitle = computed(() => {
  const modeText = props.previewMode === 'watermark' ? '水印预览' : '原件预览'
  return props.file ? `${modeText} - ${props.file.name}` : '文件预览'
})

const previewType = computed(() => {
  if (!props.file) return null

  const fileType = props.file.fileType?.toLowerCase()
  const mimeType = props.file.mimeType?.toLowerCase()

  if (fileType === 'image' || mimeType?.startsWith('image/')) {
    return 'image'
  } else if (fileType === 'pdf' || mimeType === 'application/pdf') {
    return 'pdf'
  } else if (fileType === 'word' || mimeType?.includes('word') || mimeType?.includes('document')) {
    return 'word'
  }

  return 'unsupported'
})

const imageUrl = computed(() => {
  if (!props.file || previewType.value !== 'image') return null

  // 根据预览模式返回不同的URL
  if (props.previewMode === 'watermark') {
    // 这里应该返回带水印的预览URL，暂时使用原图
    return filesApi.getPreviewUrl(props.file.id)
  } else {
    // 原件预览
    return filesApi.getPreviewUrl(props.file.id)
  }
})

const imageStyle = computed(() => {
  return {
    transform: `scale(${scale.value})`,
    transition: 'transform 0.3s ease'
  }
})

const pdfCanvasStyle = computed(() => {
  return {
    transform: `scale(${pdfScale.value})`,
    transformOrigin: 'top left',
    transition: 'transform 0.3s ease'
  }
})

// 方法
const handleImageLoad = () => {
  loading.value = false
}

const handleImageError = () => {
  loading.value = false
  error.value = '图片加载失败'
}

const zoomIn = () => {
  if (scale.value < 3) {
    scale.value = Math.min(3, scale.value + 0.2)
  }
}

const zoomOut = () => {
  if (scale.value > 0.1) {
    scale.value = Math.max(0.1, scale.value - 0.2)
  }
}

const resetZoom = () => {
  scale.value = 1
}

// PDF相关方法
const loadPdf = async () => {
  if (!props.file || previewType.value !== 'pdf') return

  try {
    loading.value = true
    error.value = ''

    const url = filesApi.getPreviewUrl(props.file.id)

    // 取消之前的渲染任务
    if (renderTask.value) {
      renderTask.value.cancel()
      renderTask.value = null
    }

    // 使用工具函数加载PDF文档
    pdfDoc.value = await loadPdfDocument(url)
    pdfPageCount.value = pdfDoc.value.numPages
    currentPage.value = 1

    // 渲染第一页
    await renderPage(1)

    loading.value = false
  } catch (err) {
    console.error('PDF加载失败:', err)
    error.value = err.message || 'PDF文档加载失败'
    loading.value = false
  }
}

const renderPage = async (pageNum) => {
  if (!pdfDoc.value || !pdfCanvas.value) return

  try {
    // 取消之前的渲染任务
    if (renderTask.value) {
      renderTask.value.cancel()
      renderTask.value = null
    }

    // 使用工具函数渲染页面
    renderTask.value = await renderPdfPage(pdfDoc.value, pageNum, pdfCanvas.value, 1.5)
    renderTask.value = null

  } catch (err) {
    if (err.name !== 'RenderingCancelledException') {
      console.error('PDF页面渲染失败:', err)
      error.value = err.message || 'PDF页面渲染失败'
    }
  }
}

const previousPage = async () => {
  if (currentPage.value > 1) {
    currentPage.value--
    await renderPage(currentPage.value)
  }
}

const nextPage = async () => {
  if (currentPage.value < pdfPageCount.value) {
    currentPage.value++
    await renderPage(currentPage.value)
  }
}

const zoomInPdf = () => {
  if (pdfScale.value < 3) {
    pdfScale.value = Math.min(3, pdfScale.value + 0.2)
  }
}

const zoomOutPdf = () => {
  if (pdfScale.value > 0.5) {
    pdfScale.value = Math.max(0.5, pdfScale.value - 0.2)
  }
}

const resetPdfZoom = () => {
  pdfScale.value = 1
}

const retryPreview = () => {
  error.value = ''
  loading.value = true

  nextTick(() => {
    if (previewImage.value) {
      previewImage.value.src = imageUrl.value
    }
  })
}

const handleClose = () => {
  visible.value = false
  error.value = ''
  scale.value = 1

  // 清理PDF资源
  if (renderTask.value) {
    renderTask.value.cancel()
    renderTask.value = null
  }
  if (pdfDoc.value) {
    pdfDoc.value.destroy()
    pdfDoc.value = null
  }
  currentPage.value = 1
  pdfPageCount.value = 0
  pdfScale.value = 1
}

// 监听文件变化
watch(() => props.file, async (newFile) => {
  if (newFile && visible.value) {
    error.value = ''
    scale.value = 1
    pdfScale.value = 1

    // 根据文件类型处理
    if (previewType.value === 'image') {
      loading.value = true
    } else if (previewType.value === 'pdf') {
      await loadPdf()
    } else {
      loading.value = false
    }
  }
}, { immediate: true })

// 监听对话框显示状态
watch(visible, async (newVisible) => {
  if (newVisible && props.file) {
    error.value = ''
    scale.value = 1
    pdfScale.value = 1

    // 根据文件类型处理
    if (previewType.value === 'image') {
      loading.value = true
    } else if (previewType.value === 'pdf') {
      await loadPdf()
    } else {
      loading.value = false
    }
  } else if (!newVisible) {
    // 对话框关闭时重置状态
    error.value = ''
    scale.value = 1
    loading.value = false

    // 清理PDF资源
    if (renderTask.value) {
      renderTask.value.cancel()
      renderTask.value = null
    }
    if (pdfDoc.value) {
      pdfDoc.value.destroy()
      pdfDoc.value = null
    }
    currentPage.value = 1
    pdfPageCount.value = 0
    pdfScale.value = 1
  }
})

// 监听预览类型变化
watch(previewType, async (newType) => {
  if (newType && visible.value) {
    // 根据文件类型处理
    if (newType === 'image') {
      loading.value = true
    } else if (newType === 'pdf') {
      await loadPdf()
    } else {
      loading.value = false
    }
  }
})
</script>

<style scoped>
.file-preview-dialog {
  --el-dialog-margin-top: 5vh;
}

.preview-container {
  min-height: 500px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.image-preview {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.image-viewer {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  max-height: 60vh;
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  background: #f5f7fa;
}

.preview-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  cursor: grab;
  user-select: none;
}

.preview-image:active {
  cursor: grabbing;
}

.image-controls {
  display: flex;
  justify-content: center;
  padding: 8px;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 6px;
}

.pdf-preview,
.word-preview,
.unsupported-preview {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pdf-notice,
.word-notice,
.unsupported-notice {
  text-align: center;
  padding: 40px;
}

.pdf-notice h3,
.word-notice h3,
.unsupported-notice h3 {
  margin: 16px 0 8px;
  color: #303133;
}

.pdf-notice p,
.word-notice p,
.unsupported-notice p {
  margin: 8px 0;
  color: #606266;
}

.notice-text {
  color: #909399 !important;
  font-size: 14px;
}

.error-state {
  text-align: center;
  padding: 40px;
}

.error-state h3 {
  margin: 16px 0 8px;
  color: #f56c6c;
}

.error-state p {
  margin: 8px 0 16px;
  color: #606266;
}

/* PDF预览样式 */
.pdf-preview {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.pdf-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #e4e7ed;
  margin-bottom: 20px;
}

.pdf-info {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.pdf-info span:first-child {
  font-weight: 500;
  color: #303133;
}

.page-info {
  font-size: 12px;
  color: #909399;
}

.pdf-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

.pdf-viewer {
  flex: 1;
  overflow: auto;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  padding: 20px;
  background-color: #f5f5f5;
}

.pdf-canvas {
  border: 1px solid #dcdfe6;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  background-color: white;
  cursor: grab;
}

.pdf-canvas:active {
  cursor: grabbing;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .file-preview-dialog {
    --el-dialog-width: 95%;
    --el-dialog-margin-top: 2vh;
  }

  .preview-container {
    min-height: 400px;
  }

  .image-viewer {
    max-height: 50vh;
  }
}
</style>
