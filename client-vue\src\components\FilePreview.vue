<template>
  <el-dialog
    v-model="visible"
    :title="dialogTitle"
    width="90%"
    :before-close="handleClose"
    class="file-preview-dialog"
    destroy-on-close
  >
    <div class="preview-container" v-loading="loading">
      <!-- 图片预览 -->
      <div v-if="previewType === 'image'" class="image-preview">
        <div class="image-viewer">
          <img
            ref="previewImage"
            :src="imageUrl"
            :alt="file?.name"
            class="preview-image"
            :style="imageStyle"
            @load="handleImageLoad"
            @error="handleImageError"
          />
        </div>

        <!-- 图片控制工具栏 -->
        <div class="image-controls">
          <el-button-group>
            <el-button @click="zoomOut" :disabled="scale <= 0.1">
              <el-icon><ZoomOut /></el-icon>
            </el-button>
            <el-button @click="resetZoom">
              {{ Math.round(scale * 100) }}%
            </el-button>
            <el-button @click="zoomIn" :disabled="scale >= 3">
              <el-icon><ZoomIn /></el-icon>
            </el-button>
          </el-button-group>
        </div>
      </div>

      <!-- PDF预览 -->
      <div v-else-if="previewType === 'pdf'" class="pdf-preview">
        <div class="pdf-notice">
          <el-icon size="48"><Document /></el-icon>
          <h3>PDF文档预览</h3>
          <p>{{ file?.name }}</p>
          <p class="notice-text">
            {{ previewMode === 'watermark' ? '水印预览：' : '原件预览：' }}
            点击下方按钮在新窗口中查看PDF文档
          </p>
          <el-button type="primary" @click="openInNewWindow">
            <el-icon><Document /></el-icon>
            在新窗口中打开
          </el-button>
        </div>
      </div>

      <!-- Word文档预览 -->
      <div v-else-if="previewType === 'word'" class="word-preview">
        <div class="word-notice">
          <el-icon size="48"><Document /></el-icon>
          <h3>Word文档</h3>
          <p>{{ file?.name }}</p>
          <p class="notice-text">Word文档暂不支持在线预览</p>
        </div>
      </div>

      <!-- 不支持预览的文件类型 -->
      <div v-else class="unsupported-preview">
        <div class="unsupported-notice">
          <el-icon size="48"><Document /></el-icon>
          <h3>文件预览</h3>
          <p>{{ file?.name }}</p>
          <p class="notice-text">此文件类型暂不支持预览</p>
        </div>
      </div>

      <!-- 错误状态 -->
      <div v-if="error" class="error-state">
        <el-icon size="48" color="#f56c6c"><Warning /></el-icon>
        <h3>预览失败</h3>
        <p>{{ error }}</p>
        <el-button @click="retryPreview">重试</el-button>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Document, Warning, ZoomIn, ZoomOut } from '@element-plus/icons-vue'
import { filesApi } from '../services/api'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  file: {
    type: Object,
    default: null
  },
  previewMode: {
    type: String,
    default: 'original', // 'original' 或 'watermark'
    validator: (value) => ['original', 'watermark'].includes(value)
  }
})

const emit = defineEmits(['update:modelValue'])

// 响应式数据
const loading = ref(false)
const error = ref('')
const scale = ref(1)
const previewImage = ref(null)

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const dialogTitle = computed(() => {
  const modeText = props.previewMode === 'watermark' ? '水印预览' : '原件预览'
  return props.file ? `${modeText} - ${props.file.name}` : '文件预览'
})

const previewType = computed(() => {
  if (!props.file) return null

  const fileType = props.file.fileType?.toLowerCase()
  const mimeType = props.file.mimeType?.toLowerCase()

  if (fileType === 'image' || mimeType?.startsWith('image/')) {
    return 'image'
  } else if (fileType === 'pdf' || mimeType === 'application/pdf') {
    return 'pdf'
  } else if (fileType === 'word' || mimeType?.includes('word') || mimeType?.includes('document')) {
    return 'word'
  }

  return 'unsupported'
})

const imageUrl = computed(() => {
  if (!props.file || previewType.value !== 'image') return null

  // 根据预览模式返回不同的URL
  if (props.previewMode === 'watermark') {
    // 这里应该返回带水印的预览URL，暂时使用原图
    return filesApi.getPreviewUrl(props.file.id)
  } else {
    // 原件预览
    return filesApi.getPreviewUrl(props.file.id)
  }
})

const imageStyle = computed(() => {
  return {
    transform: `scale(${scale.value})`,
    transition: 'transform 0.3s ease'
  }
})

// 方法
const handleImageLoad = () => {
  loading.value = false
}

const handleImageError = () => {
  loading.value = false
  error.value = '图片加载失败'
}

const zoomIn = () => {
  if (scale.value < 3) {
    scale.value = Math.min(3, scale.value + 0.2)
  }
}

const zoomOut = () => {
  if (scale.value > 0.1) {
    scale.value = Math.max(0.1, scale.value - 0.2)
  }
}

const resetZoom = () => {
  scale.value = 1
}

const openInNewWindow = () => {
  if (props.file && previewType.value === 'pdf') {
    const url = filesApi.getPreviewUrl(props.file.id)
    window.open(url, '_blank')
  }
}

const retryPreview = () => {
  error.value = ''
  loading.value = true

  nextTick(() => {
    if (previewImage.value) {
      previewImage.value.src = imageUrl.value
    }
  })
}

const handleClose = () => {
  visible.value = false
  error.value = ''
  scale.value = 1
}

// 监听文件变化
watch(() => props.file, (newFile) => {
  if (newFile && visible.value) {
    error.value = ''
    scale.value = 1
    // 只有图片类型才需要loading状态
    if (previewType.value === 'image') {
      loading.value = true
    } else {
      loading.value = false
    }
  }
}, { immediate: true })

// 监听对话框显示状态
watch(visible, (newVisible) => {
  if (newVisible && props.file) {
    error.value = ''
    scale.value = 1
    // 只有图片类型才需要loading状态
    if (previewType.value === 'image') {
      loading.value = true
    } else {
      loading.value = false
    }
  } else if (!newVisible) {
    // 对话框关闭时重置状态
    error.value = ''
    scale.value = 1
    loading.value = false
  }
})

// 监听预览类型变化
watch(previewType, (newType) => {
  if (newType && visible.value) {
    // 只有图片类型才需要loading状态
    if (newType === 'image') {
      loading.value = true
    } else {
      loading.value = false
    }
  }
})
</script>

<style scoped>
.file-preview-dialog {
  --el-dialog-margin-top: 5vh;
}

.preview-container {
  min-height: 500px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.image-preview {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.image-viewer {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  max-height: 60vh;
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  background: #f5f7fa;
}

.preview-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  cursor: grab;
  user-select: none;
}

.preview-image:active {
  cursor: grabbing;
}

.image-controls {
  display: flex;
  justify-content: center;
  padding: 8px;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 6px;
}

.pdf-preview,
.word-preview,
.unsupported-preview {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pdf-notice,
.word-notice,
.unsupported-notice {
  text-align: center;
  padding: 40px;
}

.pdf-notice h3,
.word-notice h3,
.unsupported-notice h3 {
  margin: 16px 0 8px;
  color: #303133;
}

.pdf-notice p,
.word-notice p,
.unsupported-notice p {
  margin: 8px 0;
  color: #606266;
}

.notice-text {
  color: #909399 !important;
  font-size: 14px;
}

.error-state {
  text-align: center;
  padding: 40px;
}

.error-state h3 {
  margin: 16px 0 8px;
  color: #f56c6c;
}

.error-state p {
  margin: 8px 0 16px;
  color: #606266;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .file-preview-dialog {
    --el-dialog-width: 95%;
    --el-dialog-margin-top: 2vh;
  }

  .preview-container {
    min-height: 400px;
  }

  .image-viewer {
    max-height: 50vh;
  }
}
</style>
